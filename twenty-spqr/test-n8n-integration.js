#!/usr/bin/env node

/**
 * N8n Integration Test Script
 * Quick test to verify the n8n integration is working
 */

const fetch = require('node-fetch');

const config = {
  twentyServerUrl: process.env.TWENTY_SERVER_URL || 'http://localhost:3000',
  n8nUrl: process.env.N8N_BASE_URL || 'http://localhost:5678',
  n8nApiKey: process.env.N8N_API_KEY || ''
};

async function testN8nConnection() {
  console.log('🔗 Testing N8n connection...');
  
  try {
    const response = await fetch(`${config.n8nUrl}/api/v1/workflows`, {
      headers: {
        'X-N8N-API-KEY': config.n8nApiKey,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ N8n connection successful! Found ${data.data?.length || 0} workflows`);
      return true;
    } else {
      console.log(`❌ N8n connection failed: ${response.statusText}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ N8n connection error: ${error.message}`);
    return false;
  }
}

async function testTwentyServerN8nEndpoints() {
  console.log('🔗 Testing Twenty server n8n endpoints...');
  
  try {
    // Test status endpoint
    const statusResponse = await fetch(`${config.twentyServerUrl}/webhooks/n8n/status`);
    if (statusResponse.ok) {
      const status = await statusResponse.json();
      console.log('✅ N8n status endpoint working');
      console.log('📊 Status:', JSON.stringify(status, null, 2));
    } else {
      console.log(`❌ Status endpoint failed: ${statusResponse.statusText}`);
    }

    // Test workflows endpoint
    const workflowsResponse = await fetch(`${config.twentyServerUrl}/webhooks/n8n/workflows`);
    if (workflowsResponse.ok) {
      const workflows = await workflowsResponse.json();
      console.log(`✅ Workflows endpoint working! Found ${workflows.length} workflows`);
      
      if (workflows.length > 0) {
        console.log('📋 Sample workflow:', JSON.stringify(workflows[0], null, 2));
      }
    } else {
      console.log(`❌ Workflows endpoint failed: ${workflowsResponse.statusText}`);
    }

    // Test credential mappings endpoint
    const credentialsResponse = await fetch(`${config.twentyServerUrl}/webhooks/n8n/credentials/mappings`);
    if (credentialsResponse.ok) {
      const mappings = await credentialsResponse.json();
      console.log(`✅ Credential mappings endpoint working! Found ${mappings.length} mappings`);
    } else {
      console.log(`❌ Credential mappings endpoint failed: ${credentialsResponse.statusText}`);
    }

  } catch (error) {
    console.log(`❌ Twenty server test error: ${error.message}`);
  }
}

async function testWebhookCallback() {
  console.log('🔗 Testing webhook callback...');
  
  try {
    const testPayload = {
      executionId: 'test-123',
      data: {
        processed: 5,
        created: 3,
        test: true
      },
      mcpContext: {
        sessionId: 'test-session',
        userId: 'test-user',
        workspaceId: 'test-workspace'
      }
    };

    const response = await fetch(`${config.twentyServerUrl}/webhooks/n8n/test-workflow/callback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testPayload)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Webhook callback test successful');
      console.log('📨 Result:', JSON.stringify(result, null, 2));
    } else {
      console.log(`❌ Webhook callback test failed: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`❌ Webhook callback test error: ${error.message}`);
  }
}

async function testCredentialSync() {
  console.log('🔗 Testing credential sync...');
  
  try {
    const response = await fetch(`${config.twentyServerUrl}/webhooks/n8n/credentials/sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Credential sync test successful');
      console.log('🔄 Sync results:', JSON.stringify(result, null, 2));
    } else {
      console.log(`❌ Credential sync test failed: ${response.statusText}`);
    }
  } catch (error) {
    console.log(`❌ Credential sync test error: ${error.message}`);
  }
}

async function runTests() {
  console.log('🧪 Starting N8n Integration Tests...\n');
  
  console.log('Configuration:');
  console.log(`  Twenty Server: ${config.twentyServerUrl}`);
  console.log(`  N8n URL: ${config.n8nUrl}`);
  console.log(`  N8n API Key: ${config.n8nApiKey ? '***configured***' : 'NOT SET'}\n`);

  // Test 1: N8n connection
  const n8nConnected = await testN8nConnection();
  console.log('');

  // Test 2: Twenty server endpoints
  await testTwentyServerN8nEndpoints();
  console.log('');

  // Test 3: Webhook callback
  await testWebhookCallback();
  console.log('');

  // Test 4: Credential sync
  await testCredentialSync();
  console.log('');

  console.log('🏁 Tests completed!');
  
  if (!n8nConnected) {
    console.log('\n⚠️  N8n is not accessible. Make sure:');
    console.log('   1. N8n is running (docker-compose up -d)');
    console.log('   2. N8N_BASE_URL is correct');
    console.log('   3. N8N_API_KEY is set and valid');
  }

  console.log('\n📚 Next steps:');
  console.log('   1. Set up n8n instance if not running');
  console.log('   2. Create example workflows in n8n');
  console.log('   3. Configure credential mappings');
  console.log('   4. Test end-to-end workflow execution');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testN8nConnection,
  testTwentyServerN8nEndpoints,
  testWebhookCallback,
  testCredentialSync,
  runTests
};
