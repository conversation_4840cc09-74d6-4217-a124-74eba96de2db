/**
 * N8n Integration Example
 * Demonstrates how to use the n8n integration in the SPQR-MCP system
 */

import { MCPCoordinatorService } from '../services/mcp-coordinator.service';
import { N8nIntegrationService } from '../services/n8n-integration.service';
import { N8nCredentialBridgeService } from '../services/n8n-credential-bridge.service';

export class N8nIntegrationExample {
  constructor(
    private readonly mcpCoordinator: MCPCoordinatorService,
    private readonly n8nIntegration: N8nIntegrationService,
    private readonly credentialBridge: N8nCredentialBridgeService,
  ) {}

  /**
   * Example 1: Execute Gmail workflow via n8n
   */
  async executeGmailWorkflow(): Promise<void> {
    console.log('📧 Executing Gmail workflow via n8n...');

    // Get context for the session
    const context = await this.mcpCoordinator.getContext(
      'example-session-1',
      'user-123',
      'workspace-456'
    );

    // Find Gmail workflows
    const gmailWorkflows = this.n8nIntegration.getWorkflowsByService('gmail');
    console.log(`Found ${gmailWorkflows.length} Gmail workflows`);

    if (gmailWorkflows.length > 0) {
      const workflow = gmailWorkflows[0];
      
      // Execute the workflow
      const result = await this.mcpCoordinator.executeCapability(
        context.sessionId,
        workflow.id,
        {
          action: 'process_unread_emails',
          filters: {
            labelIds: ['INBOX'],
            maxResults: 10
          },
          extractTasks: true,
          createTwentyRecords: true
        }
      );

      console.log('📧 Gmail workflow result:', result);
    }
  }

  /**
   * Example 2: Set up credential mapping
   */
  async setupCredentialMapping(): Promise<void> {
    console.log('🔐 Setting up credential mapping...');

    // Create Gmail credential mapping
    const gmailMapping = await this.credentialBridge.createCredentialMapping(
      'gmail',
      'vault-gmail-oauth-key',
      'gmailOAuth2Api',
      'supabase-to-n8n'
    );

    console.log('🔐 Created Gmail credential mapping:', gmailMapping);

    // Create Slack credential mapping
    const slackMapping = await this.credentialBridge.createCredentialMapping(
      'slack',
      'vault-slack-token-key',
      'slackApi',
      'bidirectional'
    );

    console.log('🔐 Created Slack credential mapping:', slackMapping);

    // Sync all credentials
    const syncResults = await this.credentialBridge.syncAllCredentials();
    console.log('🔄 Credential sync results:', syncResults);
  }

  /**
   * Example 3: Hybrid workflow - Agent + n8n
   */
  async executeHybridWorkflow(): Promise<void> {
    console.log('🤖 Executing hybrid workflow...');

    const context = await this.mcpCoordinator.getContext(
      'hybrid-session',
      'user-123',
      'workspace-456'
    );

    // Step 1: Use internal agent for analysis
    const analysisResult = await this.mcpCoordinator.executeCapability(
      context.sessionId,
      'customer-analysis-agent',
      {
        customerId: 'customer-123',
        analysisType: 'churn-risk'
      }
    );

    console.log('📊 Analysis result:', analysisResult);

    // Step 2: If high risk, trigger n8n workflow for intervention
    if (analysisResult?.churnRisk === 'high') {
      const interventionWorkflows = this.n8nIntegration.getWorkflowsByService('slack');
      
      if (interventionWorkflows.length > 0) {
        const result = await this.mcpCoordinator.executeCapability(
          context.sessionId,
          interventionWorkflows[0].id,
          {
            customerId: 'customer-123',
            riskLevel: analysisResult.churnRisk,
            riskFactors: analysisResult.riskFactors,
            interventionType: 'proactive-outreach'
          }
        );

        console.log('🚨 Intervention triggered:', result);
      }
    }
  }

  /**
   * Example 4: Dynamic workflow discovery
   */
  async discoverWorkflows(): Promise<void> {
    console.log('🔍 Discovering available workflows...');

    // Get all n8n workflows
    const allWorkflows = this.n8nIntegration.getAllWorkflows();
    console.log(`📋 Total workflows: ${allWorkflows.length}`);

    // Group by service
    const workflowsByService = allWorkflows.reduce((acc, workflow) => {
      for (const service of workflow.metadata.externalServices) {
        if (!acc[service]) acc[service] = [];
        acc[service].push(workflow);
      }
      return acc;
    }, {} as Record<string, any[]>);

    console.log('📊 Workflows by service:');
    for (const [service, workflows] of Object.entries(workflowsByService)) {
      console.log(`  ${service}: ${workflows.length} workflows`);
    }

    // Find optimal workflow for a specific service
    const slackWorkflows = workflowsByService.slack || [];
    if (slackWorkflows.length > 0) {
      const optimalWorkflow = slackWorkflows
        .sort((a, b) => a.metadata.estimatedDuration - b.metadata.estimatedDuration)[0];
      
      console.log('⚡ Optimal Slack workflow:', optimalWorkflow.name);
    }
  }

  /**
   * Example 5: Monitor system status
   */
  async monitorSystemStatus(): Promise<void> {
    console.log('📊 Monitoring system status...');

    // Get MCP system status
    const mcpStatus = this.mcpCoordinator.getStatus();
    console.log('🎯 MCP Status:', mcpStatus);

    // Get n8n integration status
    const n8nStatus = this.mcpCoordinator.getN8nStatus();
    console.log('🔗 N8n Status:', n8nStatus);

    // Get credential bridge status
    const credentialStatus = this.credentialBridge.getStatus();
    console.log('🔐 Credential Bridge Status:', credentialStatus);
  }

  /**
   * Example 6: Handle webhook callback simulation
   */
  async simulateWebhookCallback(): Promise<void> {
    console.log('🔗 Simulating webhook callback...');

    const webhookPayload = {
      workflowId: 'n8n-gmail-processor',
      executionId: 'exec-12345',
      data: {
        processed: 5,
        tasksCreated: 3,
        errors: 0
      },
      mcpContext: {
        sessionId: 'webhook-session',
        userId: 'user-123',
        workspaceId: 'workspace-456'
      }
    };

    await this.mcpCoordinator.handleN8nWebhook('n8n-gmail-processor', webhookPayload);
    console.log('🔗 Webhook processed successfully');
  }

  /**
   * Run all examples
   */
  async runAllExamples(): Promise<void> {
    console.log('🎬 Running N8n Integration Examples...\n');

    try {
      await this.setupCredentialMapping();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.discoverWorkflows();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.executeGmailWorkflow();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.executeHybridWorkflow();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.simulateWebhookCallback();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.monitorSystemStatus();
      console.log('\n' + '='.repeat(50) + '\n');

      console.log('✅ All examples completed successfully!');
    } catch (error) {
      console.error('❌ Example execution failed:', error);
    }
  }
}

/**
 * Usage example:
 * 
 * const example = new N8nIntegrationExample(
 *   mcpCoordinatorService,
 *   n8nIntegrationService,
 *   credentialBridgeService
 * );
 * 
 * await example.runAllExamples();
 */
