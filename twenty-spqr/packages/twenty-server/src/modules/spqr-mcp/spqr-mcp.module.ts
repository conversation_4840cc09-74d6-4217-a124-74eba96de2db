import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CredentialModule } from '../credential/credential.module';

import { MCPCoordinatorService } from './services/mcp-coordinator.service';
import { DynamicToolRegistryService } from './services/dynamic-tool-registry.service';
import { AgentFactoryService } from './services/agent-factory.service';
import { ContextMeshService } from './services/context-mesh.service';
import { TwentyIntegrationService } from './services/twenty-integration.service';
import { NaturalLanguageService } from './services/natural-language.service';
import { N8nIntegrationService } from './services/n8n-integration.service';
import { N8nCredentialBridgeService } from './services/n8n-credential-bridge.service';

import { MCPCoordinatorResolver } from './resolvers/mcp-coordinator.resolver';
import { AgentResolver } from './resolvers/agent.resolver';
import { WorkflowResolver } from './resolvers/workflow.resolver';
import { CapabilityResolver } from './resolvers/capability.resolver';

import { HealthController } from './controllers/health.controller';
import { N8nWebhookController } from './controllers/n8n-webhook.controller';

import { MCPCapabilityEntity } from './entities/mcp-capability.entity';
import { MCPAgentEntity } from './entities/mcp-agent.entity';
import { MCPWorkflowEntity } from './entities/mcp-workflow.entity';
import { MCPExecutionEntity } from './entities/mcp-execution.entity';
import { MCPContextEntity } from './entities/mcp-context.entity';

import { MCPCapabilityRepository } from './repositories/mcp-capability.repository';
import { MCPAgentRepository } from './repositories/mcp-agent.repository';
import { MCPWorkflowRepository } from './repositories/mcp-workflow.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      MCPCapabilityEntity,
      MCPAgentEntity,
      MCPWorkflowEntity,
      MCPExecutionEntity,
      MCPContextEntity,
    ]),
    CredentialModule,
  ],
  controllers: [
    HealthController,
    N8nWebhookController,
  ],
  providers: [
    // Core Services
    MCPCoordinatorService,
    DynamicToolRegistryService,
    AgentFactoryService,
    ContextMeshService,
    TwentyIntegrationService,
    NaturalLanguageService,
    N8nIntegrationService,
    N8nCredentialBridgeService,

    // Resolvers
    MCPCoordinatorResolver,
    AgentResolver,
    WorkflowResolver,
    CapabilityResolver,

    // Repositories
    MCPCapabilityRepository,
    MCPAgentRepository,
    MCPWorkflowRepository,
  ],
  exports: [
    MCPCoordinatorService,
    DynamicToolRegistryService,
    AgentFactoryService,
    ContextMeshService,
    TwentyIntegrationService,
    NaturalLanguageService,
    N8nIntegrationService,
    N8nCredentialBridgeService,
  ],
})
export class SpqrMcpModule {}
