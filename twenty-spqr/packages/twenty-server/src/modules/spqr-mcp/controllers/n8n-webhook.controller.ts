/**
 * N8n Webhook Controller
 * Handles webhook callbacks from n8n workflows
 */

import { Controller, Post, Param, Body, Logger, Get } from '@nestjs/common';
import { N8nIntegrationService } from '../services/n8n-integration.service';
import { N8nCredentialBridgeService } from '../services/n8n-credential-bridge.service';

@Controller('webhooks/n8n')
export class N8nWebhookController {
  private readonly logger = new Logger(N8nWebhookController.name);

  constructor(
    private readonly n8nIntegrationService: N8nIntegrationService,
    private readonly credentialBridgeService: N8nCredentialBridgeService,
  ) {}

  /**
   * Handle n8n workflow callback
   */
  @Post(':workflowId/callback')
  async handleWorkflowCallback(
    @Param('workflowId') workflowId: string,
    @Body() payload: any,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      this.logger.log(`📨 Received n8n webhook for workflow: ${workflowId}`);
      
      await this.n8nIntegrationService.handleWebhookCallback(workflowId, payload);
      
      return { success: true };
    } catch (error) {
      this.logger.error(`Failed to process n8n webhook for ${workflowId}:`, error);
      return { 
        success: false, 
        message: error.message 
      };
    }
  }

  /**
   * Get n8n integration status
   */
  @Get('status')
  getN8nStatus(): any {
    return {
      integration: this.n8nIntegrationService.getStatus(),
      credentialBridge: this.credentialBridgeService.getStatus()
    };
  }

  /**
   * Get all n8n workflows
   */
  @Get('workflows')
  getWorkflows(): any {
    return this.n8nIntegrationService.getAllWorkflows();
  }

  /**
   * Get workflows by service
   */
  @Get('workflows/service/:serviceName')
  getWorkflowsByService(@Param('serviceName') serviceName: string): any {
    return this.n8nIntegrationService.getWorkflowsByService(serviceName);
  }

  /**
   * Get credential mappings
   */
  @Get('credentials/mappings')
  getCredentialMappings(): any {
    return this.credentialBridgeService.getAllMappings();
  }

  /**
   * Sync all credentials
   */
  @Post('credentials/sync')
  async syncCredentials(): Promise<any> {
    try {
      const results = await this.credentialBridgeService.syncAllCredentials();
      return {
        success: true,
        results
      };
    } catch (error) {
      this.logger.error('Failed to sync credentials:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create credential mapping
   */
  @Post('credentials/mappings')
  async createCredentialMapping(@Body() body: {
    serviceName: string;
    supabaseVaultKeyId: string;
    n8nCredentialType: string;
    syncDirection?: 'supabase-to-n8n' | 'n8n-to-supabase' | 'bidirectional';
  }): Promise<any> {
    try {
      const mapping = await this.credentialBridgeService.createCredentialMapping(
        body.serviceName,
        body.supabaseVaultKeyId,
        body.n8nCredentialType,
        body.syncDirection || 'supabase-to-n8n'
      );
      
      return {
        success: true,
        mapping
      };
    } catch (error) {
      this.logger.error('Failed to create credential mapping:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
