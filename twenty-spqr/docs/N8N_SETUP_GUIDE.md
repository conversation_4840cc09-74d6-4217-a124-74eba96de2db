# N8n Integration Setup Guide

## 🚀 **Quick Setup**

### **1. Environment Variables**

Add these environment variables to your `.env` file:

```bash
# N8n Configuration
N8N_BASE_URL=http://localhost:5678
N8N_API_KEY=your_n8n_api_key_here

# N8n Credential Mappings (JSON format)
N8N_CREDENTIAL_MAPPINGS='[
  {
    "id": "gmail-mapping",
    "serviceName": "gmail",
    "supabaseVaultKeyId": "vault-gmail-oauth",
    "n8nCredentialType": "gmailOAuth2Api",
    "syncDirection": "supabase-to-n8n",
    "isActive": true
  },
  {
    "id": "slack-mapping",
    "serviceName": "slack",
    "supabaseVaultKeyId": "vault-slack-token",
    "n8nCredentialType": "slackApi",
    "syncDirection": "bidirectional",
    "isActive": true
  },
  {
    "id": "hubspot-mapping",
    "serviceName": "hubspot",
    "supabaseVaultKeyId": "vault-hubspot-key",
    "n8nCredentialType": "hubspotApi",
    "syncDirection": "supabase-to-n8n",
    "isActive": true
  }
]'

# Webhook Configuration
N8N_WEBHOOK_BASE_URL=http://your-twenty-server.com/webhooks/n8n
```

### **2. N8n Instance Setup**

#### **Option A: Docker Compose (Recommended)**

Create `docker-compose.n8n.yml`:

```yaml
version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=your_password_here
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=UTC
      - N8N_API_KEY_ENABLED=true
    volumes:
      - n8n_data:/home/<USER>/.n8n
    restart: unless-stopped

volumes:
  n8n_data:
```

Start n8n:
```bash
docker-compose -f docker-compose.n8n.yml up -d
```

#### **Option B: Local Installation**

```bash
npm install -g n8n
n8n start --tunnel
```

### **3. Generate N8n API Key**

1. Access n8n at `http://localhost:5678`
2. Go to Settings → API Keys
3. Create a new API key
4. Copy the key to your `.env` file

### **4. Create Example Workflows**

#### **Gmail Email Processor Workflow**

1. Create new workflow in n8n
2. Add webhook trigger node:
   - HTTP Method: POST
   - Path: `gmail-processor`
3. Add Gmail node:
   - Operation: Get All
   - Use expression for filters: `{{ $json.filters }}`
4. Add function node for processing:
   ```javascript
   // Extract action items from emails
   const emails = items.map(item => {
     const email = item.json;
     return {
       id: email.id,
       subject: email.payload.headers.find(h => h.name === 'Subject')?.value,
       body: email.snippet,
       actionItems: extractActionItems(email.snippet) // Your AI logic here
     };
   });
   return emails;
   ```
5. Add HTTP Request node to create Twenty tasks:
   - Method: POST
   - URL: `{{ $env.TWENTY_API_URL }}/graphql`
   - Headers: `Authorization: Bearer {{ $env.TWENTY_API_KEY }}`
6. Add webhook response node

#### **Slack Notification Workflow**

1. Create new workflow
2. Add webhook trigger: `slack-notification`
3. Add Slack node:
   - Operation: Post Message
   - Channel: `{{ $json.channel }}`
   - Text: `{{ $json.message }}`
4. Add webhook response

### **5. Set Up Credentials in n8n**

#### **Gmail OAuth2**
1. Go to Credentials → Add Credential
2. Select "Gmail OAuth2 API"
3. Configure OAuth2 settings
4. Test connection

#### **Slack API**
1. Add Slack API credential
2. Enter your Slack bot token
3. Test connection

### **6. Configure Supabase Vault**

Store credentials in Supabase Vault:

```sql
-- Gmail OAuth credentials
SELECT vault.create_secret('gmail-client-id', 'your-gmail-client-id');
SELECT vault.create_secret('gmail-client-secret', 'your-gmail-client-secret');
SELECT vault.create_secret('gmail-refresh-token', 'your-gmail-refresh-token');

-- Slack API token
SELECT vault.create_secret('slack-api-token', 'xoxb-your-slack-token');

-- HubSpot API key
SELECT vault.create_secret('hubspot-api-key', 'your-hubspot-api-key');
```

## 🔧 **Testing the Integration**

### **1. Test Workflow Discovery**

```bash
curl http://localhost:3000/webhooks/n8n/workflows
```

### **2. Test Credential Sync**

```bash
curl -X POST http://localhost:3000/webhooks/n8n/credentials/sync
```

### **3. Test Workflow Execution**

```bash
curl -X POST http://localhost:3000/api/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation { executeCapability(sessionId: \"test\", capabilityId: \"n8n-gmail-processor\", input: { filters: { labelIds: [\"INBOX\"], maxResults: 5 } }) }"
  }'
```

### **4. Test Webhook Callback**

```bash
curl -X POST http://localhost:3000/webhooks/n8n/n8n-gmail-processor/callback \
  -H "Content-Type: application/json" \
  -d '{
    "executionId": "test-123",
    "data": { "processed": 5, "created": 3 },
    "mcpContext": { "sessionId": "test", "userId": "user1", "workspaceId": "ws1" }
  }'
```

## 📊 **Monitoring and Debugging**

### **Check Integration Status**

```bash
curl http://localhost:3000/webhooks/n8n/status
```

### **View Logs**

```bash
# Twenty server logs
docker logs twenty-server

# N8n logs
docker logs n8n
```

### **Common Issues**

1. **N8n API Key Invalid**
   - Regenerate API key in n8n settings
   - Update environment variable

2. **Webhook URLs Not Accessible**
   - Ensure Twenty server is accessible from n8n
   - Check firewall settings

3. **Credential Sync Failures**
   - Verify Supabase Vault permissions
   - Check credential format transformations

4. **Workflow Not Discovered**
   - Ensure workflow is active in n8n
   - Check webhook node configuration

## 🎯 **Best Practices**

### **Security**
- Use environment variables for all secrets
- Enable n8n basic auth in production
- Use HTTPS for webhook URLs
- Regularly rotate API keys

### **Performance**
- Set appropriate timeouts for workflows
- Use async execution for long-running workflows
- Implement retry logic for failed executions
- Monitor execution metrics

### **Reliability**
- Set up health checks for n8n instance
- Implement fallback mechanisms
- Use circuit breaker pattern for external calls
- Log all workflow executions

### **Scalability**
- Use multiple n8n instances for load balancing
- Implement workflow partitioning by service type
- Cache workflow definitions
- Use database for execution state

This setup provides a robust foundation for integrating n8n as a middleware layer in your SPQR-Twenty MCP system!
