/**
 * N8n Integration Example
 * Demonstrates how agents can leverage n8n workflows for external service integration
 */

import { MCPCoordinator } from '../core/MCPCoordinator';

export class N8nIntegrationExample {
  private coordinator: MCPCoordinator;

  constructor() {
    this.coordinator = new MCPCoordinator();
  }

  /**
   * Example 1: Email Processing via N8n Gmail Integration
   */
  async emailProcessingViaN8n(): Promise<void> {
    console.log('📧 Starting Email Processing via N8n...');

    const context = await this.coordinator.getContext(
      'n8n-email-session',
      'user-123',
      'workspace-456'
    );

    // Execute n8n workflow that handles Gmail OAuth and email processing
    const emailResult = await this.coordinator.executeCapability(
      context.sessionId,
      'n8n-gmail-processor', // This would be auto-discovered from n8n
      {
        action: 'process_unread_emails',
        filters: {
          labelIds: ['INBOX'],
          maxResults: 10
        },
        extractTasks: true,
        createTwentyRecords: true
      }
    );

    console.log('📧 Email processing result:', emailResult);

    // The n8n workflow handled:
    // 1. OAuth authentication with Gmail
    // 2. Fetching unread emails
    // 3. Extracting action items using AI
    // 4. Creating tasks in Twenty CRM
    // 5. Marking emails as processed
  }

  /**
   * Example 2: Multi-Service Lead Enrichment
   */
  async leadEnrichmentWorkflow(): Promise<void> {
    console.log('🔍 Starting Lead Enrichment via N8n...');

    const context = await this.coordinator.getContext(
      'n8n-enrichment-session',
      'user-123',
      'workspace-456'
    );

    // Execute n8n workflow that enriches leads using multiple services
    const enrichmentResult = await this.coordinator.executeCapability(
      context.sessionId,
      'n8n-lead-enrichment', // Multi-service enrichment workflow
      {
        leadData: {
          email: '<EMAIL>',
          company: 'Acme Corp',
          name: 'John Doe'
        },
        enrichmentSources: ['clearbit', 'hunter', 'linkedin', 'apollo'],
        updateTwenty: true
      }
    );

    console.log('🔍 Lead enrichment result:', enrichmentResult);

    // The n8n workflow handled:
    // 1. Clearbit company enrichment
    // 2. Hunter email verification
    // 3. LinkedIn profile lookup
    // 4. Apollo contact details
    // 5. Data consolidation and deduplication
    // 6. Updating Twenty CRM person record
  }

  /**
   * Example 3: Slack Notification with Dynamic Workflow Discovery
   */
  async slackNotificationWorkflow(): Promise<void> {
    console.log('💬 Starting Slack Notification via N8n...');

    const context = await this.coordinator.getContext(
      'n8n-slack-session',
      'user-123',
      'workspace-456'
    );

    // First, discover available Slack workflows
    const slackWorkflows = this.coordinator.n8nIntegration.getWorkflowsByService('slack');
    console.log(`Found ${slackWorkflows.length} Slack workflows:`, slackWorkflows.map(w => w.name));

    if (slackWorkflows.length > 0) {
      const slackWorkflow = slackWorkflows[0]; // Use the first available Slack workflow

      const notificationResult = await this.coordinator.executeCapability(
        context.sessionId,
        slackWorkflow.id,
        {
          channel: '#sales-updates',
          message: 'New high-value lead created in Twenty CRM',
          attachments: [
            {
              title: 'Lead Details',
              fields: [
                { title: 'Company', value: 'Acme Corp', short: true },
                { title: 'Value', value: '$50,000', short: true }
              ]
            }
          ],
          notifyUsers: ['@sales-team']
        }
      );

      console.log('💬 Slack notification result:', notificationResult);
    }
  }

  /**
   * Example 4: Hybrid Agent-N8n Workflow
   */
  async hybridAgentN8nWorkflow(): Promise<void> {
    console.log('🤖 Starting Hybrid Agent-N8n Workflow...');

    const context = await this.coordinator.getContext(
      'hybrid-workflow-session',
      'user-123',
      'workspace-456'
    );

    // Step 1: Agent analyzes customer data locally
    const analysisResult = await this.coordinator.executeCapability(
      context.sessionId,
      'customer-analysis-agent',
      {
        customerId: 'customer-123',
        analysisType: 'churn-risk'
      }
    );

    console.log('📊 Customer analysis:', analysisResult);

    // Step 2: If high churn risk, trigger n8n workflow for intervention
    if (analysisResult.churnRisk === 'high') {
      const interventionResult = await this.coordinator.executeCapability(
        context.sessionId,
        'n8n-customer-intervention',
        {
          customerId: 'customer-123',
          riskLevel: analysisResult.churnRisk,
          riskFactors: analysisResult.riskFactors,
          interventionType: 'proactive-outreach'
        }
      );

      console.log('🚨 Customer intervention triggered:', interventionResult);

      // The n8n workflow handled:
      // 1. Creating personalized email campaign in Mailchimp
      // 2. Scheduling follow-up call in Calendly
      // 3. Notifying account manager via Slack
      // 4. Creating intervention task in Twenty CRM
      // 5. Setting up monitoring alerts
    }
  }

  /**
   * Example 5: Real-time Webhook Processing
   */
  async webhookProcessingExample(): Promise<void> {
    console.log('🔗 Demonstrating Webhook Processing...');

    // Simulate receiving a webhook from n8n
    const webhookPayload = {
      workflowId: 'n8n-lead-scoring',
      executionId: 'exec-12345',
      data: {
        leadId: 'lead-456',
        score: 85,
        qualification: 'hot',
        nextActions: ['schedule-demo', 'assign-sales-rep']
      },
      mcpContext: {
        sessionId: 'webhook-session',
        userId: 'user-123',
        workspaceId: 'workspace-456'
      }
    };

    // Process the webhook
    await this.coordinator.handleN8nWebhook('n8n-lead-scoring', webhookPayload);

    console.log('🔗 Webhook processed, context mesh updated');

    // The webhook processing:
    // 1. Updated execution cache with results
    // 2. Synchronized state via context mesh
    // 3. Triggered dependent capabilities
    // 4. Notified listening agents
  }

  /**
   * Example 6: Credential Management Demo
   */
  async credentialManagementDemo(): Promise<void> {
    console.log('🔐 Demonstrating Credential Management...');

    // Create credential mapping for Gmail
    const gmailMapping = await this.coordinator.credentialBridge.createCredentialMapping(
      'gmail',
      'vault-key-gmail-123',
      'gmailOAuth2Api',
      'supabase-to-n8n'
    );

    console.log('🔐 Created Gmail credential mapping:', gmailMapping);

    // Sync all credentials
    const syncResults = await this.coordinator.credentialBridge.syncAllCredentials();
    console.log('🔄 Credential sync results:', syncResults);

    // Get credential bridge status
    const bridgeStatus = this.coordinator.credentialBridge.getStatus();
    console.log('📊 Credential bridge status:', bridgeStatus);
  }

  /**
   * Example 7: Dynamic Workflow Composition
   */
  async dynamicWorkflowComposition(): Promise<void> {
    console.log('🔧 Demonstrating Dynamic Workflow Composition...');

    const context = await this.coordinator.getContext(
      'composition-session',
      'user-123',
      'workspace-456'
    );

    // Compose a workflow using both internal agents and n8n workflows
    const compositionResult = await this.coordinator.executeCapability(
      context.sessionId,
      'workflow-composer-agent',
      {
        goal: 'complete-lead-lifecycle',
        steps: [
          { type: 'agent', capability: 'lead-qualification-agent' },
          { type: 'n8n', workflow: 'n8n-lead-enrichment' },
          { type: 'agent', capability: 'lead-scoring-agent' },
          { type: 'n8n', workflow: 'n8n-sales-automation' },
          { type: 'agent', capability: 'follow-up-scheduler-agent' }
        ],
        executionMode: 'sequential'
      }
    );

    console.log('🔧 Dynamic composition result:', compositionResult);
  }

  /**
   * Run all n8n integration examples
   */
  async runAllExamples(): Promise<void> {
    console.log('🎬 Running all N8n Integration Examples...\n');

    try {
      await this.emailProcessingViaN8n();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.leadEnrichmentWorkflow();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.slackNotificationWorkflow();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.hybridAgentN8nWorkflow();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.webhookProcessingExample();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.credentialManagementDemo();
      console.log('\n' + '='.repeat(50) + '\n');

      await this.dynamicWorkflowComposition();
      console.log('\n' + '='.repeat(50) + '\n');

      // Show final system status including n8n integration
      const status = this.coordinator.getStatus();
      const n8nStatus = this.coordinator.getN8nStatus();
      
      console.log('📊 Final System Status:');
      console.log('MCP System:', JSON.stringify(status, null, 2));
      console.log('N8n Integration:', JSON.stringify(n8nStatus, null, 2));

    } catch (error) {
      console.error('❌ Example execution failed:', error);
    }
  }
}

// Example usage
if (require.main === module) {
  const example = new N8nIntegrationExample();
  example.runAllExamples();
}
