# N8n Integration Architecture for SPQR-Twenty MCP System

## 🎯 **Strategic Overview**

This document outlines the comprehensive integration of n8n as a middleware layer in the SPQR-Twenty MCP system, enabling a multi-dimensional enhancement strategy that leverages both n8n's mature ecosystem and our dynamic MCP capabilities.

## 🏗️ **Architecture Components**

### **1. N8n Integration Service (`N8nIntegration.ts`)**

**Purpose**: Primary bridge between MCP system and n8n workflows

**Key Features**:
- **Auto-Discovery**: Automatically discovers active n8n workflows via API
- **Dynamic Registration**: Registers workflows as MCP service capabilities
- **Dual Execution**: Supports both webhook and API execution modes
- **Real-time Sync**: Periodic synchronization with n8n instance
- **Context Integration**: Passes MCP context to n8n workflows

**Capability Registration Pattern**:
```typescript
// N8n workflows are registered as 'service' type capabilities
const capability: MCPCapability = {
  id: `n8n-${workflowId}`,
  name: workflow.name,
  type: 'service', // Service bridges to external tools
  schema: generateInputSchema(workflow.parameters),
  dependencies: workflow.metadata.externalServices,
  autoSpawn: false,
  lifecycle: 'on-demand'
};
```

### **2. Credential Bridge (`N8nCredentialBridge.ts`)**

**Purpose**: Manages authentication delegation between Supabase Vault and n8n

**Key Features**:
- **Bidirectional Sync**: Supports Supabase→n8n, n8n→Supabase, or bidirectional
- **Service Mapping**: Maps service credentials to appropriate n8n credential types
- **Automatic Transformation**: Converts credential formats between systems
- **Periodic Sync**: Keeps credentials synchronized across systems

**Credential Flow**:
```
Supabase Vault (Encrypted) ←→ Credential Bridge ←→ N8n Credential Store
                ↓
        Service-Specific Transformations
                ↓
        OAuth2, API Keys, Basic Auth, etc.
```

### **3. Enhanced MCP Coordinator**

**Integration Points**:
- Service execution routing (n8n- prefixed capabilities)
- Webhook callback handling
- Status monitoring and reporting
- Context mesh synchronization

## 🔄 **Execution Patterns**

### **Pattern 1: Agent-Triggered N8n Workflow**

```typescript
// Agent discovers need for external service integration
const agent = await agentFactory.createAgent('email-processing-agent');

// Agent executes n8n workflow for Gmail integration
const result = await coordinator.executeCapability(
  sessionId,
  'n8n-gmail-processor', // Auto-discovered n8n workflow
  {
    action: 'process_unread_emails',
    filters: { labelIds: ['INBOX'] },
    extractTasks: true
  }
);

// N8n handles: OAuth, API calls, data processing
// Result returned to agent for further processing
```

### **Pattern 2: Hybrid Workflow Composition**

```typescript
// Combine internal agents with n8n workflows
const workflow = [
  { type: 'agent', capability: 'lead-qualification-agent' },    // Internal
  { type: 'n8n', workflow: 'n8n-lead-enrichment' },           // External via n8n
  { type: 'agent', capability: 'lead-scoring-agent' },         // Internal
  { type: 'n8n', workflow: 'n8n-sales-automation' }           // External via n8n
];
```

### **Pattern 3: Webhook-Driven Updates**

```typescript
// N8n workflow completes and sends webhook
POST /webhooks/n8n/workflow-123/callback
{
  "executionId": "exec-456",
  "data": { "processed": 15, "tasks_created": 3 },
  "mcpContext": { "sessionId": "session-789" }
}

// System updates context mesh and notifies agents
contextMesh.updateSharedState(context, 'n8n-result', data);
```

## 🔐 **Authentication Delegation Strategy**

### **Credential Mapping Configuration**

```typescript
// Environment-based credential mapping
N8N_CREDENTIAL_MAPPINGS=[
  {
    "serviceName": "gmail",
    "supabaseVaultKeyId": "vault-gmail-oauth",
    "n8nCredentialType": "gmailOAuth2Api",
    "syncDirection": "supabase-to-n8n"
  },
  {
    "serviceName": "slack",
    "supabaseVaultKeyId": "vault-slack-token",
    "n8nCredentialType": "slackApi",
    "syncDirection": "bidirectional"
  }
]
```

### **OAuth Flow Delegation**

1. **Initial Setup**: OAuth flows handled in n8n UI
2. **Credential Sync**: Tokens synchronized to Supabase Vault
3. **Agent Access**: Agents use Supabase credentials for direct API calls when needed
4. **N8n Workflows**: Use n8n-stored credentials for complex integrations

## 📊 **Capability Discovery and Registration**

### **Auto-Discovery Process**

```mermaid
graph TD
    A[N8n Integration Service] --> B[Fetch Active Workflows]
    B --> C[Parse Workflow Definitions]
    C --> D[Extract Parameters & Services]
    D --> E[Generate MCP Capability Schema]
    E --> F[Register with Coordinator]
    F --> G[Set Up Webhook Endpoints]
    G --> H[Start Periodic Sync]
```

### **Dynamic Registration Example**

```typescript
// Discovered n8n workflow becomes MCP capability
{
  id: 'n8n-hubspot-contact-sync',
  name: 'HubSpot Contact Sync',
  type: 'service',
  schema: {
    input: {
      contactData: { type: 'object', required: true },
      syncDirection: { type: 'string', enum: ['to-hubspot', 'from-hubspot'] }
    },
    output: {
      success: 'boolean',
      syncedRecords: 'number',
      executionId: 'string'
    }
  },
  dependencies: ['hubspot'],
  metadata: {
    externalServices: ['hubspot'],
    estimatedDuration: 5000,
    costEstimate: 0.02
  }
}
```

## 🌐 **Multi-Dimensional Enhancement Strategy**

### **Dimension 1: N8n Ecosystem Leverage**

- **400+ Pre-built Integrations**: Instant access to mature connectors
- **Community Workflows**: Leverage community-built automation patterns
- **Visual Workflow Builder**: Non-technical users can create integrations
- **OAuth Management**: Robust authentication handling

### **Dimension 2: Direct Agent Tools**

- **Performance Critical**: Direct API calls for low-latency operations
- **Custom Logic**: Complex business logic in TypeScript agents
- **Context Awareness**: Full access to MCP context and state
- **Dynamic Composition**: Runtime tool composition and chaining

### **Dimension 3: Hybrid Workflows**

- **Best of Both**: Combine n8n's integrations with agent intelligence
- **Conditional Routing**: Agents decide when to use n8n vs direct calls
- **Error Handling**: Agents can recover from n8n workflow failures
- **State Management**: Shared state across hybrid execution paths

## 🔄 **Real-Time Synchronization**

### **Context Mesh Integration**

```typescript
// N8n execution updates context mesh
contextMesh.updateSharedState(
  context,
  `n8n-execution-${executionId}`,
  executionResult,
  'n8n-integration'
);

// Sync rules propagate updates to relevant agents
syncRule: {
  pattern: 'n8n-execution-.*',
  targets: ['monitoring-agent', 'analytics-agent'],
  transform: (value) => ({
    ...value,
    syncedAt: new Date()
  })
}
```

### **Event-Driven Updates**

```typescript
// N8n integration emits events
n8nIntegration.on('workflow-executed', ({ workflow, result, context }) => {
  // Update metrics
  // Trigger dependent workflows
  // Notify monitoring systems
});

n8nIntegration.on('workflow-error', ({ workflow, error, context }) => {
  // Log error
  // Trigger recovery agents
  // Update reliability scores
});
```

## 🎯 **Implementation Recommendations**

### **1. Capability Type Strategy**

- **N8n Workflows**: Register as `service` type capabilities
- **Rationale**: Services bridge to external tools, fitting n8n's role
- **Benefits**: Clear separation from internal tools and agents

### **2. Discovery Pattern**

```typescript
// Agent discovers available n8n workflows
const slackWorkflows = coordinator.n8nIntegration.getWorkflowsByService('slack');
const bestWorkflow = slackWorkflows.find(w => w.metadata.estimatedDuration < 5000);

// Execute discovered workflow
const result = await coordinator.executeCapability(sessionId, bestWorkflow.id, input);
```

### **3. Error Handling and Fallbacks**

```typescript
// Try n8n workflow first, fallback to direct agent
try {
  return await coordinator.executeCapability(sessionId, 'n8n-email-sender', input);
} catch (error) {
  console.log('N8n workflow failed, using direct email agent');
  return await coordinator.executeCapability(sessionId, 'direct-email-agent', input);
}
```

### **4. Performance Optimization**

- **Caching**: Cache n8n workflow definitions and execution results
- **Parallel Execution**: Execute independent n8n workflows in parallel
- **Streaming**: Support streaming responses for long-running workflows
- **Circuit Breaker**: Implement circuit breaker pattern for n8n failures

## 📈 **Monitoring and Analytics**

### **Key Metrics**

- N8n workflow execution times and success rates
- Credential sync status and frequency
- Agent vs n8n execution patterns
- External service usage and costs

### **Status Endpoints**

```typescript
// System status including n8n integration
GET /api/mcp/status
{
  "mcp": { "totalCapabilities": 45, "activeAgents": 12 },
  "n8n": {
    "connected": true,
    "totalWorkflows": 23,
    "activeWorkflows": 18,
    "cachedExecutions": 156
  },
  "credentialBridge": {
    "totalMappings": 8,
    "activeMappings": 7,
    "lastSyncAt": "2024-01-15T10:30:00Z"
  }
}
```

This architecture enables a truly dynamic, self-organizing system that leverages the best of both n8n's mature ecosystem and your MCP system's intelligent capabilities, creating a powerful hybrid platform for automated CRM operations.
