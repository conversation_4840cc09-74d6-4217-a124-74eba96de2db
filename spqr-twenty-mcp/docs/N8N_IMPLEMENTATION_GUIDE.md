# N8n Integration Implementation Guide

## 🚀 **Quick Start Setup**

### **1. Environment Configuration**

```bash
# N8n Configuration
N8N_BASE_URL=http://localhost:5678
N8N_API_KEY=your_n8n_api_key

# Credential Bridge Configuration
N8N_CREDENTIAL_MAPPINGS='[
  {
    "id": "gmail-mapping",
    "serviceName": "gmail",
    "supabaseVaultKeyId": "vault-gmail-oauth",
    "n8nCredentialType": "gmailOAuth2Api",
    "syncDirection": "supabase-to-n8n",
    "isActive": true
  },
  {
    "id": "slack-mapping", 
    "serviceName": "slack",
    "supabaseVaultKeyId": "vault-slack-token",
    "n8nCredentialType": "slackApi",
    "syncDirection": "bidirectional",
    "isActive": true
  }
]'

# Webhook Configuration
N8N_WEBHOOK_BASE_URL=http://your-mcp-system.com/webhooks/n8n
```

### **2. N8n Workflow Setup**

#### **Gmail Processing Workflow Example**

```json
{
  "name": "Gmail Email Processor",
  "nodes": [
    {
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "parameters": {
        "httpMethod": "POST",
        "path": "gmail-processor",
        "responseMode": "responseNode"
      }
    },
    {
      "name": "Gmail",
      "type": "n8n-nodes-base.gmail",
      "parameters": {
        "operation": "getAll",
        "filters": {
          "labelIds": "={{ $json.filters.labelIds }}",
          "maxResults": "={{ $json.filters.maxResults || 10 }}"
        }
      },
      "credentials": {
        "gmailOAuth2Api": "gmail-mcp-sync"
      }
    },
    {
      "name": "Process Emails",
      "type": "n8n-nodes-base.function",
      "parameters": {
        "functionCode": "// Extract action items using AI\nconst emails = items.map(item => {\n  const email = item.json;\n  return {\n    id: email.id,\n    subject: email.payload.headers.find(h => h.name === 'Subject')?.value,\n    body: email.snippet,\n    actionItems: extractActionItems(email.snippet)\n  };\n});\n\nreturn emails;"
      }
    },
    {
      "name": "Create Twenty Tasks",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "POST",
        "url": "{{ $env.TWENTY_API_URL }}/graphql",
        "headers": {
          "Authorization": "Bearer {{ $env.TWENTY_API_KEY }}",
          "Content-Type": "application/json"
        },
        "body": {
          "query": "mutation CreateTask($input: TaskCreateInput!) { createTask(input: $input) { id title } }",
          "variables": {
            "input": {
              "title": "{{ $json.subject }}",
              "description": "{{ $json.actionItems.join('\\n') }}"
            }
          }
        }
      }
    },
    {
      "name": "Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "parameters": {
        "respondWith": "json",
        "responseBody": {
          "success": true,
          "processed": "={{ $json.length }}",
          "tasksCreated": "={{ $json.filter(item => item.success).length }}"
        }
      }
    }
  ]
}
```

### **3. MCP System Integration**

#### **Initialize N8n Integration**

```typescript
// In your main application
import { MCPCoordinator } from './core/MCPCoordinator';

const coordinator = new MCPCoordinator();

// The n8n integration is automatically initialized
// Workflows will be discovered and registered as capabilities

// Check integration status
const n8nStatus = coordinator.getN8nStatus();
console.log('N8n Integration Status:', n8nStatus);
```

#### **Execute N8n Workflow from Agent**

```typescript
// In an agent implementation
export class EmailProcessingAgent extends DynamicAgent {
  async execute(input: any): Promise<any> {
    // Check if n8n workflow is available
    const n8nWorkflows = this.coordinator.n8nIntegration.getWorkflowsByService('gmail');
    
    if (n8nWorkflows.length > 0) {
      // Use n8n for Gmail integration
      return await this.coordinator.executeCapability(
        this.context.sessionId,
        'n8n-gmail-processor',
        {
          filters: input.filters,
          extractTasks: true,
          createTwentyRecords: true
        }
      );
    } else {
      // Fallback to direct Gmail API integration
      return await this.executeDirectGmailIntegration(input);
    }
  }
}
```

## 🔐 **Credential Management Setup**

### **1. Supabase Vault Configuration**

```sql
-- Create vault key for Gmail OAuth
SELECT vault.create_secret('gmail-oauth-client-secret', 'your-gmail-client-secret');
SELECT vault.create_secret('gmail-oauth-refresh-token', 'your-gmail-refresh-token');

-- Create vault key for Slack
SELECT vault.create_secret('slack-api-token', 'xoxb-your-slack-token');
```

### **2. Credential Bridge Setup**

```typescript
// Create credential mappings
const credentialBridge = coordinator.credentialBridge;

// Gmail OAuth mapping
await credentialBridge.createCredentialMapping(
  'gmail',
  'vault-gmail-oauth',
  'gmailOAuth2Api',
  'supabase-to-n8n'
);

// Slack API mapping
await credentialBridge.createCredentialMapping(
  'slack',
  'vault-slack-token',
  'slackApi',
  'bidirectional'
);

// Sync all credentials
const syncResults = await credentialBridge.syncAllCredentials();
console.log('Credential sync results:', syncResults);
```

## 🔄 **Webhook Handling Setup**

### **1. Express.js Webhook Endpoints**

```typescript
// In your Express.js server
import express from 'express';

const app = express();

// N8n webhook callback endpoint
app.post('/webhooks/n8n/:workflowId/callback', async (req, res) => {
  const { workflowId } = req.params;
  const payload = req.body;
  
  try {
    await coordinator.handleN8nWebhook(workflowId, payload);
    res.json({ success: true });
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Health check for n8n integration
app.get('/health/n8n', (req, res) => {
  const status = coordinator.getN8nStatus();
  res.json(status);
});
```

### **2. N8n Webhook Configuration**

In your n8n workflows, add a webhook node at the end to notify the MCP system:

```json
{
  "name": "Notify MCP System",
  "type": "n8n-nodes-base.httpRequest",
  "parameters": {
    "method": "POST",
    "url": "{{ $env.MCP_WEBHOOK_URL }}/{{ $workflow.id }}/callback",
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "workflowId": "{{ $workflow.id }}",
      "executionId": "{{ $execution.id }}",
      "data": "={{ $json }}",
      "mcpContext": "={{ $node['Webhook'].json.mcpContext }}"
    }
  }
}
```

## 🎯 **Usage Patterns**

### **1. Agent-Triggered N8n Execution**

```typescript
// Agent discovers and executes n8n workflow
const agent = await agentFactory.createAgent('lead-enrichment-agent');

const result = await agent.execute({
  leadData: {
    email: '<EMAIL>',
    company: 'Target Corp'
  },
  enrichmentSources: ['clearbit', 'hunter', 'apollo']
});

// N8n workflow handles multiple API calls and data consolidation
console.log('Enriched lead data:', result);
```

### **2. Hybrid Workflow Composition**

```typescript
// Compose workflow with both agents and n8n
const workflowSteps = [
  { type: 'agent', capability: 'lead-qualification-agent' },
  { type: 'n8n', workflow: 'n8n-lead-enrichment' },
  { type: 'agent', capability: 'lead-scoring-agent' },
  { type: 'n8n', workflow: 'n8n-sales-automation' }
];

const compositionResult = await coordinator.executeCapability(
  sessionId,
  'workflow-composer-agent',
  { steps: workflowSteps, executionMode: 'sequential' }
);
```

### **3. Dynamic Service Discovery**

```typescript
// Discover available integrations
const availableServices = coordinator.n8nIntegration.getAllWorkflows()
  .reduce((services, workflow) => {
    workflow.metadata.externalServices.forEach(service => {
      if (!services[service]) services[service] = [];
      services[service].push(workflow);
    });
    return services;
  }, {});

console.log('Available integrations:', Object.keys(availableServices));

// Use best workflow for specific service
const slackWorkflows = availableServices.slack || [];
const bestSlackWorkflow = slackWorkflows
  .sort((a, b) => a.metadata.estimatedDuration - b.metadata.estimatedDuration)[0];

if (bestSlackWorkflow) {
  await coordinator.executeCapability(sessionId, bestSlackWorkflow.id, slackData);
}
```

## 📊 **Monitoring and Debugging**

### **1. System Status Monitoring**

```typescript
// Get comprehensive system status
const systemStatus = {
  mcp: coordinator.getStatus(),
  n8n: coordinator.getN8nStatus(),
  workflows: coordinator.n8nIntegration.getAllWorkflows().map(w => ({
    id: w.id,
    name: w.name,
    isActive: w.isActive,
    externalServices: w.metadata.externalServices
  }))
};

console.log('System Status:', JSON.stringify(systemStatus, null, 2));
```

### **2. Execution Tracking**

```typescript
// Track n8n execution results
coordinator.n8nIntegration.on('workflow-executed', ({ workflow, result, context }) => {
  console.log(`✅ N8n workflow ${workflow.name} completed in ${result.duration}ms`);
  
  // Log to analytics system
  analytics.track('n8n_workflow_executed', {
    workflowId: workflow.id,
    duration: result.duration,
    success: result.success,
    externalServiceCalls: result.metadata.externalServiceCalls
  });
});

coordinator.n8nIntegration.on('workflow-error', ({ workflow, error, context }) => {
  console.error(`❌ N8n workflow ${workflow.name} failed:`, error);
  
  // Trigger recovery or fallback
  coordinator.executeCapability(context.sessionId, 'error-recovery-agent', {
    failedWorkflow: workflow.id,
    error: error.message
  });
});
```

### **3. Performance Optimization**

```typescript
// Monitor and optimize workflow selection
const workflowMetrics = coordinator.n8nIntegration.getAllWorkflows().map(workflow => {
  const executions = coordinator.n8nIntegration.getExecutionHistory(workflow.id);
  return {
    id: workflow.id,
    name: workflow.name,
    avgDuration: executions.reduce((sum, exec) => sum + exec.duration, 0) / executions.length,
    successRate: executions.filter(exec => exec.success).length / executions.length,
    costPerExecution: workflow.metadata.costEstimate
  };
});

// Select optimal workflow based on performance metrics
const optimalWorkflow = workflowMetrics
  .filter(m => m.successRate > 0.95)
  .sort((a, b) => a.avgDuration - b.avgDuration)[0];
```

This implementation guide provides a practical foundation for integrating n8n as a middleware layer while maintaining the dynamic, self-organizing nature of your MCP system.
