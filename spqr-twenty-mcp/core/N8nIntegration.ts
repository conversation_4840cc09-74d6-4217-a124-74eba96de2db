/**
 * N8n Integration Service
 * Bridges SPQR-MCP system with n8n workflows for external service integration
 */

import { EventEmitter } from 'events';
import { MCPCoordinator, MCPCapability, MCPContext } from './MCPCoordinator';

export interface N8nWorkflow {
  id: string;
  name: string;
  description: string;
  webhookUrl: string;
  apiEndpoint?: string;
  tags: string[];
  parameters: N8nParameter[];
  authentication: N8nAuthConfig;
  isActive: boolean;
  executionMode: 'webhook' | 'api' | 'hybrid';
  responseMode: 'async' | 'sync' | 'streaming';
  metadata: {
    category: string;
    externalServices: string[];
    estimatedDuration: number;
    costEstimate?: number;
  };
}

export interface N8nParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  default?: any;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    enum?: any[];
  };
}

export interface N8nAuthConfig {
  type: 'none' | 'api_key' | 'oauth2' | 'basic' | 'bearer';
  credentialName?: string; // Reference to n8n credential
  delegateToN8n: boolean; // Whether n8n handles auth
}

export interface N8nExecutionResult {
  success: boolean;
  executionId: string;
  data?: any;
  error?: string;
  duration: number;
  metadata: {
    workflowId: string;
    n8nExecutionId?: string;
    externalServiceCalls: number;
    credentialsUsed: string[];
  };
}

export class N8nIntegration extends EventEmitter {
  private coordinator: MCPCoordinator;
  private workflows: Map<string, N8nWorkflow> = new Map();
  private n8nBaseUrl: string;
  private n8nApiKey: string;
  private executionCache: Map<string, N8nExecutionResult> = new Map();
  private webhookEndpoints: Map<string, string> = new Map();

  constructor(coordinator: MCPCoordinator) {
    super();
    this.coordinator = coordinator;
    this.n8nBaseUrl = process.env.N8N_BASE_URL || 'http://localhost:5678';
    this.n8nApiKey = process.env.N8N_API_KEY || '';
    
    this.initializeIntegration();
  }

  private async initializeIntegration(): Promise<void> {
    console.log('🔗 Initializing N8n Integration...');
    
    // Discover available n8n workflows
    await this.discoverN8nWorkflows();
    
    // Register workflows as MCP capabilities
    await this.registerWorkflowCapabilities();
    
    // Set up webhook listeners
    await this.setupWebhookHandlers();
    
    // Start periodic sync
    this.startPeriodicSync();
    
    console.log('✅ N8n Integration initialized');
  }

  /**
   * Discover available n8n workflows
   */
  private async discoverN8nWorkflows(): Promise<void> {
    try {
      const response = await fetch(`${this.n8nBaseUrl}/api/v1/workflows`, {
        headers: {
          'X-N8N-API-KEY': this.n8nApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch n8n workflows: ${response.statusText}`);
      }

      const workflows = await response.json();
      
      for (const workflow of workflows.data || []) {
        if (workflow.active) {
          const n8nWorkflow = await this.parseN8nWorkflow(workflow);
          if (n8nWorkflow) {
            this.workflows.set(n8nWorkflow.id, n8nWorkflow);
            console.log(`📋 Discovered n8n workflow: ${n8nWorkflow.name}`);
          }
        }
      }
    } catch (error) {
      console.error('Failed to discover n8n workflows:', error);
    }
  }

  /**
   * Parse n8n workflow into our format
   */
  private async parseN8nWorkflow(workflow: any): Promise<N8nWorkflow | null> {
    try {
      // Extract webhook URLs from workflow nodes
      const webhookNodes = workflow.nodes?.filter((node: any) => 
        node.type === 'n8n-nodes-base.webhook'
      ) || [];
      
      const webhookUrl = webhookNodes.length > 0 
        ? `${this.n8nBaseUrl}/webhook/${webhookNodes[0].webhookId || workflow.id}`
        : null;

      // Extract parameters from webhook or manual trigger nodes
      const parameters = this.extractWorkflowParameters(workflow);
      
      // Determine external services used
      const externalServices = this.extractExternalServices(workflow);

      return {
        id: `n8n-${workflow.id}`,
        name: workflow.name,
        description: workflow.description || `N8n workflow: ${workflow.name}`,
        webhookUrl: webhookUrl || '',
        apiEndpoint: `${this.n8nBaseUrl}/api/v1/workflows/${workflow.id}/execute`,
        tags: workflow.tags || [],
        parameters,
        authentication: {
          type: 'api_key',
          credentialName: 'n8n-api',
          delegateToN8n: true
        },
        isActive: workflow.active,
        executionMode: webhookUrl ? 'webhook' : 'api',
        responseMode: 'async',
        metadata: {
          category: 'external-integration',
          externalServices,
          estimatedDuration: 5000, // Default 5 seconds
          costEstimate: externalServices.length * 0.01
        }
      };
    } catch (error) {
      console.error(`Failed to parse workflow ${workflow.id}:`, error);
      return null;
    }
  }

  /**
   * Extract parameters from workflow definition
   */
  private extractWorkflowParameters(workflow: any): N8nParameter[] {
    const parameters: N8nParameter[] = [];
    
    // Look for webhook nodes with defined parameters
    const webhookNodes = workflow.nodes?.filter((node: any) => 
      node.type === 'n8n-nodes-base.webhook'
    ) || [];
    
    for (const node of webhookNodes) {
      const nodeParams = node.parameters || {};
      
      // Extract expected parameters from webhook configuration
      if (nodeParams.options?.rawBody === false) {
        // Form data parameters
        parameters.push({
          name: 'data',
          type: 'object',
          required: true,
          description: 'Webhook payload data'
        });
      }
    }
    
    // Default parameters for all n8n workflows
    parameters.push(
      {
        name: 'executionMode',
        type: 'string',
        required: false,
        default: 'async',
        description: 'Execution mode: sync or async',
        validation: { enum: ['sync', 'async'] }
      },
      {
        name: 'waitForCompletion',
        type: 'boolean',
        required: false,
        default: false,
        description: 'Whether to wait for workflow completion'
      }
    );
    
    return parameters;
  }

  /**
   * Extract external services from workflow nodes
   */
  private extractExternalServices(workflow: any): string[] {
    const services = new Set<string>();
    
    for (const node of workflow.nodes || []) {
      // Map n8n node types to service names
      const serviceMap: Record<string, string> = {
        'n8n-nodes-base.gmail': 'gmail',
        'n8n-nodes-base.slack': 'slack',
        'n8n-nodes-base.hubspot': 'hubspot',
        'n8n-nodes-base.googleSheets': 'google-sheets',
        'n8n-nodes-base.googleCalendar': 'google-calendar',
        'n8n-nodes-base.notion': 'notion',
        'n8n-nodes-base.airtable': 'airtable',
        'n8n-nodes-base.salesforce': 'salesforce',
        'n8n-nodes-base.microsoftOutlook': 'outlook',
        'n8n-nodes-base.discord': 'discord',
        'n8n-nodes-base.telegram': 'telegram'
      };
      
      if (serviceMap[node.type]) {
        services.add(serviceMap[node.type]);
      }
    }
    
    return Array.from(services);
  }

  /**
   * Register n8n workflows as MCP capabilities
   */
  private async registerWorkflowCapabilities(): Promise<void> {
    for (const workflow of this.workflows.values()) {
      const capability: MCPCapability = {
        id: workflow.id,
        name: workflow.name,
        type: 'service', // n8n workflows are services that bridge to external tools
        schema: {
          input: this.generateInputSchema(workflow.parameters),
          output: {
            success: 'boolean',
            data: 'any',
            executionId: 'string',
            metadata: 'object'
          },
          parameters: {}
        },
        dependencies: workflow.metadata.externalServices,
        autoSpawn: false,
        lifecycle: 'on-demand'
      };

      await this.coordinator.registerCapability(capability);
      console.log(`🔧 Registered n8n workflow capability: ${workflow.name}`);
    }
  }

  /**
   * Generate input schema from workflow parameters
   */
  private generateInputSchema(parameters: N8nParameter[]): any {
    const schema: any = {};

    for (const param of parameters) {
      schema[param.name] = {
        type: param.type,
        required: param.required,
        description: param.description,
        default: param.default
      };

      if (param.validation) {
        schema[param.name].validation = param.validation;
      }
    }

    return schema;
  }

  /**
   * Execute n8n workflow
   */
  async executeWorkflow(workflowId: string, input: any, context: MCPContext): Promise<N8nExecutionResult> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`N8n workflow ${workflowId} not found`);
    }

    console.log(`🚀 Executing n8n workflow: ${workflow.name}`);
    const startTime = Date.now();
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    try {
      let result: any;

      if (workflow.executionMode === 'webhook' && workflow.webhookUrl) {
        result = await this.executeViaWebhook(workflow, input, context);
      } else if (workflow.apiEndpoint) {
        result = await this.executeViaAPI(workflow, input, context);
      } else {
        throw new Error(`No execution method available for workflow ${workflowId}`);
      }

      const executionResult: N8nExecutionResult = {
        success: true,
        executionId,
        data: result.data,
        duration: Date.now() - startTime,
        metadata: {
          workflowId,
          n8nExecutionId: result.executionId,
          externalServiceCalls: workflow.metadata.externalServices.length,
          credentialsUsed: this.extractCredentialsUsed(workflow)
        }
      };

      // Cache result for potential retrieval
      this.executionCache.set(executionId, executionResult);

      // Update context mesh with execution result
      this.coordinator.contextMesh.updateSharedState(
        context,
        `n8n-execution-${executionId}`,
        executionResult,
        'n8n-integration'
      );

      this.emit('workflow-executed', { workflow, result: executionResult, context });

      return executionResult;
    } catch (error) {
      const executionResult: N8nExecutionResult = {
        success: false,
        executionId,
        error: error.message,
        duration: Date.now() - startTime,
        metadata: {
          workflowId,
          externalServiceCalls: 0,
          credentialsUsed: []
        }
      };

      this.emit('workflow-error', { workflow, error, context });

      return executionResult;
    }
  }

  /**
   * Execute workflow via webhook
   */
  private async executeViaWebhook(workflow: N8nWorkflow, input: any, context: MCPContext): Promise<any> {
    const payload = {
      ...input,
      mcpContext: {
        sessionId: context.sessionId,
        userId: context.userId,
        workspaceId: context.workspaceId,
        timestamp: new Date().toISOString()
      }
    };

    const response = await fetch(workflow.webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-MCP-Session-ID': context.sessionId,
        'X-MCP-User-ID': context.userId
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      throw new Error(`Webhook execution failed: ${response.statusText}`);
    }

    const result = await response.json();

    return {
      data: result,
      executionId: result.executionId || `webhook-${Date.now()}`
    };
  }

  /**
   * Execute workflow via n8n API
   */
  private async executeViaAPI(workflow: N8nWorkflow, input: any, context: MCPContext): Promise<any> {
    const workflowId = workflow.id.replace('n8n-', '');
    const url = `${this.n8nBaseUrl}/api/v1/workflows/${workflowId}/execute`;

    const payload = {
      data: {
        ...input,
        mcpContext: {
          sessionId: context.sessionId,
          userId: context.userId,
          workspaceId: context.workspaceId
        }
      }
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'X-N8N-API-KEY': this.n8nApiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      throw new Error(`API execution failed: ${response.statusText}`);
    }

    const result = await response.json();

    return {
      data: result.data,
      executionId: result.executionId
    };
  }

  /**
   * Extract credentials used by workflow
   */
  private extractCredentialsUsed(workflow: N8nWorkflow): string[] {
    // This would analyze the workflow to determine which credentials are used
    // For now, return the external services as a proxy
    return workflow.metadata.externalServices;
  }

  /**
   * Set up webhook handlers for n8n callbacks
   */
  private async setupWebhookHandlers(): Promise<void> {
    // Register webhook endpoints for n8n to call back
    for (const workflow of this.workflows.values()) {
      if (workflow.executionMode === 'webhook') {
        const callbackUrl = `/webhooks/n8n/${workflow.id}/callback`;
        this.webhookEndpoints.set(workflow.id, callbackUrl);
        console.log(`📡 Registered webhook endpoint: ${callbackUrl}`);
      }
    }
  }

  /**
   * Handle webhook callback from n8n
   */
  async handleWebhookCallback(workflowId: string, payload: any): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      console.error(`Received callback for unknown workflow: ${workflowId}`);
      return;
    }

    console.log(`📨 Received n8n callback for workflow: ${workflow.name}`);

    // Update execution cache if we have the execution ID
    if (payload.executionId && this.executionCache.has(payload.executionId)) {
      const cachedResult = this.executionCache.get(payload.executionId)!;
      cachedResult.data = payload.data;
      cachedResult.metadata.n8nExecutionId = payload.n8nExecutionId;
    }

    // Emit event for any listeners
    this.emit('webhook-callback', { workflowId, payload });

    // Update context mesh if we have session information
    if (payload.mcpContext?.sessionId) {
      const context = await this.coordinator.getContext(
        payload.mcpContext.sessionId,
        payload.mcpContext.userId,
        payload.mcpContext.workspaceId
      );

      this.coordinator.contextMesh.updateSharedState(
        context,
        `n8n-callback-${workflowId}`,
        payload,
        'n8n-integration'
      );
    }
  }

  /**
   * Start periodic sync with n8n
   */
  private startPeriodicSync(): void {
    // Sync every 5 minutes
    setInterval(async () => {
      await this.syncWithN8n();
    }, 5 * 60 * 1000);
  }

  /**
   * Sync with n8n to discover new workflows and update existing ones
   */
  private async syncWithN8n(): Promise<void> {
    try {
      console.log('🔄 Syncing with n8n...');

      const previousWorkflowCount = this.workflows.size;
      await this.discoverN8nWorkflows();

      if (this.workflows.size > previousWorkflowCount) {
        console.log(`🆕 Discovered ${this.workflows.size - previousWorkflowCount} new n8n workflows`);
        await this.registerWorkflowCapabilities();
      }
    } catch (error) {
      console.error('Failed to sync with n8n:', error);
    }
  }

  /**
   * Get workflow by ID
   */
  getWorkflow(workflowId: string): N8nWorkflow | undefined {
    return this.workflows.get(workflowId);
  }

  /**
   * Get all workflows
   */
  getAllWorkflows(): N8nWorkflow[] {
    return Array.from(this.workflows.values());
  }

  /**
   * Get workflows by external service
   */
  getWorkflowsByService(serviceName: string): N8nWorkflow[] {
    return Array.from(this.workflows.values()).filter(workflow =>
      workflow.metadata.externalServices.includes(serviceName)
    );
  }

  /**
   * Get execution result
   */
  getExecutionResult(executionId: string): N8nExecutionResult | undefined {
    return this.executionCache.get(executionId);
  }

  /**
   * Get integration status
   */
  getStatus(): any {
    return {
      connected: true,
      n8nBaseUrl: this.n8nBaseUrl,
      totalWorkflows: this.workflows.size,
      activeWorkflows: Array.from(this.workflows.values()).filter(w => w.isActive).length,
      cachedExecutions: this.executionCache.size,
      webhookEndpoints: Array.from(this.webhookEndpoints.values()),
      workflows: Array.from(this.workflows.values()).map(w => ({
        id: w.id,
        name: w.name,
        isActive: w.isActive,
        executionMode: w.executionMode,
        externalServices: w.metadata.externalServices
      }))
    };
  }

  /**
   * Clean up resources
   */
  cleanup(): void {
    this.workflows.clear();
    this.executionCache.clear();
    this.webhookEndpoints.clear();
    this.removeAllListeners();
  }
}
