/**
 * N8n Credential Bridge
 * Manages credential synchronization between Supabase Vault and n8n credential store
 */

import { EventEmitter } from 'events';

export interface CredentialMapping {
  id: string;
  serviceName: string;
  supabaseVaultKeyId: string;
  n8nCredentialId: string;
  n8nCredentialType: string;
  syncDirection: 'supabase-to-n8n' | 'n8n-to-supabase' | 'bidirectional';
  lastSyncAt: Date;
  isActive: boolean;
}

export interface N8nCredential {
  id: string;
  name: string;
  type: string;
  data: any;
  nodesAccess: any[];
}

export interface CredentialSyncResult {
  success: boolean;
  credentialId: string;
  action: 'created' | 'updated' | 'deleted' | 'skipped';
  error?: string;
}

export class N8nCredentialBridge extends EventEmitter {
  private mappings: Map<string, CredentialMapping> = new Map();
  private n8nBaseUrl: string;
  private n8nApiKey: string;
  private credentialService: any; // Reference to Twenty's credential service
  private syncInterval: NodeJS.Timeout | null = null;

  constructor(credentialService: any) {
    super();
    this.credentialService = credentialService;
    this.n8nBaseUrl = process.env.N8N_BASE_URL || 'http://localhost:5678';
    this.n8nApiKey = process.env.N8N_API_KEY || '';
    
    this.initializeBridge();
  }

  private async initializeBridge(): Promise<void> {
    console.log('🔐 Initializing N8n Credential Bridge...');
    
    // Load existing mappings
    await this.loadCredentialMappings();
    
    // Discover unmapped credentials
    await this.discoverUnmappedCredentials();
    
    // Start periodic sync
    this.startPeriodicSync();
    
    console.log('✅ N8n Credential Bridge initialized');
  }

  /**
   * Load existing credential mappings from database
   */
  private async loadCredentialMappings(): Promise<void> {
    // In a real implementation, this would load from a database table
    // For now, we'll use environment-based configuration
    
    const mappingConfig = process.env.N8N_CREDENTIAL_MAPPINGS;
    if (mappingConfig) {
      try {
        const mappings = JSON.parse(mappingConfig);
        for (const mapping of mappings) {
          this.mappings.set(mapping.id, {
            ...mapping,
            lastSyncAt: new Date(mapping.lastSyncAt || Date.now()),
            isActive: mapping.isActive !== false
          });
        }
        console.log(`📋 Loaded ${this.mappings.size} credential mappings`);
      } catch (error) {
        console.error('Failed to parse credential mappings:', error);
      }
    }
  }

  /**
   * Discover unmapped credentials in both systems
   */
  private async discoverUnmappedCredentials(): Promise<void> {
    try {
      // Get n8n credentials
      const n8nCredentials = await this.getN8nCredentials();
      
      // Get Supabase credentials (this would need to be implemented)
      // const supabaseCredentials = await this.getSupabaseCredentials();
      
      // Find unmapped n8n credentials
      for (const n8nCred of n8nCredentials) {
        const existingMapping = Array.from(this.mappings.values())
          .find(m => m.n8nCredentialId === n8nCred.id);
        
        if (!existingMapping) {
          console.log(`🔍 Found unmapped n8n credential: ${n8nCred.name} (${n8nCred.type})`);
          // Could auto-create mapping or notify admin
        }
      }
    } catch (error) {
      console.error('Failed to discover unmapped credentials:', error);
    }
  }

  /**
   * Get all n8n credentials
   */
  private async getN8nCredentials(): Promise<N8nCredential[]> {
    try {
      const response = await fetch(`${this.n8nBaseUrl}/api/v1/credentials`, {
        headers: {
          'X-N8N-API-KEY': this.n8nApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch n8n credentials: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data || [];
    } catch (error) {
      console.error('Failed to get n8n credentials:', error);
      return [];
    }
  }

  /**
   * Create credential mapping
   */
  async createCredentialMapping(
    serviceName: string,
    supabaseVaultKeyId: string,
    n8nCredentialType: string,
    syncDirection: 'supabase-to-n8n' | 'n8n-to-supabase' | 'bidirectional' = 'supabase-to-n8n'
  ): Promise<CredentialMapping> {
    const mappingId = `mapping-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const mapping: CredentialMapping = {
      id: mappingId,
      serviceName,
      supabaseVaultKeyId,
      n8nCredentialId: '', // Will be set when credential is created in n8n
      n8nCredentialType,
      syncDirection,
      lastSyncAt: new Date(),
      isActive: true
    };

    this.mappings.set(mappingId, mapping);
    
    // Perform initial sync
    await this.syncCredential(mappingId);
    
    this.emit('mapping-created', mapping);
    
    return mapping;
  }

  /**
   * Sync a specific credential
   */
  async syncCredential(mappingId: string): Promise<CredentialSyncResult> {
    const mapping = this.mappings.get(mappingId);
    if (!mapping || !mapping.isActive) {
      return {
        success: false,
        credentialId: mappingId,
        action: 'skipped',
        error: 'Mapping not found or inactive'
      };
    }

    try {
      let result: CredentialSyncResult;

      switch (mapping.syncDirection) {
        case 'supabase-to-n8n':
          result = await this.syncSupabaseToN8n(mapping);
          break;
        case 'n8n-to-supabase':
          result = await this.syncN8nToSupabase(mapping);
          break;
        case 'bidirectional':
          // For bidirectional, prefer Supabase as source of truth
          result = await this.syncSupabaseToN8n(mapping);
          break;
        default:
          throw new Error(`Unknown sync direction: ${mapping.syncDirection}`);
      }

      if (result.success) {
        mapping.lastSyncAt = new Date();
        this.emit('credential-synced', { mapping, result });
      }

      return result;
    } catch (error) {
      const result: CredentialSyncResult = {
        success: false,
        credentialId: mappingId,
        action: 'skipped',
        error: error.message
      };

      this.emit('credential-sync-error', { mapping, error });
      
      return result;
    }
  }

  /**
   * Sync credential from Supabase to n8n
   */
  private async syncSupabaseToN8n(mapping: CredentialMapping): Promise<CredentialSyncResult> {
    // Get credential from Supabase Vault
    const supabaseCredential = await this.getSupabaseCredential(
      mapping.serviceName,
      mapping.supabaseVaultKeyId
    );

    if (!supabaseCredential) {
      return {
        success: false,
        credentialId: mapping.id,
        action: 'skipped',
        error: 'Credential not found in Supabase'
      };
    }

    // Transform credential data for n8n format
    const n8nCredentialData = this.transformCredentialForN8n(
      supabaseCredential,
      mapping.n8nCredentialType
    );

    // Create or update credential in n8n
    if (mapping.n8nCredentialId) {
      // Update existing
      await this.updateN8nCredential(mapping.n8nCredentialId, n8nCredentialData);
      return {
        success: true,
        credentialId: mapping.id,
        action: 'updated'
      };
    } else {
      // Create new
      const n8nCredential = await this.createN8nCredential(
        mapping.serviceName,
        mapping.n8nCredentialType,
        n8nCredentialData
      );
      
      mapping.n8nCredentialId = n8nCredential.id;
      
      return {
        success: true,
        credentialId: mapping.id,
        action: 'created'
      };
    }
  }

  /**
   * Sync credential from n8n to Supabase
   */
  private async syncN8nToSupabase(mapping: CredentialMapping): Promise<CredentialSyncResult> {
    if (!mapping.n8nCredentialId) {
      return {
        success: false,
        credentialId: mapping.id,
        action: 'skipped',
        error: 'No n8n credential ID specified'
      };
    }

    // Get credential from n8n
    const n8nCredential = await this.getN8nCredential(mapping.n8nCredentialId);
    
    if (!n8nCredential) {
      return {
        success: false,
        credentialId: mapping.id,
        action: 'skipped',
        error: 'Credential not found in n8n'
      };
    }

    // Transform credential data for Supabase format
    const supabaseCredentialData = this.transformCredentialForSupabase(
      n8nCredential,
      mapping.serviceName
    );

    // Store in Supabase Vault
    await this.storeSupabaseCredential(
      mapping.serviceName,
      mapping.supabaseVaultKeyId,
      supabaseCredentialData
    );

    return {
      success: true,
      credentialId: mapping.id,
      action: 'updated'
    };
  }

  /**
   * Get credential from Supabase
   */
  private async getSupabaseCredential(serviceName: string, vaultKeyId: string): Promise<any> {
    // This would use the credential service to get decrypted credential
    try {
      return await this.credentialService.getCredential('system', serviceName, vaultKeyId);
    } catch (error) {
      console.error(`Failed to get Supabase credential for ${serviceName}:`, error);
      return null;
    }
  }

  /**
   * Store credential in Supabase
   */
  private async storeSupabaseCredential(serviceName: string, vaultKeyId: string, data: any): Promise<void> {
    // This would use the credential service to store encrypted credential
    await this.credentialService.createCredential('system', serviceName, vaultKeyId, JSON.stringify(data), vaultKeyId);
  }

  /**
   * Transform credential data for n8n format
   */
  private transformCredentialForN8n(supabaseCredential: any, n8nCredentialType: string): any {
    // Transform based on credential type
    const transformers: Record<string, (data: any) => any> = {
      'gmailOAuth2Api': (data) => ({
        clientId: data.client_id,
        clientSecret: data.client_secret,
        refreshToken: data.refresh_token
      }),
      'slackApi': (data) => ({
        accessToken: data.access_token
      }),
      'hubspotApi': (data) => ({
        apiKey: data.api_key
      }),
      'googleSheetsOAuth2Api': (data) => ({
        clientId: data.client_id,
        clientSecret: data.client_secret,
        refreshToken: data.refresh_token
      })
    };

    const transformer = transformers[n8nCredentialType];
    if (transformer) {
      return transformer(JSON.parse(supabaseCredential.decrypted_secret));
    }

    // Default transformation
    return JSON.parse(supabaseCredential.decrypted_secret);
  }

  /**
   * Transform credential data for Supabase format
   */
  private transformCredentialForSupabase(n8nCredential: N8nCredential, serviceName: string): any {
    // Transform n8n credential data to standard format
    return {
      service: serviceName,
      type: n8nCredential.type,
      data: n8nCredential.data,
      syncedFrom: 'n8n',
      syncedAt: new Date().toISOString()
    };
  }

  /**
   * Get n8n credential by ID
   */
  private async getN8nCredential(credentialId: string): Promise<N8nCredential | null> {
    try {
      const response = await fetch(`${this.n8nBaseUrl}/api/v1/credentials/${credentialId}`, {
        headers: {
          'X-N8N-API-KEY': this.n8nApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to get n8n credential: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Failed to get n8n credential ${credentialId}:`, error);
      return null;
    }
  }

  /**
   * Create n8n credential
   */
  private async createN8nCredential(name: string, type: string, data: any): Promise<N8nCredential> {
    const payload = {
      name: `${name}-mcp-sync`,
      type,
      data
    };

    const response = await fetch(`${this.n8nBaseUrl}/api/v1/credentials`, {
      method: 'POST',
      headers: {
        'X-N8N-API-KEY': this.n8nApiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      throw new Error(`Failed to create n8n credential: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Update n8n credential
   */
  private async updateN8nCredential(credentialId: string, data: any): Promise<void> {
    const response = await fetch(`${this.n8nBaseUrl}/api/v1/credentials/${credentialId}`, {
      method: 'PATCH',
      headers: {
        'X-N8N-API-KEY': this.n8nApiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ data })
    });

    if (!response.ok) {
      throw new Error(`Failed to update n8n credential: ${response.statusText}`);
    }
  }

  /**
   * Start periodic sync
   */
  private startPeriodicSync(): void {
    // Sync every 30 minutes
    this.syncInterval = setInterval(async () => {
      await this.syncAllCredentials();
    }, 30 * 60 * 1000);
  }

  /**
   * Sync all active credential mappings
   */
  async syncAllCredentials(): Promise<CredentialSyncResult[]> {
    console.log('🔄 Syncing all credentials...');

    const results: CredentialSyncResult[] = [];

    for (const mapping of this.mappings.values()) {
      if (mapping.isActive) {
        const result = await this.syncCredential(mapping.id);
        results.push(result);
      }
    }

    const successful = results.filter(r => r.success).length;
    console.log(`✅ Synced ${successful}/${results.length} credentials`);

    return results;
  }

  /**
   * Get credential mapping by service
   */
  getMappingByService(serviceName: string): CredentialMapping | undefined {
    return Array.from(this.mappings.values())
      .find(mapping => mapping.serviceName === serviceName);
  }

  /**
   * Get all credential mappings
   */
  getAllMappings(): CredentialMapping[] {
    return Array.from(this.mappings.values());
  }

  /**
   * Delete credential mapping
   */
  async deleteCredentialMapping(mappingId: string): Promise<boolean> {
    const mapping = this.mappings.get(mappingId);
    if (!mapping) {
      return false;
    }

    // Optionally delete from n8n as well
    if (mapping.n8nCredentialId) {
      try {
        await fetch(`${this.n8nBaseUrl}/api/v1/credentials/${mapping.n8nCredentialId}`, {
          method: 'DELETE',
          headers: {
            'X-N8N-API-KEY': this.n8nApiKey
          }
        });
      } catch (error) {
        console.error(`Failed to delete n8n credential ${mapping.n8nCredentialId}:`, error);
      }
    }

    this.mappings.delete(mappingId);
    this.emit('mapping-deleted', mapping);

    return true;
  }

  /**
   * Get bridge status
   */
  getStatus(): any {
    const mappings = Array.from(this.mappings.values());

    return {
      connected: true,
      n8nBaseUrl: this.n8nBaseUrl,
      totalMappings: mappings.length,
      activeMappings: mappings.filter(m => m.isActive).length,
      lastSyncTimes: mappings.reduce((acc, mapping) => {
        acc[mapping.serviceName] = mapping.lastSyncAt;
        return acc;
      }, {} as Record<string, Date>),
      mappings: mappings.map(m => ({
        id: m.id,
        serviceName: m.serviceName,
        syncDirection: m.syncDirection,
        isActive: m.isActive,
        lastSyncAt: m.lastSyncAt
      }))
    };
  }

  /**
   * Clean up resources
   */
  cleanup(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    this.mappings.clear();
    this.removeAllListeners();
  }
}
