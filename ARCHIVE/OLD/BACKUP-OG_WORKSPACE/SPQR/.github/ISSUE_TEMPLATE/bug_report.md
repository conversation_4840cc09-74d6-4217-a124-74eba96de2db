---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''
---

## Bug Description
A clear and concise description of what the bug is.

## Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
A clear and concise description of what actually happened.

## Screenshots
If applicable, add screenshots to help explain your problem.

## Environment
- OS: [e.g. Ubuntu 22.04, Windows 11]
- Node.js version: [e.g. 18.12.1]
- SPQR version: [e.g. 3.0.0]
- Docker version (if applicable): [e.g. 24.0.5]

## Additional Context
Add any other context about the problem here, such as:
- API keys being used (DO NOT share actual keys)
- Configuration settings
- Error logs
- Network conditions
