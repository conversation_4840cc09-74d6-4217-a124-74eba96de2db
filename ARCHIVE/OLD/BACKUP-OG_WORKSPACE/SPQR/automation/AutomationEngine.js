/**
 * AutomationEngine.js
 * 
 * Automatisierungs-Engine für regelbasierte Automatisierung.
 */

class AutomationEngine {
  constructor(brain) {
    this.brain = brain;
    this.rules = [];
    this.eventBus = brain.eventBus;
  }
  
  // Initialisiert die Automatisierungs-Engine
  async initialize(config) {
    console.log("Automatisierungs-Engine wird initialisiert...");
    
    // Regeln laden
    if (config.rules) {
      for (const ruleConfig of config.rules) {
        this.addRule(new AutomationRule(ruleConfig));
      }
    }
    
    // Event-Listener registrieren
    this.setupEventListeners();
    
    console.log(`Automatisierungs-Engine initialisiert mit ${this.rules.length} Regeln`);
    return this;
  }
  
  // Fügt eine neue Regel hinzu
  addRule(rule) {
    this.rules.push(rule);
    
    // Regeln nach Priorität sortieren
    this.rules.sort((a, b) => b.priority - a.priority);
    
    console.log(`Regel hinzugefügt: ${rule.name}`);
    return rule;
  }
  
  // Entfernt eine Regel
  removeRule(ruleId) {
    const index = this.rules.findIndex(rule => rule.id === ruleId);
    
    if (index !== -1) {
      const rule = this.rules[index];
      this.rules.splice(index, 1);
      console.log(`Regel entfernt: ${rule.name}`);
      return true;
    }
    
    return false;
  }
  
  // Richtet Event-Listener ein
  setupEventListeners() {
    // Auf alle Events hören
    this.eventBus.subscribe('*', (event) => {
      this.processEvent(event);
    });
  }
  
  // Verarbeitet ein Event und prüft, ob Regeln ausgelöst werden
  async processEvent(event) {
    console.log(`Automatisierungs-Engine verarbeitet Event: ${event.type}`);
    
    const context = {
      timestamp: new Date(),
      environment: this.brain.getEnvironmentContext()
    };
    
    // Passende Regeln finden
    const matchingRules = this.rules.filter(rule => 
      rule.enabled && rule.matches(event, context)
    );
    
    if (matchingRules.length === 0) {
      return;
    }
    
    console.log(`${matchingRules.length} passende Regeln gefunden für Event ${event.type}`);
    
    // Regeln ausführen
    for (const rule of matchingRules) {
      try {
        await this.executeRule(rule, event, context);
      } catch (error) {
        console.error(`Fehler bei Ausführung der Regel ${rule.name}:`, error);
      }
    }
  }
  
  // Führt eine Regel aus
  async executeRule(rule, event, context) {
    console.log(`Führe Regel aus: ${rule.name}`);
    
    // Event für Regel-Ausführung auslösen
    this.eventBus.emit('rule_triggered', {
      rule,
      event,
      context
    });
    
    // Aktionen ausführen
    const results = [];
    
    for (const action of rule.actions) {
      try {
        const result = await this.executeAction(action, event, context);
        results.push({
          action,
          success: true,
          result
        });
      } catch (error) {
        console.error(`Fehler bei Aktion ${action.type} in Regel ${rule.name}:`, error);
        
        results.push({
          action,
          success: false,
          error: error.message
        });
        
        // Bei Fehler abbrechen, wenn so konfiguriert
        if (rule.stopOnError) {
          break;
        }
      }
    }
    
    return {
      rule,
      success: results.every(r => r.success),
      results
    };
  }
  
  // Führt eine Aktion aus
  async executeAction(action, event, context) {
    console.log(`Führe Aktion aus: ${action.type}`);
    
    // Werte in der Aktion auflösen
    const resolvedAction = this.resolveActionValues(action, event, context);
    
    // Je nach Aktionstyp unterschiedlich handeln
    switch (resolvedAction.type) {
      case 'notify':
        return await this.brain.sendNotification(
          resolvedAction.recipient,
          resolvedAction.message,
          resolvedAction.options
        );
      
      case 'create_task':
        return await this.brain.createTask(resolvedAction.taskDetails);
      
      case 'api_call':
        return await this.brain.executeApiCall(
          resolvedAction.service,
          resolvedAction.endpoint,
          resolvedAction.method,
          resolvedAction.parameters
        );
      
      case 'run_process':
        return await this.brain.executeProcess(
          resolvedAction.processId,
          resolvedAction.inputs
        );
      
      case 'delegate_to_agent':
        return await this.brain.delegateToAgent(
          resolvedAction.agent,
          resolvedAction.request,
          { event, context }
        );
      
      default:
        throw new Error(`Unbekannter Aktionstyp: ${resolvedAction.type}`);
    }
  }
  
  // Löst Werte in einer Aktion auf
  resolveActionValues(action, event, context) {
    const resolved = { ...action };
    
    // Rekursiv durch alle Eigenschaften gehen
    for (const [key, value] of Object.entries(resolved)) {
      if (typeof value === 'string') {
        // Platzhalter auflösen
        if (value.startsWith('{{') && value.endsWith('}}')) {
          const path = value.slice(2, -2).trim();
          resolved[key] = this.getValueByPath(path, { event, context });
        }
      } else if (typeof value === 'object' && value !== null) {
        // Rekursiv für Objekte und Arrays
        resolved[key] = this.resolveActionValues(value, event, context);
      }
    }
    
    return resolved;
  }
  
  // Holt einen Wert anhand eines Pfads
  getValueByPath(path, data) {
    const parts = path.split('.');
    let current = data;
    
    for (const part of parts) {
      if (current === undefined || current === null) {
        return undefined;
      }
      
      current = current[part];
    }
    
    return current;
  }
}

// Regelklasse
class AutomationRule {
  constructor(config) {
    this.id = config.id || `rule-${Date.now()}`;
    this.name = config.name;
    this.description = config.description;
    this.trigger = config.trigger;
    this.conditions = config.conditions || [];
    this.actions = config.actions || [];
    this.enabled = config.enabled !== false;
    this.priority = config.priority || 0;
    this.stopOnError = config.stopOnError !== false;
    this.createdAt = config.createdAt || new Date();
    this.updatedAt = config.updatedAt || new Date();
  }
  
  // Prüft, ob die Regel auf ein Event zutrifft
  matches(event, context) {
    // Trigger prüfen
    if (this.trigger.type === 'event' && this.trigger.eventType !== event.type) {
      return false;
    }
    
    // Bedingungen prüfen
    for (const condition of this.conditions) {
      if (!this.evaluateCondition(condition, event, context)) {
        return false;
      }
    }
    
    return true;
  }
  
  // Wertet eine Bedingung aus
  evaluateCondition(condition, event, context) {
    const leftValue = this.resolveValue(condition.left, event, context);
    const rightValue = this.resolveValue(condition.right, event, context);
    
    switch (condition.operator) {
      case 'equals':
        return leftValue === rightValue;
      case 'not_equals':
        return leftValue !== rightValue;
      case 'contains':
        return String(leftValue).includes(String(rightValue));
      case 'greater_than':
        return leftValue > rightValue;
      case 'less_than':
        return leftValue < rightValue;
      case 'in':
        return Array.isArray(rightValue) && rightValue.includes(leftValue);
      default:
        return false;
    }
  }
  
  // Löst einen Wert auf (kann statisch oder dynamisch sein)
  resolveValue(value, event, context) {
    if (typeof value !== 'string') {
      return value;
    }
    
    // Platzhalter auflösen
    if (value.startsWith('{{') && value.endsWith('}}')) {
      const path = value.slice(2, -2).trim();
      return this.getValueByPath(path, { event, context });
    }
    
    return value;
  }
  
  // Holt einen Wert anhand eines Pfads
  getValueByPath(path, data) {
    const parts = path.split('.');
    let current = data;
    
    for (const part of parts) {
      if (current === undefined || current === null) {
        return undefined;
      }
      
      current = current[part];
    }
    
    return current;
  }
}

module.exports = {
  AutomationEngine,
  AutomationRule
};
