              className={`pb-2 px-1 ${activeTab === 'settings' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
              onClick={() => setActiveTab('settings')}
            >
              Einstellungen
            </button>
            <button 
              className={`pb-2 px-1 ${activeTab === 'branding' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
              onClick={() => setActiveTab('branding')}
            >
              Branding
            </button>
            <button 
              className={`pb-2 px-1 ${activeTab === 'usage' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
              onClick={() => setActiveTab('usage')}
            >
              Nutzung
            </button>
          </div>
        </div>
        
        {/* Content based on active tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Basic Info Card */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Ressourcenkapazitäten</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Maximale Benutzeranzahl</label>
                <input 
                  type="number" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={selectedTenant.maxUsers}
                  min="1"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Speicherkapazität (GB)</label>
                <input 
                  type="number" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={selectedTenant.maxStorage / 1073741824}
                  min="1"
                  step="1"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Token-Limit pro Monat (Millionen)</label>
                <input 
                  type="number" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={selectedTenant.aiUsage.totalTokens / 1000000}
                  min="1"
                  step="1"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </>
    );
  };
  
  // Render the appropriate view
  const renderView = () => {
    switch (view) {
      case 'detail':
        return renderTenantDetail();
      case 'create':
        return renderCreateTenantForm();
      case 'edit':
        return renderEditTenantForm();
      case 'list':
      default:
        return renderTenantList();
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {renderView()}
    </div>
  );
};

export default MultiTenantAdmin;>Grundinformationen</CardTitle>
                  <Button
                    variant="outline"
                    className="flex items-center gap-1"
                    onClick={() => handleEditTenant(selectedTenant)}
                  >
                    <Edit className="h-4 w-4" />
                    Bearbeiten
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h3 className="font-medium mb-3">Tenant Details</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Name:</span>
                        <span>{selectedTenant.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Slug:</span>
                        <span className="font-mono">{selectedTenant.slug}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Domain:</span>
                        <a href={`https://${selectedTenant.domain}`} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline flex items-center">
                          {selectedTenant.domain}
                          <ArrowUpRight className="h-3 w-3 ml-1" />
                        </a>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Plan:</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${getPlanColor(selectedTenant.plan)}`}>
                          {getPlanName(selectedTenant.plan)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium mb-3">Zeitraum</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Erstellt am:</span>
                        <span>{new Date(selectedTenant.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Läuft ab:</span>
                        <span>{new Date(selectedTenant.expiresAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Status:</span>
                        <span className="capitalize">{selectedTenant.status}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium mb-3">Administatoren</h3>
                    <div className="space-y-2">
                      {selectedTenant.administrators.map(admin => (
                        <div key={admin.id} className="flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium">{admin.name}</div>
                            <div className="text-xs text-gray-500">{admin.email}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Benutzer</p>
                      <h3 className="text-2xl font-bold">{selectedTenant.userCount} / {selectedTenant.maxUsers}</h3>
                    </div>
                    <Users className="h-8 w-8 text-blue-500" />
                  </div>
                  <div className="w-full h-1.5 bg-gray-200 rounded-full mt-2">
                    <div 
                      className="h-1.5 bg-blue-500 rounded-full" 
                      style={{ width: `${Math.min(100, (selectedTenant.userCount / selectedTenant.maxUsers) * 100)}%` }}
                    ></div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Speichernutzung</p>
                      <h3 className="text-2xl font-bold">{formatBytes(selectedTenant.storageUsed)}</h3>
                    </div>
                    <Database className="h-8 w-8 text-green-500" />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0</span>
                    <span>{formatBytes(selectedTenant.maxStorage)}</span>
                  </div>
                  <div className="w-full h-1.5 bg-gray-200 rounded-full mt-1">
                    <div 
                      className={`h-1.5 rounded-full ${selectedTenant.storageUsed / selectedTenant.maxStorage > 0.9 ? 'bg-red-500' : selectedTenant.storageUsed / selectedTenant.maxStorage > 0.7 ? 'bg-yellow-500' : 'bg-green-500'}`}
                      style={{ width: `${Math.min(100, (selectedTenant.storageUsed / selectedTenant.maxStorage) * 100)}%` }}
                    ></div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Token-Nutzung</p>
                      <h3 className="text-2xl font-bold">{(selectedTenant.aiUsage.monthlyTokens / 1000000).toFixed(1)}M</h3>
                    </div>
                    <Package className="h-8 w-8 text-purple-500" />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0</span>
                    <span>{(selectedTenant.aiUsage.totalTokens / 1000000).toFixed(1)}M</span>
                  </div>
                  <div className="w-full h-1.5 bg-gray-200 rounded-full mt-1">
                    <div 
                      className="h-1.5 bg-purple-500 rounded-full"
                      style={{ width: `${(selectedTenant.aiUsage.monthlyTokens / selectedTenant.aiUsage.totalTokens) * 100}%` }}
                    ></div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">API-Aufrufe</p>
                      <h3 className="text-2xl font-bold">{selectedTenant.aiUsage.monthlyApiCalls.toLocaleString()}</h3>
                    </div>
                    <ArrowUpRight className="h-8 w-8 text-yellow-500" />
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Für diesen Monat
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Features Card */}
            <Card>
              <CardHeader>
                <CardTitle>Aktivierte Funktionen</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div className={`p-4 border rounded-lg text-center ${selectedTenant.settings.features.aiAgent ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'}`}>
                    <div className={`mx-auto w-8 h-8 rounded-full flex items-center justify-center mb-2 ${selectedTenant.settings.features.aiAgent ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Brain className={`h-5 w-5 ${selectedTenant.settings.features.aiAgent ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <div className="font-medium text-sm">AI-Agent</div>
                    <div className="mt-1">
                      {selectedTenant.settings.features.aiAgent ? (
                        <CheckCircle className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-gray-400 mx-auto" />
                      )}
                    </div>
                  </div>
                  
                  <div className={`p-4 border rounded-lg text-center ${selectedTenant.settings.features.telephony ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'}`}>
                    <div className={`mx-auto w-8 h-8 rounded-full flex items-center justify-center mb-2 ${selectedTenant.settings.features.telephony ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <div className={`h-5 w-5 ${selectedTenant.settings.features.telephony ? 'text-green-600' : 'text-gray-400'}`}>📞</div>
                    </div>
                    <div className="font-medium text-sm">Telefonie</div>
                    <div className="mt-1">
                      {selectedTenant.settings.features.telephony ? (
                        <CheckCircle className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-gray-400 mx-auto" />
                      )}
                    </div>
                  </div>
                  
                  <div className={`p-4 border rounded-lg text-center ${selectedTenant.settings.features.apiAccess ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'}`}>
                    <div className={`mx-auto w-8 h-8 rounded-full flex items-center justify-center mb-2 ${selectedTenant.settings.features.apiAccess ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Code className={`h-5 w-5 ${selectedTenant.settings.features.apiAccess ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <div className="font-medium text-sm">API-Zugriff</div>
                    <div className="mt-1">
                      {selectedTenant.settings.features.apiAccess ? (
                        <CheckCircle className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-gray-400 mx-auto" />
                      )}
                    </div>
                  </div>
                  
                  <div className={`p-4 border rounded-lg text-center ${selectedTenant.settings.features.analytics ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'}`}>
                    <div className={`mx-auto w-8 h-8 rounded-full flex items-center justify-center mb-2 ${selectedTenant.settings.features.analytics ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <BarChart4 className={`h-5 w-5 ${selectedTenant.settings.features.analytics ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <div className="font-medium text-sm">Analytics</div>
                    <div className="mt-1">
                      {selectedTenant.settings.features.analytics ? (
                        <CheckCircle className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-gray-400 mx-auto" />
                      )}
                    </div>
                  </div>
                  
                  <div className={`p-4 border rounded-lg text-center ${selectedTenant.settings.features.multiUser ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'}`}>
                    <div className={`mx-auto w-8 h-8 rounded-full flex items-center justify-center mb-2 ${selectedTenant.settings.features.multiUser ? 'bg-green-100' : 'bg-gray-100'}`}>
                      <Users className={`h-5 w-5 ${selectedTenant.settings.features.multiUser ? 'text-green-600' : 'text-gray-400'}`} />
                    </div>
                    <div className="font-medium text-sm">Multi-User</div>
                    <div className="mt-1">
                      {selectedTenant.settings.features.multiUser ? (
                        <CheckCircle className="h-4 w-4 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-4 w-4 text-gray-400 mx-auto" />
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Security Settings Card */}
            <Card>
              <CardHeader>
                <CardTitle>Sicherheitseinstellungen</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <div className={`mt-0.5 rounded-full w-4 h-4 flex items-center justify-center ${selectedTenant.settings.security.mfaRequired ? 'bg-green-100' : 'bg-gray-100'}`}>
                          {selectedTenant.settings.security.mfaRequired ? (
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          ) : (
                            <X className="h-3 w-3 text-gray-400" />
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium">MFA erforderlich</h3>
                          <p className="text-sm text-gray-600">Alle Benutzer müssen die Zwei-Faktor-Authentifizierung aktivieren</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <div className={`mt-0.5 rounded-full w-4 h-4 flex items-center justify-center ${selectedTenant.settings.security.ssoEnabled ? 'bg-green-100' : 'bg-gray-100'}`}>
                          {selectedTenant.settings.security.ssoEnabled ? (
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          ) : (
                            <X className="h-3 w-3 text-gray-400" />
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium">SSO aktiviert</h3>
                          <p className="text-sm text-gray-600">Single Sign-On mit externen Identitätsanbietern</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-medium">Datenaufbewahrung</h3>
                        <p className="text-sm text-gray-600">{selectedTenant.settings.security.dataRetention} Tage</p>
                      </div>
                      
                      <div>
                        <h3 className="font-medium">IP-Einschränkungen</h3>
                        {selectedTenant.settings.security.ipRestrictions.length > 0 ? (
                          <div className="flex flex-wrap gap-2 mt-2">
                            {selectedTenant.settings.security.ipRestrictions.map((ip, index) => (
                              <span key={index} className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-mono">
                                {ip}
                              </span>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-600">Keine Einschränkungen konfiguriert</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Action Buttons */}
            <div className="flex gap-3 justify-end">
              <Button variant="outline" className="flex items-center gap-1">
                <FileCheck className="h-4 w-4" />
                Compliance-Prüfung
              </Button>
              
              <Button variant="outline" className="flex items-center gap-1">
                <ArrowUpRight className="h-4 w-4" />
                Als Tenant anmelden
              </Button>
              
              {selectedTenant.status === 'active' ? (
                <Button variant="outline" className="flex items-center gap-1 text-yellow-600">
                  <AlertCircle className="h-4 w-4" />
                  Tenant sperren
                </Button>
              ) : selectedTenant.status === 'suspended' ? (
                <Button variant="outline" className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  Tenant entsperren
                </Button>
              ) : null}
            </div>
          </div>
        )}
        
        {activeTab === 'users' && (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Benutzer</CardTitle>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1">
                  <Plus className="h-4 w-4" />
                  Benutzer hinzufügen
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <h3 className="font-medium mb-1">Benutzerübersicht</h3>
                <p>Hier würde eine Liste aller Benutzer im ausgewählten Tenant angezeigt werden.</p>
              </div>
            </CardContent>
          </Card>
        )}
        
        {activeTab === 'settings' && (
          <Card>
            <CardHeader>
              <CardTitle>Tenant-Einstellungen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <Settings className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <h3 className="font-medium mb-1">Einstellungen</h3>
                <p>Hier würden alle konfigurierbaren Einstellungen für den Tenant angezeigt werden.</p>
              </div>
            </CardContent>
          </Card>
        )}
        
        {activeTab === 'branding' && (
          <Card>
            <CardHeader>
              <CardTitle>Branding</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <Palette className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <h3 className="font-medium mb-1">Branding-Einstellungen</h3>
                <p>Hier würden Logo, Farbschema und andere Branding-Elemente des Tenants verwaltet werden.</p>
              </div>
            </CardContent>
          </Card>
        )}
        
        {activeTab === 'usage' && (
          <Card>
            <CardHeader>
              <CardTitle>Nutzungsmetriken</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <BarChart4 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <h3 className="font-medium mb-1">Nutzungsstatistiken</h3>
                <p>Hier würden detaillierte Nutzungsmetriken und Auslastungsstatistiken angezeigt werden.</p>
              </div>
            </CardContent>
          </Card>
        )}
      </>
    );
  };
  
  const renderCreateTenantForm = () => {
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToList}>
            Abbrechen
          </Button>
          <h1 className="text-2xl font-bold">Neuen Tenant erstellen</h1>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Tenant-Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input type="text" className="w-full p-2 border rounded-md" placeholder="z.B. Acme Corporation" />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                  <div className="flex items-center">
                    <input type="text" className="w-full p-2 border rounded-md" placeholder="acme" />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Wird für die Datenbank-Identifikation verwendet</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Domain</label>
                  <div className="flex items-center">
                    <input type="text" className="w-full p-2 border rounded-md" placeholder="z.B. acme.example.com" />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Optional: Für benutzerdefinierte Domains</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Plan</label>
                <select className="w-full p-2 border rounded-md">
                  <option value="enterprise">Enterprise</option>
                  <option value="professional">Professional</option>
                  <option value="basic">Basic</option>
                  <option value="free">Free</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Administrator</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input type="text" className="w-full p-2 border rounded-md" placeholder="Name" />
                  <input type="email" className="w-full p-2 border rounded-md" placeholder="E-Mail" />
                </div>
                <p className="text-xs text-gray-500 mt-1">Der erste Administrator für diesen Tenant</p>
              </div>
            </div>
            
            <div className="flex justify-end mt-6">
              <Button variant="outline" className="mr-2" onClick={handleBackToList}>
                Abbrechen
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                Tenant erstellen
              </Button>
            </div>
          </CardContent>
        </Card>
      </>
    );
  };
  
  const renderEditTenantForm = () => {
    if (!selectedTenant) return null;
    
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={() => setView('detail')}>
            Abbrechen
          </Button>
          <h1 className="text-2xl font-bold">{selectedTenant.name} bearbeiten</h1>
        </div>
        
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Tenant-Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input 
                  type="text" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={selectedTenant.name}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                  <div className="flex items-center">
                    <input 
                      type="text" 
                      className="w-full p-2 border rounded-md" 
                      defaultValue={selectedTenant.slug}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Wird für die Datenbank-Identifikation verwendet</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Domain</label>
                  <div className="flex items-center">
                    <input 
                      type="text" 
                      className="w-full p-2 border rounded-md" 
                      defaultValue={selectedTenant.domain}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Optional: Für benutzerdefinierte Domains</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Plan</label>
                <select 
                  className="w-full p-2 border rounded-md"
                  defaultValue={selectedTenant.plan}
                >
                  <option value="enterprise">Enterprise</option>
                  <option value="professional">Professional</option>
                  <option value="basic">Basic</option>
                  <option value="free">Free</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select 
                  className="w-full p-2 border rounded-md"
                  defaultValue={selectedTenant.status}
                >
                  <option value="active">Aktiv</option>
                  <option value="pending">Ausstehend</option>
                  <option value="suspended">Gesperrt</option>
                  <option value="expired">Abgelaufen</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ablaufdatum</label>
                <input 
                  type="date" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={new Date(selectedTenant.expiresAt).toISOString().split('T')[0]}
                />
              </div>
            </div>
            
            <div className="flex justify-end mt-6">
              <Button variant="outline" className="mr-2" onClick={() => setView('detail')}>
                Abbrechen
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                Änderungen speichern
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitleimport React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Building, Users, Settings, Database, Package, 
  BriefcaseBusiness, BarChart4, Globe, Palette, 
  User, ArrowUpRight, Plus, Search, Filter, X, 
  Edit, MoreHorizontal, CheckCircle, AlertCircle, FileCheck
} from 'lucide-react';

const MultiTenantAdmin = () => {
  const [loading, setLoading] = useState(true);
  const [tenants, setTenants] = useState([]);
  const [view, setView] = useState('list'); // list, detail, create, edit
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [activeTab, setActiveTab] = useState('overview'); // overview, users, settings, storage, etc.
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  
  useEffect(() => {
    // Mock data loading
    setTimeout(() => {
      setTenants([
        {
          id: 1,
          name: 'Acme Corporation',
          slug: 'acme',
          domain: 'acme.example.com',
          plan: 'enterprise',
          status: 'active',
          createdAt: '2025-01-10T09:12:45',
          expiresAt: '2026-01-10T09:12:45',
          userCount: 125,
          maxUsers: 200,
          storageUsed: 8570000000, // in bytes
          maxStorage: 10737418240, // 10GB in bytes
          aiUsage: {
            monthlyTokens: 12450000,
            remainingTokens: 7550000,
            totalTokens: 20000000,
            monthlyApiCalls: 8720,
          },
          settings: {
            branding: {
              logoUrl: 'https://example.com/logo.png',
              primaryColor: '#3B82F6',
              secondaryColor: '#1E40AF',
              accentColor: '#EF4444',
            },
            features: {
              aiAgent: true,
              telephony: true,
              apiAccess: true,
              analytics: true,
              multiUser: true
            },
            security: {
              mfaRequired: true,
              ssoEnabled: true,
              dataRetention: 730, // days
              ipRestrictions: ['***********/24', '10.0.0.0/16']
            },
            notifications: {
              email: true,
              slack: false,
              webhook: true
            }
          },
          administrators: [
            { id: 101, name: 'John Smith', email: '<EMAIL>' },
            { id: 102, name: 'Sarah Johnson', email: '<EMAIL>' }
          ]
        },
        {
          id: 2,
          name: 'TechStart GmbH',
          slug: 'techstart',
          domain: 'techstart.example.com',
          plan: 'professional',
          status: 'active',
          createdAt: '2025-02-15T14:36:12',
          expiresAt: '2026-02-15T14:36:12',
          userCount: 38,
          maxUsers: 50,
          storageUsed: 1850000000, // in bytes
          maxStorage: 5368709120, // 5GB in bytes
          aiUsage: {
            monthlyTokens: 3250000,
            remainingTokens: 1750000,
            totalTokens: 5000000,
            monthlyApiCalls: 2340,
          },
          settings: {
            branding: {
              logoUrl: 'https://example.com/techstart-logo.png',
              primaryColor: '#10B981',
              secondaryColor: '#065F46',
              accentColor: '#6366F1',
            },
            features: {
              aiAgent: true,
              telephony: true,
              apiAccess: true,
              analytics: true,
              multiUser: true
            },
            security: {
              mfaRequired: false,
              ssoEnabled: false,
              dataRetention: 365, // days
              ipRestrictions: []
            },
            notifications: {
              email: true,
              slack: true,
              webhook: false
            }
          },
          administrators: [
            { id: 201, name: 'Michael Müller', email: '<EMAIL>' }
          ]
        },
        {
          id: 3,
          name: 'Startup Incubator',
          slug: 'startup-inc',
          domain: 'startupinc.example.com',
          plan: 'basic',
          status: 'pending',
          createdAt: '2025-03-05T10:24:36',
          expiresAt: '2026-03-05T10:24:36',
          userCount: 5,
          maxUsers: 10,
          storageUsed: 350000000, // in bytes
          maxStorage: 1073741824, // 1GB in bytes
          aiUsage: {
            monthlyTokens: 250000,
            remainingTokens: 750000,
            totalTokens: 1000000,
            monthlyApiCalls: 420,
          },
          settings: {
            branding: {
              logoUrl: null,
              primaryColor: '#6366F1',
              secondaryColor: '#4F46E5',
              accentColor: '#F59E0B',
            },
            features: {
              aiAgent: true,
              telephony: false,
              apiAccess: false,
              analytics: false,
              multiUser: true
            },
            security: {
              mfaRequired: false,
              ssoEnabled: false,
              dataRetention: 90, // days
              ipRestrictions: []
            },
            notifications: {
              email: true,
              slack: false,
              webhook: false
            }
          },
          administrators: [
            { id: 301, name: 'Alex Rodriguez', email: '<EMAIL>' }
          ]
        },
        {
          id: 4,
          name: 'Global Manufacturing Inc.',
          slug: 'global-manufacturing',
          domain: 'gmi.example.com',
          plan: 'enterprise',
          status: 'suspended',
          createdAt: '2025-02-01T08:45:12',
          expiresAt: '2026-02-01T08:45:12',
          userCount: 85,
          maxUsers: 150,
          storageUsed: 7250000000, // in bytes
          maxStorage: 10737418240, // 10GB in bytes
          aiUsage: {
            monthlyTokens: 8750000,
            remainingTokens: 11250000,
            totalTokens: 20000000,
            monthlyApiCalls: 5480,
          },
          settings: {
            branding: {
              logoUrl: 'https://example.com/gmi-logo.png',
              primaryColor: '#475569',
              secondaryColor: '#1E293B',
              accentColor: '#EAB308',
            },
            features: {
              aiAgent: true,
              telephony: true,
              apiAccess: true,
              analytics: true,
              multiUser: true
            },
            security: {
              mfaRequired: true,
              ssoEnabled: true,
              dataRetention: 1095, // days
              ipRestrictions: ['***********/24']
            },
            notifications: {
              email: true,
              slack: true,
              webhook: true
            }
          },
          administrators: [
            { id: 401, name: 'Patricia Wong', email: '<EMAIL>' },
            { id: 402, name: 'Robert Johnson', email: '<EMAIL>' }
          ]
        }
      ]);
      setLoading(false);
    }, 1500);
  }, []);
  
  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'suspended':
        return 'bg-red-500';
      case 'expired':
        return 'bg-gray-500';
      default:
        return 'bg-gray-400';
    }
  };
  
  const getPlanColor = (plan) => {
    switch (plan) {
      case 'enterprise':
        return 'bg-purple-100 text-purple-800';
      case 'professional':
        return 'bg-blue-100 text-blue-800';
      case 'basic':
        return 'bg-green-100 text-green-800';
      case 'free':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const getPlanName = (plan) => {
    switch (plan) {
      case 'enterprise':
        return 'Enterprise';
      case 'professional':
        return 'Professional';
      case 'basic':
        return 'Basic';
      case 'free':
        return 'Free';
      default:
        return plan;
    }
  };
  
  const handleTenantSelect = (tenant) => {
    setSelectedTenant(tenant);
    setView('detail');
    setActiveTab('overview');
  };
  
  const handleBackToList = () => {
    setSelectedTenant(null);
    setView('list');
  };
  
  const handleCreateNew = () => {
    setSelectedTenant(null);
    setView('create');
  };
  
  const handleEditTenant = (tenant) => {
    setSelectedTenant(tenant);
    setView('edit');
  };
  
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };
  
  // Filter tenants based on search and status filter
  const filteredTenants = tenants.filter(tenant => {
    // Filter by search query
    if (searchQuery && 
        !tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !tenant.domain.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !tenant.slug.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Filter by status
    if (statusFilter !== 'all' && tenant.status !== statusFilter) {
      return false;
    }
    
    return true;
  });
  
  const renderTenantList = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Tenant Verwaltung</h1>
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1"
            onClick={handleCreateNew}
          >
            <Plus className="h-4 w-4" />
            Neuen Tenant erstellen
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="md:col-span-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Nach Namen, Domain oder Slug suchen..."
                className="w-full px-10 py-2 border rounded-md"
                value={searchQuery}
                onChange={handleSearchChange}
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              {searchQuery && (
                <button 
                  className="absolute right-3 top-2.5"
                  onClick={() => setSearchQuery('')}
                >
                  <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>
          </div>
          
          <div>
            <select 
              className="w-full p-2 border rounded-md"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">Alle Status</option>
              <option value="active">Aktiv</option>
              <option value="pending">Ausstehend</option>
              <option value="suspended">Gesperrt</option>
              <option value="expired">Abgelaufen</option>
            </select>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {loading ? (
            <div className="lg:col-span-3 flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : filteredTenants.length === 0 ? (
            <div className="lg:col-span-3 flex items-center justify-center h-64">
              <div className="text-center text-gray-500">
                <h3 className="font-medium mb-1">Keine Tenants gefunden</h3>
                <p>Passen Sie Ihre Suchkriterien an oder erstellen Sie einen neuen Tenant.</p>
              </div>
            </div>
          ) : (
            filteredTenants.map(tenant => (
              <Card 
                key={tenant.id}
                className={`hover:shadow-md transition-shadow cursor-pointer ${tenant.status === 'suspended' ? 'opacity-60' : ''}`}
                onClick={() => handleTenantSelect(tenant)}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="flex items-center gap-2">
                      <Building className="h-5 w-5 text-blue-500" />
                      {tenant.name}
                    </CardTitle>
                    <div className={`rounded-full h-3 w-3 ${getStatusColor(tenant.status)}`}></div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-600 mb-1">
                        <Globe className="h-4 w-4 inline-block mr-1 -mt-1" />
                        Domain: <span className="font-medium">{tenant.domain}</span>
                      </p>
                      <p className="text-sm text-gray-600">
                        <Database className="h-4 w-4 inline-block mr-1 -mt-1" />
                        Slug: <span className="font-mono">{tenant.slug}</span>
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-xs text-gray-500 mb-1">Benutzer</div>
                        <div className="font-medium">{tenant.userCount} / {tenant.maxUsers}</div>
                        <div className="w-full h-1.5 bg-gray-200 rounded-full mt-1">
                          <div 
                            className="h-1.5 bg-blue-500 rounded-full" 
                            style={{ width: `${Math.min(100, (tenant.userCount / tenant.maxUsers) * 100)}%` }}
                          ></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="text-xs text-gray-500 mb-1">Speicher</div>
                        <div className="font-medium">{formatBytes(tenant.storageUsed)} / {formatBytes(tenant.maxStorage)}</div>
                        <div className="w-full h-1.5 bg-gray-200 rounded-full mt-1">
                          <div 
                            className={`h-1.5 rounded-full ${tenant.storageUsed / tenant.maxStorage > 0.9 ? 'bg-red-500' : tenant.storageUsed / tenant.maxStorage > 0.7 ? 'bg-yellow-500' : 'bg-green-500'}`}
                            style={{ width: `${Math.min(100, (tenant.storageUsed / tenant.maxStorage) * 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className={`px-2 py-1 rounded-full text-xs ${getPlanColor(tenant.plan)}`}>
                        {getPlanName(tenant.plan)}
                      </span>
                      <span className="text-xs text-gray-500">
                        Erstellt: {new Date(tenant.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </>
    );
  };
  
  const renderTenantDetail = () => {
    if (!selectedTenant) return null;
    
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToList}>
            Zurück
          </Button>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Building className="h-6 w-6 text-blue-500" />
            {selectedTenant.name}
          </h1>
          <div className={`ml-4 rounded-full h-3 w-3 ${getStatusColor(selectedTenant.status)}`}></div>
          <span className="ml-2 text-sm capitalize">{selectedTenant.status}</span>
        </div>
        
        {/* Tabs */}
        <div className="border-b mb-6">
          <div className="flex gap-6">
            <button 
              className={`pb-2 px-1 ${activeTab === 'overview' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
              onClick={() => setActiveTab('overview')}
            >
              Übersicht
            </button>
            <button 
              className={`pb-2 px-1 ${activeTab === 'users' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
              onClick={() => setActiveTab('users')}
            >
              Benutzer
            </button>
            <button 
              