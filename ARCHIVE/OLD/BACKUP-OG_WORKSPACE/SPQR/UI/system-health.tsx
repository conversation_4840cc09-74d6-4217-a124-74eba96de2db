import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>hart, Line, BarChart, Bar, ResponsiveContainer, XAxis, YAxis, Tooltip, Legend } from 'recharts';
import { 
  RefreshCw, Server, Database, Cpu, HardDrive, Activity, 
  AlertCircle, CheckCircle, Clock, Zap, Globe, 
  ArrowUpRight, Download, Upload
} from 'lucide-react';

const SystemHealthDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const [systemData, setSystemData] = useState({
    components: [],
    performance: {},
    alerts: [],
    metrics: {}
  });
  
  // Load mock data
  useEffect(() => {
    const fetchData = () => {
      setLoading(true);
      setTimeout(() => {
        setSystemData({
          components: [
            { id: 'core', name: 'SPQR Core', status: 'healthy', uptime: '99.98%', load: 42, memory: 38, responseTime: 120 },
            { id: 'vector', name: 'Vector Store', status: 'healthy', uptime: '99.95%', load: 35, memory: 65, responseTime: 85 },
            { id: 'agent', name: 'Agent Manager', status: 'healthy', uptime: '99.99%', load: 28, memory: 42, responseTime: 110 },
            { id: 'integration', name: 'Integration Gateway', status: 'warning', uptime: '99.82%', load: 68, memory: 75, responseTime: 350 },
            { id: 'zep', name: 'Zep Memory', status: 'healthy', uptime: '99.91%', load: 23, memory: 51, responseTime: 95 }
          ],
          performance: {
            cpu: 48,
            memory: 62,
            disk: 37,
            network: {
              in: 25.6,
              out: 12.8
            }
          },
          alerts: [
            { id: 1, level: 'warning', component: 'Integration Gateway', message: 'Erhöhte Latenz bei Webhook-Endpunkten', time: '2025-03-09T10:28:15' },
            { id: 2, level: 'info', component: 'Vector Store', message: 'Index-Neuaufbau erfolgreich abgeschlossen', time: '2025-03-09T09:15:22' },
            { id: 3, level: 'error', component: 'Integration Gateway', message: 'Zeitüberschreitung bei 3 API-Anfragen', time: '2025-03-09T08:42:11' },
            { id: 4, level: 'info', component: 'Agent Manager', message: 'Neuer Agent "Data Analyst" hinzugefügt', time: '2025-03-08T16:35:40' }
          ],
          metrics: {
            requestsPerMinute: [
              { time: '10:00', core: 120, vector: 85, agent: 45, integration: 65, zep: 30 },
              { time: '10:05', core: 135, vector: 90, agent: 50, integration: 70, zep: 35 },
              { time: '10:10', core: 125, vector: 88, agent: 42, integration: 68, zep: 32 },
              { time: '10:15', core: 142, vector: 95, agent: 55, integration: 75, zep: 38 },
              { time: '10:20', core: 138, vector: 92, agent: 48, integration: 72, zep: 36 },
              { time: '10:25', core: 145, vector: 97, agent: 52, integration: 78, zep: 40 },
              { time: '10:30', core: 152, vector: 102, agent: 58, integration: 85, zep: 45 }
            ],
            responseTimesMs: [
              { time: '10:00', core: 110, vector: 80, agent: 95, integration: 320, zep: 90 },
              { time: '10:05', core: 115, vector: 82, agent: 100, integration: 330, zep: 92 },
              { time: '10:10', core: 112, vector: 81, agent: 98, integration: 335, zep: 91 },
              { time: '10:15', core: 118, vector: 84, agent: 105, integration: 345, zep: 94 },
              { time: '10:20', core: 120, vector: 85, agent: 108, integration: 350, zep: 95 },
              { time: '10:25', core: 122, vector: 86, agent: 110, integration: 355, zep: 96 },
              { time: '10:30', core: 120, vector: 85, agent: 110, integration: 350, zep: 95 }
            ]
          }
        });
        setLastRefresh(new Date());
        setLoading(false);
      }, 1000);
    };

    fetchData();
    const interval = setInterval(fetchData, refreshInterval * 1000);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const getStatusColor = (status) => {
    switch(status) {
      case 'healthy': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status) => {
    switch(status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'error': return <AlertCircle className="h-5 w-5 text-red-500" />;
      default: return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getAlertIcon = (level) => {
    switch(level) {
      case 'error': return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'info': return <AlertCircle className="h-5 w-5 text-blue-500" />;
      default: return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const handleRefresh = () => {
    // Trigger a manual refresh
    setLoading(true);
    setTimeout(() => {
      // Reuse the same mock data for simplicity
      setLastRefresh(new Date());
      setLoading(false);
    }, 1000);
  };

  if (loading && !systemData.components.length) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">SPQR System Health Dashboard</h1>
          <p className="text-gray-600">Letzte Aktualisierung: {lastRefresh.toLocaleTimeString('de-DE')}</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <label htmlFor="refresh" className="text-sm text-gray-600">Auto-Update:</label>
            <select 
              id="refresh"
              className="border rounded-md px-2 py-1 text-sm"
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
            >
              <option value={10}>10 Sekunden</option>
              <option value={30}>30 Sekunden</option>
              <option value={60}>1 Minute</option>
              <option value={300}>5 Minuten</option>
            </select>
          </div>
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={handleRefresh}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Aktualisieren
          </Button>
        </div>
      </div>

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">CPU Auslastung</p>
                <h3 className="text-2xl font-bold">{systemData.performance.cpu}%</h3>
              </div>
              <Cpu className="h-8 w-8 text-blue-500" />
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className={`h-2 rounded-full ${systemData.performance.cpu > 80 ? 'bg-red-500' : systemData.performance.cpu > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                style={{ width: `${systemData.performance.cpu}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Speicher</p>
                <h3 className="text-2xl font-bold">{systemData.performance.memory}%</h3>
              </div>
              <HardDrive className="h-8 w-8 text-purple-500" />
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className={`h-2 rounded-full ${systemData.performance.memory > 80 ? 'bg-red-500' : systemData.performance.memory > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                style={{ width: `${systemData.performance.memory}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Festplatte</p>
                <h3 className="text-2xl font-bold">{systemData.performance.disk}%</h3>
              </div>
              <Database className="h-8 w-8 text-green-500" />
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className={`h-2 rounded-full ${systemData.performance.disk > 80 ? 'bg-red-500' : systemData.performance.disk > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                style={{ width: `${systemData.performance.disk}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Netzwerk</p>
                <h3 className="text-2xl font-bold">{systemData.performance.network.in} MB/s</h3>
              </div>
              <Download className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Ausgehend: {systemData.performance.network.out} MB/s
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Service-Status</p>
                <h3 className="text-xl font-bold flex items-center gap-2">
                  <span className={`inline-block w-3 h-3 rounded-full ${getStatusColor('warning')}`}></span>
                  Warnung
                </h3>
              </div>
              <Server className="h-8 w-8 text-red-500" />
            </div>
            <div className="text-xs text-gray-500 mt-1">
              4 Gesund, 1 Warnung, 0 Fehler
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Component Status Table */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Komponentenstatus</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-4">Komponente</th>
                  <th className="text-left py-2 px-4">Status</th>
                  <th className="text-left py-2 px-4">Uptime</th>
                  <th className="text-left py-2 px-4">Auslastung</th>
                  <th className="text-left py-2 px-4">Speicher</th>
                  <th className="text-left py-2 px-4">Antwortzeit</th>
                </tr>
              </thead>
              <tbody>
                {systemData.components.map(component => (
                  <tr key={component.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4 font-medium">{component.name}</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <span className={`inline-block w-3 h-3 rounded-full mr-2 ${getStatusColor(component.status)}`}></span>
                        <span className="capitalize">{component.status}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">{component.uptime}</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                          <div 
                            className={`h-2 rounded-full ${component.load > 80 ? 'bg-red-500' : component.load > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                            style={{ width: `${component.load}%` }}
                          ></div>
                        </div>
                        <span>{component.load}%</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                          <div 
                            className={`h-2 rounded-full ${component.memory > 80 ? 'bg-red-500' : component.memory > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                            style={{ width: `${component.memory}%` }}
                          ></div>
                        </div>
                        <span>{component.memory}%</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={component.responseTime > 300 ? 'text-red-600 font-medium' : component.responseTime > 200 ? 'text-yellow-600' : 'text-green-600'}>
                        {component.responseTime} ms
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Metrics and Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Performance Charts */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Anfragen pro Minute</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={systemData.metrics.requestsPerMinute}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="core" stroke="#3b82f6" name="SPQR Core" strokeWidth={2} />
                    <Line type="monotone" dataKey="vector" stroke="#8b5cf6" name="Vector Store" strokeWidth={2} />
                    <Line type="monotone" dataKey="agent" stroke="#10b981" name="Agent Manager" strokeWidth={2} />
                    <Line type="monotone" dataKey="integration" stroke="#f59e0b" name="Integration Gateway" strokeWidth={2} />
                    <Line type="monotone" dataKey="zep" stroke="#ef4444" name="Zep Memory" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Antwortzeiten (ms)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={systemData.metrics.responseTimesMs}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="core" stroke="#3b82f6" name="SPQR Core" strokeWidth={2} />
                    <Line type="monotone" dataKey="vector" stroke="#8b5cf6" name="Vector Store" strokeWidth={2} />
                    <Line type="monotone" dataKey="agent" stroke="#10b981" name="Agent Manager" strokeWidth={2} />
                    <Line type="monotone" dataKey="integration" stroke="#f59e0b" name="Integration Gateway" strokeWidth={2} />
                    <Line type="monotone" dataKey="zep" stroke="#ef4444" name="Zep Memory" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Alerts */}
        <div>
          <Card className="h-full">
            <CardHeader>
              <CardTitle>System-Alerts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {systemData.alerts.map(alert => (
                  <div key={alert.id} className="p-3 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-start gap-3">
                      {getAlertIcon(alert.level)}
                      <div>
                        <p className="font-medium">{alert.component}</p>
                        <p className="text-gray-600 text-sm">{alert.message}</p>
                        <p className="text-gray-500 text-xs mt-1">
                          {new Date(alert.time).toLocaleString('de-DE')}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                <Button
                  variant="outline"
                  className="w-full mt-2 text-sm"
                >
                  Alle Alerts anzeigen
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SystemHealthDashboard;
