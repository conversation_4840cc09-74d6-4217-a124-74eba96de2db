import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Plus, Save, ArrowRight, RefreshCw, Check, X, 
  Globe, Database, Lock, Code, Link2, PlugZap, 
  FileText, Webhook, MoreHorizontal, Brain
} from 'lucide-react';

const ConnectionBuilder = () => {
  const [view, setView] = useState('list'); // list, create, detail, edit
  const [connections, setConnections] = useState([]);
  const [selectedConnection, setSelectedConnection] = useState(null);
  const [connectionType, setConnectionType] = useState('api');
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Mock data loading
    const fetchConnections = async () => {
      setLoading(true);
      
      // This would be replaced with actual API calls
      setTimeout(() => {
        setConnections([
          {
            id: 1,
            name: 'Salesforce CRM',
            type: 'api',
            status: 'active',
            description: 'Integration mit Salesforce zur Synchronisation von Kundendaten',
            createdAt: '2025-01-15',
            lastUsed: '2025-03-09T08:25:10',
            baseUrl: 'https://api.salesforce.com/v56.0',
            authType: 'oauth2',
            endpoints: [
              { id: 1, name: 'Get Contacts', method: 'GET', path: '/contacts', parameters: {} },
              { id: 2, name: 'Create Lead', method: 'POST', path: '/leads', parameters: {} },
              { id: 3, name: 'Update Opportunity', method: 'PUT', path: '/opportunities/{id}', parameters: {} }
            ],
            health: 'healthy',
            executions: 1245,
            avgResponseTime: 320
          },
          {
            id: 2,
            name: 'PostgreSQL Database',
            type: 'database',
            status: 'active',
            description: 'Direkter Datenbankzugriff auf Legacy-System',
            createdAt: '2025-02-10',
            lastUsed: '2025-03-08T16:42:35',
            connectionString: '*************************************************/crm',
            queries: [
              { id: 1, name: 'Active Customers', query: 'SELECT * FROM customers WHERE status = $1', parameters: ['active'] },
              { id: 2, name: 'Monthly Sales', query: 'SELECT sum(amount) FROM sales WHERE date_trunc(\'month\', sale_date) = $1', parameters: ['current_month'] }
            ],
            health: 'healthy',
            executions: 782,
            avgResponseTime: 180
          },
          {
            id: 3,
            name: 'Support Ticketing UI',
            type: 'ui_automation',
            status: 'warning',
            description: 'Web-Automatisierung für Legacy-Ticketsystem',
            createdAt: '2025-02-18',
            lastUsed: '2025-03-09T09:12:18',
            baseUrl: 'https://support.legacy-system.com',
            steps: [
              { id: 1, action: 'navigate', params: { url: 'https://support.legacy-system.com/login' } },
              { id: 2, action: 'input', params: { selector: '#username', value: '${username}' } },
              { id: 3, action: 'input', params: { selector: '#password', value: '${password}' } },
              { id: 4, action: 'click', params: { selector: 'button[type="submit"]' } }
            ],
            health: 'warning',
            executions: 342,
            avgResponseTime: 5800
          },
          {
            id: 4,
            name: 'Stripe Webhooks',
            type: 'webhook',
            status: 'active',
            description: 'Verarbeitung von Stripe-Zahlungsereignissen',
            createdAt: '2025-03-01',
            lastUsed: '2025-03-09T07:35:42',
            endpoint: '/api/webhooks/stripe',
            secretKey: 'whsec_****',
            events: [
              { id: 1, name: 'payment_intent.succeeded', handler: 'handlePaymentSuccess' },
              { id: 2, name: 'invoice.payment_failed', handler: 'handlePaymentFailure' }
            ],
            health: 'healthy',
            executions: 523,
            avgResponseTime: 110
          }
        ]);
        setLoading(false);
      }, 1000);
    };

    fetchConnections();
  }, []);

  const getConnectionTypeIcon = (type) => {
    switch (type) {
      case 'api':
        return <Globe className="h-5 w-5 text-blue-500" />;
      case 'database':
        return <Database className="h-5 w-5 text-green-500" />;
      case 'ui_automation':
        return <Code className="h-5 w-5 text-purple-500" />;
      case 'webhook':
        return <Webhook className="h-5 w-5 text-yellow-500" />;
      default:
        return <Link2 className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      case 'inactive':
        return 'bg-gray-400';
      default:
        return 'bg-gray-500';
    }
  };

  const handleConnectionSelect = (connection) => {
    setSelectedConnection(connection);
    setView('detail');
  };

  const handleCreateNew = () => {
    setConnectionType('api');
    setSelectedConnection(null);
    setView('create');
  };

  const handleBackToList = () => {
    setSelectedConnection(null);
    setView('list');
  };

  const handleBackToDetail = () => {
    setView('detail');
  };

  const handleEdit = (connection) => {
    setSelectedConnection(connection);
    setView('edit');
  };

  const renderConnectionsList = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      );
    }

    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Connection Manager</h1>
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
            onClick={handleCreateNew}
          >
            <Plus className="h-4 w-4" />
            Neue Verbindung
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {connections.map((connection) => (
            <Card 
              key={connection.id} 
              className="hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleConnectionSelect(connection)}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    {getConnectionTypeIcon(connection.type)}
                    <CardTitle>{connection.name}</CardTitle>
                  </div>
                  <div className={`rounded-full h-3 w-3 ${getStatusColor(connection.status)}`}></div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">{connection.description}</p>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Typ:</span>
                    <span className="font-medium capitalize">{connection.type.replace('_', ' ')}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Status:</span>
                    <span className="font-medium capitalize">{connection.status}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Letzte Nutzung:</span>
                    <span className="font-medium">{new Date(connection.lastUsed).toLocaleString()}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Ausführungen:</span>
                    <span className="font-medium">{connection.executions.toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    );
  };

  const renderConnectionDetail = () => {
    if (!selectedConnection) return null;
    
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToList}>
            Zurück
          </Button>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            {getConnectionTypeIcon(selectedConnection.type)}
            {selectedConnection.name}
          </h1>
          <div className={`ml-4 rounded-full h-3 w-3 ${getStatusColor(selectedConnection.status)}`}></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Verbindungsdetails</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium mb-4">Grundinformationen</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Typ:</span>
                        <span className="capitalize">{selectedConnection.type.replace('_', ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Status:</span>
                        <span className="capitalize">{selectedConnection.status}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Erstellt am:</span>
                        <span>{selectedConnection.createdAt}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Letzte Nutzung:</span>
                        <span>{new Date(selectedConnection.lastUsed).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium mb-4">Leistungsmetriken</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Ausführungen:</span>
                        <span>{selectedConnection.executions.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Durchschn. Antwortzeit:</span>
                        <span>{selectedConnection.avgResponseTime} ms</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Gesundheit:</span>
                        <span className={selectedConnection.health === 'healthy' ? 'text-green-600' : 'text-yellow-600'}>
                          {selectedConnection.health}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-6">
                  <h3 className="font-medium mb-2">Beschreibung</h3>
                  <p className="text-gray-700">{selectedConnection.description}</p>
                </div>

                {selectedConnection.type === 'api' && (
                  <div className="mt-6">
                    <h3 className="font-medium mb-2">API-Konfiguration</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Basis-URL:</span>
                        <span className="font-mono text-sm">{selectedConnection.baseUrl}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Auth-Typ:</span>
                        <span>{selectedConnection.authType}</span>
                      </div>
                    </div>
                  </div>
                )}

                {selectedConnection.type === 'database' && (
                  <div className="mt-6">
                    <h3 className="font-medium mb-2">Datenbank-Konfiguration</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Connection String:</span>
                        <span className="font-mono text-sm">{selectedConnection.connectionString}</span>
                      </div>
                    </div>
                  </div>
                )}

                {selectedConnection.type === 'webhook' && (
                  <div className="mt-6">
                    <h3 className="font-medium mb-2">Webhook-Konfiguration</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Endpoint:</span>
                        <span className="font-mono text-sm">{selectedConnection.endpoint}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Secret Key:</span>
                        <span className="font-mono text-sm">{selectedConnection.secretKey}</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {selectedConnection.type === 'api' && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>API-Endpunkte</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedConnection.endpoints.map(endpoint => (
                      <div key={endpoint.id} className="p-3 border rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <div className="font-medium">{endpoint.name}</div>
                          <div className="flex items-center gap-2">
                            <span className={`font-mono text-xs px-2 py-1 rounded ${
                              endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                              endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                              endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                              endpoint.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {endpoint.method}
                            </span>
                            <Button variant="outline" size="sm" className="text-xs">
                              Test
                            </Button>
                          </div>
                        </div>
                        <div className="font-mono text-sm text-gray-600">{selectedConnection.baseUrl}{endpoint.path}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {selectedConnection.type === 'database' && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Vordefinierte Abfragen</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedConnection.queries.map(query => (
                      <div key={query.id} className="p-3 border rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <div className="font-medium">{query.name}</div>
                          <Button variant="outline" size="sm" className="text-xs">
                            Test
                          </Button>
                        </div>
                        <div className="font-mono text-xs p-2 bg-gray-50 rounded mb-2">
                          {query.query}
                        </div>
                        <div className="text-sm text-gray-600">
                          Parameter: {JSON.stringify(query.parameters)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {selectedConnection.type === 'ui_automation' && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>UI-Automatisierungsschritte</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {selectedConnection.steps.map((step, index) => (
                      <div key={step.id} className="flex items-start">
                        <div className="bg-blue-100 text-blue-800 rounded-full h-6 w-6 flex items-center justify-center font-medium text-sm mr-3 mt-1">
                          {index + 1}
                        </div>
                        <div className="flex-1 p-3 border rounded-lg">
                          <div className="font-medium capitalize mb-1">{step.action}</div>
                          <div className="text-sm text-gray-600">
                            {JSON.stringify(step.params)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {selectedConnection.type === 'webhook' && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Event-Handler</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedConnection.events.map(event => (
                      <div key={event.id} className="p-3 border rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <div className="font-medium">{event.name}</div>
                          <Button variant="outline" size="sm" className="text-xs">
                            Trigger Test
                          </Button>
                        </div>
                        <div className="text-sm text-gray-600">
                          Handler-Funktion: <span className="font-mono">{event.handler}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
          
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Aktionen</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button className="w-full flex justify-center items-center gap-2" onClick={() => handleEdit(selectedConnection)}>
                    <PlugZap className="h-4 w-4" />
                    Verbindung bearbeiten
                  </Button>
                  <Button variant="outline" className="w-full flex justify-center items-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Verbindung testen
                  </Button>
                  <Button variant="outline" className="w-full flex justify-center items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Ausführungshistorie
                  </Button>
                  <Button variant="outline" className="w-full flex justify-center items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Berechtigungen verwalten
                  </Button>
                  <Button variant="outline" className="w-full flex justify-center items-center gap-2 text-red-600">
                    <X className="h-4 w-4" />
                    Verbindung deaktivieren
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>KI-Werkzeuge</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 bg-gray-50 rounded-lg text-center text-sm text-gray-600 mb-4">
                  <Brain className="h-5 w-5 mx-auto mb-2 text-blue-500" />
                  Generiere KI-Werkzeuge, die diese Verbindung nutzen
                </div>
                <Button className="w-full flex justify-center items-center gap-2">
                  <Plus className="h-4 w-4" />
                  KI-Werkzeug generieren
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  };

  const renderConnectionCreate = () => {
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToList}>
            Abbrechen
          </Button>
          <h1 className="text-2xl font-bold">Neue Verbindung erstellen</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Verbindungstyp</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div 
                className={`p-4 border rounded-lg hover:border-blue-500 cursor-pointer ${connectionType === 'api' ? 'border-blue-500 bg-blue-50' : ''}`}
                onClick={() => setConnectionType('api')}
              >
                <div className="flex items-center justify-center mb-4">
                  <Globe className="h-8 w-8 text-blue-500" />
                </div>
                <div className="text-center font-medium">REST API</div>
                <p className="text-xs text-gray-600 text-center mt-2">
                  Verbindung zu RESTful APIs mit OAuth, API-Key oder Basic Auth
                </p>
              </div>
              
              <div 
                className={`p-4 border rounded-lg hover:border-blue-500 cursor-pointer ${connectionType === 'database' ? 'border-blue-500 bg-blue-50' : ''}`}
                onClick={() => setConnectionType('database')}
              >
                <div className="flex items-center justify-center mb-4">
                  <Database className="h-8 w-8 text-green-500" />
                </div>
                <div className="text-center font-medium">Datenbank</div>
                <p className="text-xs text-gray-600 text-center mt-2">
                  Direkte Datenbankverbindung zu SQL oder NoSQL-Datenbanken
                </p>
              </div>
              
              <div 
                className={`p-4 border rounded-lg hover:border-blue-500 cursor-pointer ${connectionType === 'ui_automation' ? 'border-blue-500 bg-blue-50' : ''}`}
                onClick={() => setConnectionType('ui_automation')}
              >
                <div className="flex items-center justify-center mb-4">
                  <Code className="h-8 w-8 text-purple-500" />
                </div>
                <div className="text-center font-medium">UI-Automatisierung</div>
                <p className="text-xs text-gray-600 text-center mt-2">
                  Browser-Automatisierung für Systeme ohne API
                </p>
              </div>
              
              <div 
                className={`p-4 border rounded-lg hover:border-blue-500 cursor-pointer ${connectionType === 'webhook' ? 'border-blue-500 bg-blue-50' : ''}`}
                onClick={() => setConnectionType('webhook')}
              >
                <div className="flex items-center justify-center mb-4">
                  <Webhook className="h-8 w-8 text-yellow-500" />
                </div>
                <div className="text-center font-medium">Webhook</div>
                <p className="text-xs text-gray-600 text-center mt-2">
                  Einrichtung von Webhooks zum Empfangen von Ereignissen
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Grundinformationen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Verbindungsname</label>
                <input type="text" className="w-full p-2 border rounded-md" placeholder="z.B. Salesforce CRM" />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Beschreibung</label>
                <textarea className="w-full p-2 border rounded-md h-20" placeholder="Beschreiben Sie den Zweck dieser Verbindung..."></textarea>
              </div>
            </div>
          </CardContent>
        </Card>

        {connectionType === 'api' && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>API-Konfiguration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Webhook Endpunkt</label>
                  <div className="flex items-center">
                    <span className="text-gray-500 bg-gray-100 p-2 border-l border-t border-b rounded-l-md">https://your-domain.com</span>
                    <input type="text" className="flex-1 p-2 border rounded-r-md" placeholder="/api/webhooks/stripe" />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Event-Handler</label>
                  <div className="border rounded-md p-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Event-Name</label>
                        <input type="text" className="w-full p-2 border rounded-md" placeholder="payment_intent.succeeded" />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Handler-Funktion</label>
                        <input type="text" className="w-full p-2 border rounded-md" placeholder="handlePaymentSuccess" />
                      </div>
                    </div>
                    
                    <Button variant="outline" className="w-full flex items-center justify-center gap-2">
                      <Plus className="h-4 w-4" />
                      Event-Handler hinzufügen
                    </Button>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Webhook Secret</label>
                  <input type="text" className="w-full p-2 border rounded-md" placeholder="whsec_..." />
                  <p className="text-xs text-gray-500 mt-1">Wird zur Signaturverifizierung verwendet</p>
                </div>
                
                <div className="flex justify-end mt-6">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </>
    );
  };
  
  const renderConnectionEdit = () => {
    if (!selectedConnection) return null;
    
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToDetail}>
            Abbrechen
          </Button>
          <h1 className="text-2xl font-bold">
            {selectedConnection.name} bearbeiten
          </h1>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Grundinformationen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Verbindungsname</label>
                <input 
                  type="text" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={selectedConnection.name}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Beschreibung</label>
                <textarea 
                  className="w-full p-2 border rounded-md h-20" 
                  defaultValue={selectedConnection.description}
                ></textarea>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select 
                  className="w-full p-2 border rounded-md"
                  defaultValue={selectedConnection.status}
                >
                  <option value="active">Aktiv</option>
                  <option value="inactive">Inaktiv</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Type-specific configuration forms would go here, similar to the create view */}
        
        <div className="flex justify-end mt-6 gap-4">
          <Button variant="outline" onClick={handleBackToDetail}>
            Abbrechen
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
            <Save className="h-4 w-4" />
            Änderungen speichern
          </Button>
        </div>
      </>
    );
  };

  // Render the appropriate view based on current state
  const renderView = () => {
    switch (view) {
      case 'detail':
        return renderConnectionDetail();
      case 'create':
        return renderConnectionCreate();
      case 'edit':
        return renderConnectionEdit();
      case 'list':
      default:
        return renderConnectionsList();
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {renderView()}
    </div>
  );
};

export default ConnectionBuilder;block text-sm font-medium text-gray-700 mb-1">Start-URL</label>
                  <input type="text" className="w-full p-2 border rounded-md" placeholder="https://example.com/login" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Automatisierungsschritte</label>
                  <div className="border rounded-md p-4 space-y-4">
                    <div className="flex items-start">
                      <div className="bg-blue-100 text-blue-800 rounded-full h-6 w-6 flex items-center justify-center font-medium text-sm mr-3 mt-1">
                        1
                      </div>
                      <div className="flex-1">
                        <select className="w-full p-2 border rounded-md mb-2">
                          <option value="navigate">Navigate</option>
                          <option value="click">Click</option>
                          <option value="input">Input Text</option>
                          <option value="select">Select Option</option>
                          <option value="wait">Wait</option>
                          <option value="extract">Extract Data</option>
                        </select>
                        <div>
                          <input type="text" className="w-full p-2 border rounded-md" placeholder="Selector or URL" />
                        </div>
                      </div>
                      <Button variant="outline" size="sm" className="ml-2">
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <Button variant="outline" className="w-full flex items-center justify-center gap-2">
                      <Plus className="h-4 w-4" />
                      Schritt hinzufügen
                    </Button>
                  </div>
                </div>
                
                <div className="flex justify-end mt-6">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        
        {connectionType === 'webhook' && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Webhook-Konfiguration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Basis-URL</label>
                  <input type="text" className="w-full p-2 border rounded-md" placeholder="https://api.example.com/v1" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Authentifizierungstyp</label>
                  <select className="w-full p-2 border rounded-md">
                    <option value="none">Keine Authentifizierung</option>
                    <option value="basic">Basic Auth</option>
                    <option value="apikey">API Key</option>
                    <option value="oauth2">OAuth 2.0</option>
                    <option value="bearer">Bearer Token</option>
                  </select>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Passwort</label>
                    <input type="password" className="w-full p-2 border rounded-md" placeholder="password" />
                  </div>
                </div>
                
                <div className="flex justify-end mt-6">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        
        {connectionType === 'ui_automation' && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>UI-Automatisierung</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Port</label>
                    <input type="text" className="w-full p-2 border rounded-md" placeholder="5432" />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Datenbankname</label>
                    <input type="text" className="w-full p-2 border rounded-md" placeholder="mydatabase" />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Benutzername</label>
                    <input type="text" className="w-full p-2 border rounded-md" placeholder="user" />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                    <input type="text" className="w-full p-2 border rounded-md" placeholder="Username" />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <input type="password" className="w-full p-2 border rounded-md" placeholder="Password" />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Headers (JSON)</label>
                  <textarea className="w-full p-2 border rounded-md h-20 font-mono text-sm" placeholder='{"Content-Type": "application/json"}'></textarea>
                </div>
              </div>
              
              <div className="flex justify-end mt-6">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Speichern
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
        
        {connectionType === 'database' && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Datenbank-Konfiguration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Datenbanktyp</label>
                  <select className="w-full p-2 border rounded-md">
                    <option value="postgresql">PostgreSQL</option>
                    <option value="mysql">MySQL</option>
                    <option value="mongodb">MongoDB</option>
                    <option value="sqlserver">SQL Server</option>
                    <option value="oracle">Oracle</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Connection String</label>
                  <input type="text" className="w-full p-2 border rounded-md" placeholder="postgresql://user:password@host:port/database" />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Host</label>
                    <input type="text" className="w-full p-2 border rounded-md" placeholder="localhost" />
                  </div>
                  
                  <div>
                    <label className="