import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Settings, Plus, Search, Filter, X, Edit, ChevronDown, ChevronUp, 
  Check, Archive, Flag, BarChart4, RefreshCw, Clock, Lock, Percent, 
  Users, Building, Activity, Share2
} from 'lucide-react';

const FeatureFlagManagement = () => {
  const [loading, setLoading] = useState(true);
  const [featureFlags, setFeatureFlags] = useState([]);
  const [expandedFlag, setExpandedFlag] = useState(null);
  const [view, setView] = useState('list'); // list, create, edit, metrics
  const [selectedFlag, setSelectedFlag] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    category: 'all'
  });
  
  useEffect(() => {
    // Mock data loading
    setTimeout(() => {
      setFeatureFlags([
        {
          id: 1,
          key: 'new_dashboard',
          name: 'Neues Dashboard',
          description: 'Aktiviert das neue Dashboard mit verbesserten Visualisierungen und Leistung',
          category: 'ui',
          enabled: true,
          rolloutPercentage: 75,
          tenantConfigurable: true,
          createdAt: '2025-01-15T10:24:36',
          updatedAt: '2025-03-05T14:22:18',
          startsAt: null,
          endsAt: null,
          lastUpdatedBy: '<EMAIL>',
          rules: {
            tenantIds: [],
            excludedTenantIds: [],
            tenantPlans: ['enterprise', 'professional'],
            userRoles: [],
            environments: ['production', 'staging'],
            countries: []
          },
          metrics: {
            usageCount: 12450,
            successRate: 98.2,
            errorRate: 1.8,
            averageLatency: 150, // ms
          },
          dependencies: ['multi_tenant_support']
        },
        {
          id: 2,
          key: 'advanced_analytics',
          name: 'Erweiterte Analyse',
          description: 'Erweiterte Analysetools und Berichte für detaillierte Geschäftseinblicke',
          category: 'analytics',
          enabled: true,
          rolloutPercentage: 100,
          tenantConfigurable: false,
          createdAt: '2025-01-22T09:18:45',
          updatedAt: '2025-02-28T16:35:10',
          startsAt: null,
          endsAt: null,
          lastUpdatedBy: '<EMAIL>',
          rules: {
            tenantIds: [],
            excludedTenantIds: [],
            tenantPlans: ['enterprise'],
            userRoles: ['admin', 'manager'],
            environments: ['production'],
            countries: []
          },
          metrics: {
            usageCount: 3562,
            successRate: 99.5,
            errorRate: 0.5,
            averageLatency: 220, // ms
          },
          dependencies: []
        },
        {
          id: 3,
          key: 'ai_document_analysis',
          name: 'KI-Dokumentenanalyse',
          description: 'Automatisierte Analyse von Dokumenten mit KI-Extraktion von Schlüsselinformationen',
          category: 'ai',
          enabled: true,
          rolloutPercentage: 50,
          tenantConfigurable: true,
          createdAt: '2025-02-10T11:45:22',
          updatedAt: '2025-03-08T08:12:36',
          startsAt: null,
          endsAt: null,
          lastUpdatedBy: '<EMAIL>',
          rules: {
            tenantIds: ['tenant-1', 'tenant-3'],
            excludedTenantIds: [],
            tenantPlans: ['enterprise', 'professional'],
            userRoles: [],
            environments: ['production', 'staging'],
            countries: []
          },
          metrics: {
            usageCount: 4582,
            successRate: 92.8,
            errorRate: 7.2,
            averageLatency: 850, // ms
          },
          dependencies: ['multi_tenant_support']
        },
        {
          id: 4,
          key: 'voice_commands',
          name: 'Sprachbefehle',
          description: 'Sprachbefehlsunterstützung für hände-freie Interaktion mit dem CRM',
          category: 'accessibility',
          enabled: false,
          rolloutPercentage: 0,
          tenantConfigurable: false,
          createdAt: '2025-02-20T15:30:45',
          updatedAt: '2025-03-01T09:45:12',
          startsAt: '2025-04-01T00:00:00',
          endsAt: null,
          lastUpdatedBy: '<EMAIL>',
          rules: {
            tenantIds: [],
            excludedTenantIds: [],
            tenantPlans: ['enterprise'],
            userRoles: ['admin'],
            environments: ['staging'],
            countries: []
          },
          metrics: {
            usageCount: 152,
            successRate: 85.4,
            errorRate: 14.6,
            averageLatency: 420, // ms
          },
          dependencies: []
        },
        {
          id: 5,
          key: 'multi_tenant_support',
          name: 'Multi-Tenant-Unterstützung',
          description: 'Grundlegende Infrastruktur zur Unterstützung mehrerer Mandanten',
          category: 'core',
          enabled: true,
          rolloutPercentage: 100,
          tenantConfigurable: false,
          createdAt: '2025-01-05T08:15:30',
          updatedAt: '2025-01-05T08:15:30',
          startsAt: null,
          endsAt: null,
          lastUpdatedBy: '<EMAIL>',
          rules: {
            tenantIds: [],
            excludedTenantIds: [],
            tenantPlans: ['free', 'basic', 'professional', 'enterprise'],
            userRoles: [],
            environments: ['production', 'staging', 'development'],
            countries: []
          },
          metrics: {
            usageCount: 45820,
            successRate: 99.8,
            errorRate: 0.2,
            averageLatency: 85, // ms
          },
          dependencies: []
        }
      ]);
      setLoading(false);
    }, 1500);
  }, []);
  
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };
  
  const handleFilterChange = (field, value) => {
    setFilters({
      ...filters,
      [field]: value
    });
  };
  
  const handleToggleFlag = (flagId) => {
    setFeatureFlags(featureFlags.map(flag => {
      if (flag.id === flagId) {
        return {
          ...flag,
          enabled: !flag.enabled
        };
      }
      return flag;
    }));
  };
  
  const handleExpandFlag = (flagId) => {
    if (expandedFlag === flagId) {
      setExpandedFlag(null);
    } else {
      setExpandedFlag(flagId);
    }
  };
  
  const handleFlagSelect = (flag) => {
    setSelectedFlag(flag);
    setView('edit');
  };
  
  const handleCreateNew = () => {
    setSelectedFlag(null);
    setView('create');
  };
  
  const handleBackToList = () => {
    setSelectedFlag(null);
    setView('list');
  };
  
  const handleShowMetrics = (flag) => {
    setSelectedFlag(flag);
    setView('metrics');
  };
  
  const getCategoryBadge = (category) => {
    switch (category) {
      case 'ui':
        return <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">UI</span>;
      case 'analytics':
        return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Analytics</span>;
      case 'ai':
        return <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">AI</span>;
      case 'accessibility':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Accessibility</span>;
      case 'core':
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">Core</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">{category}</span>;
    }
  };
  
  // Filter flags based on search and filters
  const filteredFlags = featureFlags.filter(flag => {
    // Filter by search query
    if (searchQuery && 
        !flag.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !flag.key.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !flag.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Filter by status
    if (filters.status !== 'all') {
      if (filters.status === 'enabled' && !flag.enabled) {
        return false;
      }
      if (filters.status === 'disabled' && flag.enabled) {
        return false;
      }
    }
    
    // Filter by category
    if (filters.category !== 'all' && flag.category !== filters.category) {
      return false;
    }
    
    return true;
  });
  
  const renderFlagList = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Feature Flags</h1>
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1"
            onClick={handleCreateNew}
          >
            <Plus className="h-4 w-4" />
            Neues Feature Flag
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="md:col-span-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Nach Namen oder Key suchen..."
                className="w-full px-10 py-2 border rounded-md"
                value={searchQuery}
                onChange={handleSearchChange}
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              {searchQuery && (
                <button 
                  className="absolute right-3 top-2.5"
                  onClick={() => setSearchQuery('')}
                >
                  <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>
          </div>
          
          <div>
            <Button
              variant="outline"
              className="w-full flex items-center justify-center gap-1"
            >
              <Filter className="h-4 w-4" />
              Erweiterte Filter
            </Button>
          </div>
        </div>
        
        <div className="mb-6">
          <div className="flex gap-2 flex-wrap">
            {/* Status filter pills */}
            <div className="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center">
              <span className="mr-1 text-gray-600">Status:</span>
              <select 
                className="bg-transparent border-none text-blue-600 font-medium focus:outline-none cursor-pointer"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="all">Alle</option>
                <option value="enabled">Aktiv</option>
                <option value="disabled">Inaktiv</option>
              </select>
            </div>
            
            {/* Category filter pills */}
            <div className="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center">
              <span className="mr-1 text-gray-600">Kategorie:</span>
              <select 
                className="bg-transparent border-none text-blue-600 font-medium focus:outline-none cursor-pointer"
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
              >
                <option value="all">Alle</option>
                <option value="ui">UI</option>
                <option value="analytics">Analytics</option>
                <option value="ai">AI</option>
                <option value="accessibility">Accessibility</option>
                <option value="core">Core</option>
              </select>
            </div>
          </div>
        </div>
        
        <Card>
          <CardContent className="p-0">
            <div className="divide-y">
              {loading ? (
                <div className="py-10 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                </div>
              ) : filteredFlags.length === 0 ? (
                <div className="py-8 text-center text-gray-500">
                  Keine Feature Flags gefunden. Passen Sie Ihre Suchkriterien an oder erstellen Sie ein neues Feature Flag.
                </div>
              ) : (
                filteredFlags.map(flag => (
                  <div key={flag.id} className="p-4">
                    <div 
                      className="flex items-center justify-between cursor-pointer"
                      onClick={() => handleExpandFlag(flag.id)}
                    >
                      <div className="flex items-center gap-4">
                        <div className="flex flex-col items-center">
                          <div 
                            className={`h-6 w-12 rounded-full ${flag.enabled ? 'bg-green-100' : 'bg-gray-100'} cursor-pointer`}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleToggleFlag(flag.id);
                            }}
                          >
                            <div 
                              className={`transform transition-transform h-6 w-6 rounded-full ${flag.enabled ? 'bg-green-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}
                            ></div>
                          </div>
                          {flag.rolloutPercentage < 100 && flag.enabled && (
                            <div className="text-xs text-gray-500 mt-1">{flag.rolloutPercentage}%</div>
                          )}
                        </div>
                        <div>
                          <div className="font-medium">{flag.name}</div>
                          <div className="text-sm text-gray-500 font-mono">{flag.key}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getCategoryBadge(flag.category)}
                        {expandedFlag === flag.id ? (
                          <ChevronUp className="h-4 w-4 text-gray-400" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                    </div>
                    
                    {expandedFlag === flag.id && (
                      <div className="mt-4 pl-16">
                        <p className="text-gray-600 mb-4">{flag.description}</p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-4">
                          <div className="text-sm">
                            <span className="text-gray-500">Erstellt am:</span>{' '}
                            {new Date(flag.createdAt).toLocaleDateString()}
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-500">Letztes Update:</span>{' '}
                            {new Date(flag.updatedAt).toLocaleDateString()}
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-500">Tenant-konfigurierbar:</span>{' '}
                            {flag.tenantConfigurable ? 'Ja' : 'Nein'}
                          </div>
                        </div>
                        
                        {(flag.rules.tenantPlans.length > 0 || 
                         flag.rules.userRoles.length > 0 || 
                         flag.rules.environments.length > 0) && (
                          <div className="mb-4">
                            <div className="text-xs text-gray-500 mb-1">Regeln:</div>
                            
                            <div className="flex flex-wrap gap-2">
                              {flag.rules.tenantPlans.length > 0 && (
                                <div className="px-2 py-1 bg-purple-50 text-purple-700 rounded-full text-xs flex items-center">
                                  <Building className="h-3 w-3 mr-1" />
                                  Pläne: {flag.rules.tenantPlans.join(', ')}
                                </div>
                              )}
                              
                              {flag.rules.userRoles.length > 0 && (
                                <div className="px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs flex items-center">
                                  <Users className="h-3 w-3 mr-1" />
                                  Rollen: {flag.rules.userRoles.join(', ')}
                                </div>
                              )}
                              
                              {flag.rules.environments.length > 0 && (
                                <div className="px-2 py-1 bg-green-50 text-green-700 rounded-full text-xs flex items-center">
                                  <Settings className="h-3 w-3 mr-1" />
                                  Umgebungen: {flag.rules.environments.join(', ')}
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                        
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" className="text-xs" onClick={() => handleFlagSelect(flag)}>
                            <Edit className="h-3.5 w-3.5 mr-1" /> Bearbeiten
                          </Button>
                          <Button variant="outline" size="sm" className="text-xs" onClick={() => handleShowMetrics(flag)}>
                            <BarChart4 className="h-3.5 w-3.5 mr-1" /> Metriken
                          </Button>
                          <Button variant="outline" size="sm" className="text-xs">
                            <Archive className="h-3.5 w-3.5 mr-1" /> Archivieren
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </>
    );
  };
  
  const renderFlagForm = () => {
    const isEditMode = view === 'edit';
    const flag = isEditMode ? selectedFlag : null;
    
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToList}>
            Zurück
          </Button>
          <h1 className="text-2xl font-bold">
            {isEditMode ? `Feature Flag bearbeiten: ${flag.name}` : 'Neues Feature Flag erstellen'}
          </h1>
        </div>
        
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Grundinformationen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input 
                  type="text" 
                  className="w-full p-2 border rounded-md" 
                  placeholder="z.B. Neues Dashboard"
                  defaultValue={flag?.name || ''}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Key</label>
                <input 
                  type="text" 
                  className="w-full p-2 border rounded-md font-mono" 
                  placeholder="z.B. new_dashboard"
                  defaultValue={flag?.key || ''}
                />
                <p className="text-xs text-gray-500 mt-1">Wird im Code verwendet, um das Feature zu identifizieren</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Beschreibung</label>
                <textarea 
                  className="w-full p-2 border rounded-md h-20" 
                  placeholder="Beschreiben Sie das Feature und seine Auswirkungen"
                  defaultValue={flag?.description || ''}
                ></textarea>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Startdatum</label>
                  <input 
                    type="datetime-local" 
                    className="w-full p-2 border rounded-md"
                    defaultValue={flag?.startsAt || ''}
                  />
                  <p className="text-xs text-gray-500 mt-1">Leer lassen für sofortige Aktivierung</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Enddatum</label>
                  <input 
                    type="datetime-local" 
                    className="w-full p-2 border rounded-md"
                    defaultValue={flag?.endsAt || ''}
                  />
                  <p className="text-xs text-gray-500 mt-1">Leer lassen, wenn kein Enddatum gewünscht ist</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Abhängigkeiten (andere Features, die aktiviert sein müssen)</label>
                <select 
                  multiple 
                  className="w-full p-2 border rounded-md h-24"
                  defaultValue={flag?.dependencies || []}
                >
                  {featureFlags
                    .filter(f => f.id !== (flag?.id || 0))
                    .map(f => (
                      <option key={f.id} value={f.key}>{f.name} ({f.key})</option>
                    ))
                  }
                </select>
              </div>
            </div>
            
            <div className="flex justify-end mt-6">
              <Button variant="outline" className="mr-2" onClick={handleBackToList}>
                Abbrechen
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                {isEditMode ? 'Änderungen speichern' : 'Feature Flag erstellen'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </>
    );
  };
  
  const renderFlagMetrics = () => {
    if (!selectedFlag) return null;
    
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToList}>
            Zurück
          </Button>
          <h1 className="text-2xl font-bold">
            Metriken für: {selectedFlag.name}
          </h1>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Nutzung</p>
                  <h3 className="text-2xl font-bold">{selectedFlag.metrics.usageCount.toLocaleString()}</h3>
                </div>
                <Activity className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Erfolgsrate</p>
                  <h3 className="text-2xl font-bold">{selectedFlag.metrics.successRate}%</h3>
                </div>
                <Check className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Fehlerrate</p>
                  <h3 className="text-2xl font-bold">{selectedFlag.metrics.errorRate}%</h3>
                </div>
                <X className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Latenz</p>
                  <h3 className="text-2xl font-bold">{selectedFlag.metrics.averageLatency} ms</h3>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Nutzungsstatistiken</CardTitle>
          </CardHeader>
          <CardContent className="h-80 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <BarChart4 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="font-medium mb-1">Nutzungsgraph</h3>
              <p>Hier würde ein Diagramm mit detaillierten Nutzungsstatistiken angezeigt werden.</p>
            </div>
          </CardContent>
        </Card>
        
        <div className="flex justify-end mt-6">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={handleBackToList}
          >
            Zurück zur Übersicht
          </Button>
        </div>
      </>
    );
  };
  
  // Render the appropriate view
  const renderView = () => {
    switch (view) {
      case 'create':
      case 'edit':
        return renderFlagForm();
      case 'metrics':
        return renderFlagMetrics();
      case 'list':
      default:
        return renderFlagList();
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {renderView()}
    </div>
  );
};

export default FeatureFlagManagement; text-sm font-medium text-gray-700 mb-1">Kategorie</label>
                  <select 
                    className="w-full p-2 border rounded-md"
                    defaultValue={flag?.category || 'ui'}
                  >
                    <option value="ui">UI</option>
                    <option value="analytics">Analytics</option>
                    <option value="ai">AI</option>
                    <option value="accessibility">Accessibility</option>
                    <option value="core">Core</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <div className="flex items-center gap-2 h-10">
                    <label className="inline-flex items-center cursor-pointer">
                      <input 
                        type="checkbox" 
                        className="sr-only peer" 
                        checked={flag?.enabled || false}
                        onChange={() => {}}
                      />
                      <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      <span className="ml-3 text-sm font-medium text-gray-900">
                        {flag?.enabled ? 'Aktiviert' : 'Deaktiviert'}
                      </span>
                    </label>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Rollout-Prozentsatz</label>
                <div className="flex items-center gap-2">
                  <input 
                    type="range" 
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer" 
                    min="0" 
                    max="100" 
                    step="5"
                    defaultValue={flag?.rolloutPercentage || 100}
                  />
                  <span className="w-12 text-center">{flag?.rolloutPercentage || 100}%</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">Prozentsatz der Nutzer, die das Feature sehen können</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Regeln und Beschränkungen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tenant-Pläne (mindestens erforderlich)</label>
                <select 
                  multiple 
                  className="w-full p-2 border rounded-md h-24" 
                  defaultValue={flag?.rules?.tenantPlans || []}
                >
                  <option value="free">Free</option>
                  <option value="basic">Basic</option>
                  <option value="professional">Professional</option>
                  <option value="enterprise">Enterprise</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Strg-Klick zur Mehrfachauswahl</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Benutzerrollen (mindestens erforderlich)</label>
                <select 
                  multiple 
                  className="w-full p-2 border rounded-md h-24"
                  defaultValue={flag?.rules?.userRoles || []}
                >
                  <option value="admin">Administrator</option>
                  <option value="manager">Manager</option>
                  <option value="agent">Agent</option>
                  <option value="user">Standard-Benutzer</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Leer lassen, um allen Benutzerrollen Zugriff zu gewähren</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Umgebungen</label>
                <select 
                  multiple 
                  className="w-full p-2 border rounded-md h-24"
                  defaultValue={flag?.rules?.environments || []}
                >
                  <option value="development">Development</option>
                  <option value="staging">Staging</option>
                  <option value="production">Production</option>
                </select>
              </div>
              
              <div className="flex items-center gap-2">
                <input 
                  type="checkbox" 
                  id="tenantConfigurable" 
                  className="rounded border-gray-300"
                  checked={flag?.tenantConfigurable || false}
                  onChange={() => {}}
                />
                <label htmlFor="tenantConfigurable" className="text-sm text-gray-700">
                  Tenant-konfigurierbar (Tenant-Administratoren können das Feature aktivieren/deaktivieren)
                </label>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Zeitplan und Abhängigkeiten</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block