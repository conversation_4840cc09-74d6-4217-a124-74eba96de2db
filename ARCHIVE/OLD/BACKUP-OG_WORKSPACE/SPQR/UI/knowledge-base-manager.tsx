import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Upload, FileText, Folder, Search, Filter, TrashIcon, 
  RefreshCw, Tag, ChevronDown, ChevronUp, Edit, 
  Download, Eye, AlertCircle, CheckCircle, X, Settings
} from 'lucide-react';

const KnowledgeBaseManager = () => {
  const [loading, setLoading] = useState(true);
  const [documents, setDocuments] = useState([]);
  const [collections, setCollections] = useState([]);
  const [activeView, setActiveView] = useState('documents'); // documents, collections, settings
  const [activeCollection, setActiveCollection] = useState(null);
  const [expandedDocument, setExpandedDocument] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    collection: 'all',
    status: 'all',
    fileType: 'all'
  });
  
  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      // Mock collections data
      setCollections([
        { 
          id: 1, 
          name: 'Produktdokumente', 
          description: 'Produktbeschreibungen und Anleitungen', 
          documentCount: 23, 
          lastUpdated: '2025-03-08T14:22:45'
        },
        { 
          id: 2, 
          name: 'Support-Wissensbasis', 
          description: 'FAQ und Troubleshooting-Dokumente', 
          documentCount: 58, 
          lastUpdated: '2025-03-09T08:17:32'
        },
        { 
          id: 3, 
          name: 'Interne Richtlinien', 
          description: 'Unternehmensrichtlinien und Prozesse', 
          documentCount: 12, 
          lastUpdated: '2025-02-28T16:45:11'
        },
        { 
          id: 4, 
          name: 'Vertriebsmaterialien', 
          description: 'Verkaufspräsentationen und Preislisten', 
          documentCount: 17, 
          lastUpdated: '2025-03-05T11:20:35'
        }
      ]);
      
      // Mock documents data
      setDocuments([
        { 
          id: 1, 
          filename: 'Produktkatalog_2025.pdf', 
          collection: 'Produktdokumente',
          collectionId: 1,
          status: 'processed',
          fileType: 'pdf',
          fileSize: 4253000,
          pageCount: 32,
          uploadDate: '2025-02-20T09:15:22',
          processedDate: '2025-02-20T09:16:45',
          chunks: 64,
          tags: ['katalog', 'produkte', '2025'],
          url: '#',
          processingErrors: null,
          metadata: {
            author: 'Marketing-Abteilung',
            lastModified: '2025-02-18'
          }
        },
        { 
          id: 2, 
          filename: 'Installation_Guide_v3.2.pdf', 
          collection: 'Produktdokumente',
          collectionId: 1,
          status: 'processed',
          fileType: 'pdf',
          fileSize: 2180000,
          pageCount: 15,
          uploadDate: '2025-02-25T14:12:30',
          processedDate: '2025-02-25T14:13:22',
          chunks: 31,
          tags: ['anleitung', 'installation'],
          url: '#',
          processingErrors: null,
          metadata: {
            author: 'Technische Dokumentation',
            lastModified: '2025-02-24'
          }
        },
        { 
          id: 3, 
          filename: 'Common_Issues_FAQ.docx', 
          collection: 'Support-Wissensbasis',
          collectionId: 2,
          status: 'processing',
          fileType: 'docx',
          fileSize: 546000,
          pageCount: null,
          uploadDate: '2025-03-09T08:17:32',
          processedDate: null,
          chunks: null,
          tags: ['faq', 'support'],
          url: '#',
          processingErrors: null,
          metadata: {
            author: 'Support-Team',
            lastModified: '2025-03-08'
          }
        },
        { 
          id: 4, 
          filename: 'Annual_Report_2024.pptx', 
          collection: 'Vertriebsmaterialien',
          collectionId: 4,
          status: 'error',
          fileType: 'pptx',
          fileSize: 8920000,
          pageCount: 42,
          uploadDate: '2025-03-05T11:20:35',
          processedDate: '2025-03-05T11:22:10',
          chunks: null,
          tags: ['bericht', 'jahresbericht', '2024'],
          url: '#',
          processingErrors: 'Fehler beim Verarbeiten der Folie 23: Unerwartetes Format',
          metadata: {
            author: 'Finanzabteilung',
            lastModified: '2025-03-04'
          }
        },
        { 
          id: 5, 
          filename: 'Security_Guidelines_2025.pdf', 
          collection: 'Interne Richtlinien',
          collectionId: 3,
          status: 'processed',
          fileType: 'pdf',
          fileSize: 1240000,
          pageCount: 8,
          uploadDate: '2025-02-28T16:45:11',
          processedDate: '2025-02-28T16:46:02',
          chunks: 16,
          tags: ['sicherheit', 'richtlinien'],
          url: '#',
          processingErrors: null,
          metadata: {
            author: 'IT-Sicherheit',
            lastModified: '2025-02-27'
          }
        }
      ]);
      
      setLoading(false);
    }, 1500);
  }, []);
  
  const getStatusIcon = (status) => {
    switch (status) {
      case 'processed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };
  
  const getStatusText = (status) => {
    switch (status) {
      case 'processed':
        return 'Verarbeitet';
      case 'processing':
        return 'Wird verarbeitet';
      case 'error':
        return 'Fehler';
      default:
        return status;
    }
  };
  
  const getFileTypeIcon = (fileType) => {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-500" />;
      case 'docx':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'pptx':
        return <FileText className="h-5 w-5 text-orange-500" />;
      case 'xlsx':
        return <FileText className="h-5 w-5 text-green-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };
  
  const formatFileSize = (bytes) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  };
  
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };
  
  const handleFilterChange = (field, value) => {
    setFilters({
      ...filters,
      [field]: value
    });
  };
  
  const handleCollectionClick = (collectionId) => {
    setActiveCollection(collectionId);
    setActiveView('documents');
    handleFilterChange('collection', collectionId);
  };
  
  const toggleDocumentExpand = (docId) => {
    if (expandedDocument === docId) {
      setExpandedDocument(null);
    } else {
      setExpandedDocument(docId);
    }
  };
  
  // Filter documents based on search and filters
  const filteredDocuments = documents.filter(doc => {
    // Filter by search query
    if (searchQuery && !doc.filename.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Filter by collection
    if (filters.collection !== 'all' && doc.collectionId !== parseInt(filters.collection)) {
      return false;
    }
    
    // Filter by status
    if (filters.status !== 'all' && doc.status !== filters.status) {
      return false;
    }
    
    // Filter by file type
    if (filters.fileType !== 'all' && doc.fileType !== filters.fileType) {
      return false;
    }
    
    return true;
  });
  
  const renderDocuments = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Wissensdatenbank</h1>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className="flex items-center gap-1"
              onClick={() => setActiveView('collections')}
            >
              <Folder className="h-4 w-4" />
              Kollektionen
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1"
            >
              <Upload className="h-4 w-4" />
              Dokument hochladen
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="md:col-span-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Dokumente durchsuchen..."
                className="w-full px-10 py-2 border rounded-md"
                value={searchQuery}
                onChange={handleSearchChange}
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              {searchQuery && (
                <button 
                  className="absolute right-3 top-2.5"
                  onClick={() => setSearchQuery('')}
                >
                  <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>
          </div>
          
          <div>
            <Button
              variant="outline"
              className="w-full flex items-center justify-center gap-1"
            >
              <Filter className="h-4 w-4" />
              Filter
            </Button>
          </div>
        </div>
        
        <div className="mb-6">
          <div className="flex gap-2 flex-wrap">
            {/* Collection filter pills */}
            <div className="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center">
              <span className="mr-1 text-gray-600">Kollektion:</span>
              <select 
                className="bg-transparent border-none text-blue-600 font-medium focus:outline-none cursor-pointer"
                value={filters.collection}
                onChange={(e) => handleFilterChange('collection', e.target.value)}
              >
                <option value="all">Alle</option>
                {collections.map(col => (
                  <option key={col.id} value={col.id}>{col.name}</option>
                ))}
              </select>
            </div>
            
            {/* Status filter pills */}
            <div className="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center">
              <span className="mr-1 text-gray-600">Status:</span>
              <select 
                className="bg-transparent border-none text-blue-600 font-medium focus:outline-none cursor-pointer"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="all">Alle</option>
                <option value="processed">Verarbeitet</option>
                <option value="processing">Wird verarbeitet</option>
                <option value="error">Fehler</option>
              </select>
            </div>
            
            {/* File type filter pills */}
            <div className="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center">
              <span className="mr-1 text-gray-600">Dateityp:</span>
              <select 
                className="bg-transparent border-none text-blue-600 font-medium focus:outline-none cursor-pointer"
                value={filters.fileType}
                onChange={(e) => handleFilterChange('fileType', e.target.value)}
              >
                <option value="all">Alle</option>
                <option value="pdf">PDF</option>
                <option value="docx">DOCX</option>
                <option value="pptx">PPTX</option>
                <option value="xlsx">XLSX</option>
              </select>
            </div>
          </div>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Dokumente ({filteredDocuments.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredDocuments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                Keine Dokumente gefunden. Passen Sie Ihre Suchkriterien an oder laden Sie neue Dokumente hoch.
              </div>
            ) : (
              <div className="space-y-4">
                {filteredDocuments.map(doc => (
                  <div 
                    key={doc.id} 
                    className="border rounded-lg overflow-hidden"
                  >
                    <div 
                      className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
                      onClick={() => toggleDocumentExpand(doc.id)}
                    >
                      <div className="flex items-center gap-4">
                        {getFileTypeIcon(doc.fileType)}
                        <div>
                          <div className="font-medium">{doc.filename}</div>
                          <div className="text-sm text-gray-500">
                            {doc.collection} • {formatFileSize(doc.fileSize)} • Hochgeladen am {new Date(doc.uploadDate).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1" title={getStatusText(doc.status)}>
                          {getStatusIcon(doc.status)}
                          <span className="text-sm">{doc.chunks ? `${doc.chunks} Chunks` : '-'}</span>
                        </div>
                        {expandedDocument === doc.id ? (
                          <ChevronUp className="h-5 w-5 text-gray-400" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                    
                    {expandedDocument === doc.id && (
                      <div className="p-4 border-t bg-gray-50">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h3 className="font-medium mb-3">Dokumentdetails</h3>
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Status:</span>
                                <span className="flex items-center gap-1">
                                  {getStatusIcon(doc.status)} {getStatusText(doc.status)}
                                </span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Dateityp:</span>
                                <span>{doc.fileType.toUpperCase()}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Größe:</span>
                                <span>{formatFileSize(doc.fileSize)}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Seiten:</span>
                                <span>{doc.pageCount || '-'}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Autor:</span>
                                <span>{doc.metadata?.author || '-'}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Letzte Änderung:</span>
                                <span>{doc.metadata?.lastModified || '-'}</span>
                              </div>
                            </div>
                          </div>
                          
                          <div>
                            <h3 className="font-medium mb-3">Verarbeitungsinformationen</h3>
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Hochgeladen am:</span>
                                <span>{new Date(doc.uploadDate).toLocaleString()}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Verarbeitet am:</span>
                                <span>{doc.processedDate ? new Date(doc.processedDate).toLocaleString() : '-'}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Kollektion:</span>
                                <span>{doc.collection}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Chunks:</span>
                                <span>{doc.chunks || '-'}</span>
                              </div>
                              {doc.processingErrors && (
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">Fehler:</span>
                                  <span className="text-red-600">{doc.processingErrors}</span>
                                </div>
                              )}
                              <div className="text-sm">
                                <span className="text-gray-600">Tags:</span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {doc.tags.map(tag => (
                                    <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                      {tag}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex gap-2 justify-end mt-4">
                          <Button variant="outline" className="text-xs flex items-center gap-1">
                            <Download className="h-3.5 w-3.5" />
                            Herunterladen
                          </Button>
                          <Button variant="outline" className="text-xs flex items-center gap-1">
                            <Eye className="h-3.5 w-3.5" />
                            Vorschau
                          </Button>
                          <Button variant="outline" className="text-xs flex items-center gap-1">
                            <Edit className="h-3.5 w-3.5" />
                            Bearbeiten
                          </Button>
                          <Button variant="outline" className="text-xs flex items-center gap-1 text-red-600">
                            <TrashIcon className="h-3.5 w-3.5" />
                            Löschen
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </>
    );
  };
  
  const renderCollections = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Kollektionen</h1>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className="flex items-center gap-1"
              onClick={() => setActiveView('documents')}
            >
              <FileText className="h-4 w-4" />
              Dokumente
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1"
            >
              <Folder className="h-4 w-4" />
              Neue Kollektion
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {collections.map(collection => (
            <Card 
              key={collection.id} 
              className="hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleCollectionClick(collection.id)}
            >
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Folder className="h-5 w-5 text-blue-500" />
                  {collection.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">{collection.description}</p>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Dokumente:</span>
                    <span className="font-medium">{collection.documentCount}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Letzte Aktualisierung:</span>
                    <span className="font-medium">{new Date(collection.lastUpdated).toLocaleDateString()}</span>
                  </div>
                </div>
                
                <div className="flex gap-2 justify-end mt-4">
                  <Button variant="outline" className="text-xs flex items-center gap-1">
                    <Edit className="h-3.5 w-3.5" />
                    Bearbeiten
                  </Button>
                  <Button variant="outline" className="text-xs flex items-center gap-1 text-red-600">
                    <TrashIcon className="h-3.5 w-3.5" />
                    Löschen
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    );
  };
  
  const renderSettings = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Wissensdatenbank-Einstellungen</h1>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              className="flex items-center gap-1"
              onClick={() => setActiveView('documents')}
            >
              <FileText className="h-4 w-4" />
              Zurück zu Dokumenten
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Verarbeitungseinstellungen</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Embedding-Modell</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="text-embedding-3-large">OpenAI text-embedding-3-large</option>
                      <option value="text-embedding-3-small">OpenAI text-embedding-3-small</option>
                      <option value="text-embedding-ada-002">OpenAI text-embedding-ada-002 (Legacy)</option>
                      <option value="local-e5-large">Local E5-large</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">Bestimmt die Qualität und Geschwindigkeit der semantischen Suche</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Chunk-Größe</label>
                    <input 
                      type="number" 
                      className="w-full p-2 border rounded-md" 
                      defaultValue={512} 
                      min={128} 
                      max={2048}
                    />
                    <p className="text-xs text-gray-500 mt-1">Anzahl der Tokens pro Chunk (empfohlen: 256-1024)</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Chunk-Überlappung</label>
                    <input 
                      type="number" 
                      className="w-full p-2 border rounded-md" 
                      defaultValue={50} 
                      min={0} 
                      max={512}
                    />
                    <p className="text-xs text-gray-500 mt-1">Anzahl der Tokens, die sich zwischen benachbarten Chunks überlappen</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Standard-Abfragemodell</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="claude-3-opus">Claude 3 Opus</option>
                      <option value="claude-3-haiku">Claude 3 Haiku</option>
                      <option value="gpt-4-turbo">GPT-4 Turbo</option>
                      <option value="local-llama">Local Llama 3</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">Modell, das für RAG-Abfragen verwendet wird</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ähnlichkeitsschwellenwert</label>
                    <input 
                      type="range" 
                      className="w-full" 
                      min={0} 
                      max={1} 
                      step={0.01} 
                      defaultValue={0.75}
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Niedrig (0.5)</span>
                      <span>0.75</span>
                      <span>Hoch (0.95)</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Minimaler Ähnlichkeitswert für Vektorsuchen</p>
                  </div>
                  
                  <div className="flex justify-end">
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                      Einstellungen speichern
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Wissensdatenbank-Statistiken</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm text-gray-500 mb-1">Gesamtzahl Dokumente</h3>
                    <p className="text-2xl font-bold">{documents.length}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm text-gray-500 mb-1">Kollektionen</h3>
                    <p className="text-2xl font-bold">{collections.length}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm text-gray-500 mb-1">Verarbeitete Chunks</h3>
                    <p className="text-2xl font-bold">
                      {documents
                        .filter(doc => doc.status === 'processed')
                        .reduce((sum, doc) => sum + (doc.chunks || 0), 0)}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm text-gray-500 mb-1">Speichernutzung</h3>
                    <p className="text-2xl font-bold">
                      {formatFileSize(documents.reduce((sum, doc) => sum + doc.fileSize, 0))}
                    </p>
                  </div>
                </div>
                
                <div className="mt-6">
                  <Button variant="outline" className="w-full flex items-center justify-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Index neu erstellen
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Verarbeitungswarteschlange</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="py-4 text-center text-sm text-gray-500">
                  <RefreshCw className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                  <p>1 Dokument in Verarbeitung</p>
                  <p>0 Dokumente in Warteschlange</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  };
  
  const renderActiveView = () => {
    switch (activeView) {
      case 'collections':
        return renderCollections();
      case 'settings':
        return renderSettings();
      case 'documents':
      default:
        return renderDocuments();
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Main Navigation Tabs */}
      <div className="border-b mb-6">
        <div className="flex gap-6">
          <button 
            className={`pb-2 px-1 ${activeView === 'documents' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveView('documents')}
          >
            Dokumente
          </button>
          <button 
            className={`pb-2 px-1 ${activeView === 'collections' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveView('collections')}
          >
            Kollektionen
          </button>
          <button 
            className={`pb-2 px-1 ${activeView === 'settings' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveView('settings')}
          >
            Einstellungen
          </button>
        </div>
      </div>
      
      {renderActiveView()}
    </div>
  );
};

export default KnowledgeBaseManager;