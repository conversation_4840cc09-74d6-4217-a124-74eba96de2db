import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Search, Upload, Database, RefreshCw, Info, Eye, Trash2, Filter, Download } from 'lucide-react';

const VectorStoreExplorer = () => {
  const [view, setView] = useState('collections'); // collections, collection-detail, search
  const [collections, setCollections] = useState([]);
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [searchResults, setSearchResults] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Mock data loading
    setTimeout(() => {
      setCollections([
        { 
          id: 1, 
          name: 'Produktdokumentation', 
          count: 12450, 
          lastUpdated: '2025-03-08T15:23:45',
          embedding: 'text-embedding-3-large',
          dimensions: 3072,
          description: 'Enthält alle Produkthandbücher und technische Dokumentation'
        },
        { 
          id: 2, 
          name: 'Kundensupport-Wissensbasis', 
          count: 8736, 
          lastUpdated: '2025-03-09T09:12:30',
          embedding: 'text-embedding-3-large',
          dimensions: 3072,
          description: 'Häufig gestellte Fragen und Support-Artikel'
        },
        { 
          id: 3, 
          name: 'Interne Richtlinien', 
          count: 3214, 
          lastUpdated: '2025-03-07T11:34:22',
          embedding: 'text-embedding-3-large',
          dimensions: 3072,
          description: 'Interne Unternehmensrichtlinien und Verfahren'
        },
        { 
          id: 4, 
          name: 'Forschungsdokumente', 
          count: 5860, 
          lastUpdated: '2025-03-05T16:45:10',
          embedding: 'text-embedding-3-small',
          dimensions: 1536,
          description: 'Wissenschaftliche Studien und Forschungsergebnisse'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const mockSearchResults = [
    {
      id: 1,
      text: "Die SPQR-Plattform unterstützt mehrere LLM-Modelle, darunter Claude, OpenAI und lokale LLMs wie Llama.",
      metadata: {
        source: "SPQR-Dokumentation_v1.2.pdf",
        page: 14,
        collection: "Produktdokumentation",
        created: "2025-02-12T10:24:15"
      },
      similarity: 0.92
    },
    {
      id: 2,
      text: "Für die Integration verschiedener Sprachmodelle stellt SPQR eine einheitliche API bereit, die den Wechsel zwischen Claude, GPT-4 und lokalen Modellen vereinfacht.",
      metadata: {
        source: "Entwicklerhandbuch.pdf",
        page: 87,
        collection: "Produktdokumentation",
        created: "2025-01-28T13:15:22"
      },
      similarity: 0.89
    },
    {
      id: 3,
      text: "Das System ermöglicht die dynamische Auswahl des besten Modells je nach Anwendungsfall, wobei zwischen Claude für komplexe Reasoning-Aufgaben, OpenAI für kreative Inhalte und lokalen LLMs für datenschutzkritische Anwendungen gewählt werden kann.",
      metadata: {
        source: "Modellauswahl_Leitfaden.docx",
        page: 3,
        collection: "Produktdokumentation",
        created: "2025-02-05T09:45:33"
      },
      similarity: 0.86
    },
    {
      id: 4,
      text: "Die SPQR-Einstellungen erlauben die Konfiguration von Modellparametern wie Temperatur, Max-Tokens und Antwortformatierung für jedes unterstützte LLM.",
      metadata: {
        source: "Admin_Handbuch.pdf",
        page: 42,
        collection: "Produktdokumentation",
        created: "2025-02-18T16:30:10"
      },
      similarity: 0.84
    },
    {
      id: 5,
      text: "Für latenz-sensitive Anwendungen bietet SPQR eine LLM-Routing-Funktion, die je nach Verfügbarkeit und Antwortzeit automatisch zwischen den konfigurierten Modellen wechselt.",
      metadata: {
        source: "Performance_Optimierung.pptx",
        page: 15,
        collection: "Produktdokumentation",
        created: "2025-03-01T11:22:45"
      },
      similarity: 0.81
    }
  ];

  const handleSearch = () => {
    if (searchQuery.trim() === '') return;
    
    setLoading(true);
    setTimeout(() => {
      setSearchResults(mockSearchResults);
      setView('search');
      setLoading(false);
    }, 800);
  };

  const handleCollectionSelect = (collection) => {
    setSelectedCollection(collection);
    setView('collection-detail');
  };

  const handleBackToCollections = () => {
    setSelectedCollection(null);
    setView('collections');
  };

  const handleBackToCollection = () => {
    setView('collection-detail');
  };

  const renderCollectionsList = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      );
    }

    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Vector Store Explorer</h1>
          <div className="flex gap-2">
            <Button
              className="flex items-center gap-1 bg-blue-600 text-white"
            >
              <Upload className="h-4 w-4" />
              Dokumente hochladen
            </Button>
            <Button
              variant="outline"
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-4 w-4" />
              Aktualisieren
            </Button>
          </div>
        </div>

        <div className="bg-white p-4 rounded-md border mb-6">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="Vektorsuche durchführen..."
                className="w-full pl-10 pr-4 py-2 border rounded-md"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Search className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
            </div>
            <Button
              className="bg-blue-600 text-white"
              onClick={handleSearch}
            >
              Suchen
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {collections.map(collection => (
            <Card 
              key={collection.id} 
              className="hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleCollectionSelect(collection)}
            >
              <CardHeader className="pb-2">
                <CardTitle>{collection.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">{collection.description}</p>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Vektoren:</span>
                    <span className="font-medium">{collection.count.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Embedding-Modell:</span>
                    <span className="font-medium">{collection.embedding}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Dimensionen:</span>
                    <span className="font-medium">{collection.dimensions}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Letzte Aktualisierung:</span>
                    <span className="font-medium">{new Date(collection.lastUpdated).toLocaleDateString('de-DE')}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    );
  };

  const renderCollectionDetail = () => {
    if (!selectedCollection) return null;

    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToCollections}>
            Zurück
          </Button>
          <h1 className="text-2xl font-bold">{selectedCollection.name}</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Übersicht</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-sm text-gray-600">{selectedCollection.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Vektoren:</span>
                  <span className="font-medium">{selectedCollection.count.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Embedding-Modell:</span>
                  <span className="font-medium">{selectedCollection.embedding}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Dimensionen:</span>
                  <span className="font-medium">{selectedCollection.dimensions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Letzte Aktualisierung:</span>
                  <span className="font-medium">{new Date(selectedCollection.lastUpdated).toLocaleDateString('de-DE')}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Statistiken</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Speichernutzung:</span>
                  <span className="font-medium">2.4 GB</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Durchschn. Vektorlänge:</span>
                  <span className="font-medium">512 Tokens</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Quellen:</span>
                  <span className="font-medium">87 Dokumente</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Abfragen pro Tag:</span>
                  <span className="font-medium">~1250</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Aktionen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button className="w-full flex justify-center items-center gap-2">
                  <Upload className="h-4 w-4" />
                  Dokumente hinzufügen
                </Button>
                <Button variant="outline" className="w-full flex justify-center items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Index neu erstellen
                </Button>
                <Button variant="outline" className="w-full flex justify-center items-center gap-2">
                  <Download className="h-4 w-4" />
                  Metadaten exportieren
                </Button>
                <Button variant="outline" className="w-full flex justify-center items-center gap-2 text-red-600">
                  <Trash2 className="h-4 w-4" />
                  Collection löschen
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">Dokumente</CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" className="text-xs flex items-center gap-1">
                  <Filter className="h-3 w-3" />
                  Filter
                </Button>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Suchen..."
                    className="pl-8 pr-2 py-1 text-sm border rounded-md"
                  />
                  <Search className="h-3 w-3 text-gray-400 absolute left-2 top-2" />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-center text-gray-500 py-8">
              Hier würde eine paginierte Liste von Dokumenten in dieser Collection angezeigt werden.
            </div>
          </CardContent>
        </Card>
      </>
    );
  };

  const renderSearchResults = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      );
    }

    return (
      <>
        <div className="flex items-center mb-6">
          <Button 
            variant="outline" 
            className="mr-4" 
            onClick={selectedCollection ? handleBackToCollection : handleBackToCollections}
          >
            Zurück
          </Button>
          <h1 className="text-2xl font-bold">Suchergebnisse für "{searchQuery}"</h1>
        </div>

        <div className="mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Suchinformationen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-sm text-gray-500">Anfrage</div>
                  <div className="font-medium">{searchQuery}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Ergebnisse</div>
                  <div className="font-medium">{searchResults.length}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Schwellenwert</div>
                  <div className="font-medium">0.75</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Suchzeit</div>
                  <div className="font-medium">0.24s</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          {searchResults.map(result => (
            <Card key={result.id}>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <p className="text-lg font-medium mb-2">{result.text}</p>
                    <div className="flex flex-wrap gap-2 text-xs text-gray-500">
                      <span className="px-2 py-1 bg-gray-100 rounded-md">
                        Quelle: {result.metadata.source}
                      </span>
                      <span className="px-2 py-1 bg-gray-100 rounded-md">
                        Seite: {result.metadata.page}
                      </span>
                      <span className="px-2 py-1 bg-gray-100 rounded-md">
                        Collection: {result.metadata.collection}
                      </span>
                      <span className="px-2 py-1 bg-gray-100 rounded-md">
                        Erstellt: {new Date(result.metadata.created).toLocaleDateString('de-DE')}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4 flex flex-col items-end">
                    <div className="text-lg font-bold text-blue-600">
                      {(result.similarity * 100).toFixed(0)}%
                    </div>
                    <div className="text-xs text-gray-500">Ähnlichkeit</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    );
  };

  // Render the appropriate view
  const renderContent = () => {
    switch (view) {
      case 'collection-detail':
        return renderCollectionDetail();
      case 'search':
        return renderSearchResults();
      case 'collections':
      default:
        return renderCollectionsList();
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {renderContent()}
    </div>
  );
};

export default VectorStoreExplorer;
