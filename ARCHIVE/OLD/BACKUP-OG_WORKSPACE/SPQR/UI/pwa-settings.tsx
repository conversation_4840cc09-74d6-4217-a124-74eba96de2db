import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Settings, RefreshCw, Globe, Smartphone, Bell, Database, 
  Cloud, CloudOff, CheckSquare, Wifi, WifiOff, Share2,
  Save, Trash, Code, Upload, Download, Lock
} from 'lucide-react';

const PWASettings = () => {
  const [loading, setLoading] = useState(true);
  const [pwaConfig, setPwaConfig] = useState(null);
  const [activeTab, setActiveTab] = useState('general'); // general, offline, sync, notifications
  const [networkStatus, setNetworkStatus] = useState('online');
  const [syncStatus, setSyncStatus] = useState({
    lastSync: '2025-03-09T10:24:35',
    pendingChanges: 3,
    syncInProgress: false
  });
  
  useEffect(() => {
    // Mock data loading
    setTimeout(() => {
      setPwaConfig({
        general: {
          appName: 'AI-First CRM',
          shortName: 'AICRM',
          description: 'AI-gestützte CRM-Plattform für moderne Teams',
          themeColor: '#3b82f6',
          backgroundColor: '#ffffff',
          displayMode: 'standalone', // standalone, fullscreen, minimal-ui, browser
          orientation: 'any', // any, portrait, landscape
          iconPath: '/images/icon-512x512.png',
          splashScreenEnabled: true,
          installPromptEnabled: true,
          customInstallPrompt: {
            title: 'Installieren Sie unsere App',
            description: 'Installieren Sie die AI-CRM App für eine bessere Erfahrung und Offline-Funktionalität.',
            acceptButtonText: 'Installieren',
            dismissButtonText: 'Später',
            showAfterDismissalDays: 7
          }
        },
        offline: {
          enabled: true,
          cachingStrategy: 'network-first', // cache-first, network-first, stale-while-revalidate, network-only
          maxCacheSize: 50, // In MB
          dynamicCachingEnabled: true,
          appsShellCachingEnabled: true,
          offlinePageEnabled: true,
          offlinePagePath: '/offline.html',
          cachedRoutes: [
            '/',
            '/dashboard',
            '/customers',
            '/deals'
          ],
          dataStorage: {
            customers: true,
            deals: true,
            interactions: true,
            tasks: true
          }
        },
        sync: {
          enabled: true,
          backgroundSyncEnabled: true,
          syncInterval: 15, // minutes
          retryStrategy: 'exponential', // exponential, fixed, progressive
          maxRetries: 5,
          syncOnReconnect: true,
          priorityQueues: {
            highPriority: ['deals', 'customers'],
            mediumPriority: ['interactions'],
            lowPriority: ['analytics', 'logs']
          }
        },
        notifications: {
          enabled: true,
          promptOnFirstVisit: false,
          promptDelay: 3, // days
          defaultNotificationTitle: 'AI-First CRM',
          notificationTypes: {
            dealUpdates: {
              enabled: true,
              grouping: true,
              sound: true,
              icon: '/images/deal-icon.png'
            },
            taskReminders: {
              enabled: true,
              grouping: false,
              sound: true,
              icon: '/images/task-icon.png'
            },
            systemAlerts: {
              enabled: true,
              grouping: false,
              sound: false,
              icon: '/images/system-icon.png'
            }
          },
          notificationActions: {
            enabled: true,
            maxActions: 3
          }
        }
      });
      setLoading(false);
    }, 1500);
  }, []);
  
  const handleNetworkStatusChange = () => {
    // Simulate network status change
    setNetworkStatus(prev => prev === 'online' ? 'offline' : 'online');
  };
  
  const handleSyncNow = () => {
    // Simulate sync
    setSyncStatus(prev => ({
      ...prev,
      syncInProgress: true
    }));
    
    setTimeout(() => {
      setSyncStatus({
        lastSync: new Date().toISOString(),
        pendingChanges: 0,
        syncInProgress: false
      });
    }, 2000);
  };
  
  const renderGeneralSettings = () => {
    if (!pwaConfig) return null;
    
    const { general } = pwaConfig;
    
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Allgemeine Einstellungen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">App Name</label>
                  <input 
                    type="text" 
                    className="w-full p-2 border rounded-md" 
                    defaultValue={general.appName}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Kurzname</label>
                  <input 
                    type="text" 
                    className="w-full p-2 border rounded-md" 
                    defaultValue={general.shortName}
                  />
                  <p className="text-xs text-gray-500 mt-1">Wird auf dem Home-Bildschirm angezeigt</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Beschreibung</label>
                <textarea 
                  className="w-full p-2 border rounded-md h-20" 
                  defaultValue={general.description}
                ></textarea>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Theme Farbe</label>
                  <div className="flex items-center">
                    <input 
                      type="color" 
                      className="w-12 h-10 border rounded-md" 
                      defaultValue={general.themeColor}
                    />
                    <input 
                      type="text" 
                      className="w-full p-2 border rounded-md ml-2" 
                      defaultValue={general.themeColor}
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Hintergrundfarbe</label>
                  <div className="flex items-center">
                    <input 
                      type="color" 
                      className="w-12 h-10 border rounded-md" 
                      defaultValue={general.backgroundColor}
                    />
                    <input 
                      type="text" 
                      className="w-full p-2 border rounded-md ml-2" 
                      defaultValue={general.backgroundColor}
                    />
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Anzeigemodus</label>
                  <select 
                    className="w-full p-2 border rounded-md" 
                    defaultValue={general.displayMode}
                  >
                    <option value="standalone">Standalone</option>
                    <option value="fullscreen">Vollbild</option>
                    <option value="minimal-ui">Minimale UI</option>
                    <option value="browser">Browser</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Ausrichtung</label>
                  <select 
                    className="w-full p-2 border rounded-md" 
                    defaultValue={general.orientation}
                  >
                    <option value="any">Beliebig</option>
                    <option value="portrait">Hochformat</option>
                    <option value="landscape">Querformat</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">App-Icon</label>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                    <Smartphone className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-4 flex-1">
                    <input 
                      type="text" 
                      className="w-full p-2 border rounded-md" 
                      defaultValue={general.iconPath}
                    />
                  </div>
                  <Button variant="outline" className="ml-2">
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1">Mindestens 512x512 px, PNG-Format empfohlen</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Installations-Aufforderung</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Installation-Aufforderung aktivieren</h3>
                  <p className="text-sm text-gray-600">Zeigt Benutzern eine Aufforderung zum Installieren der App an</p>
                </div>
                <div className="h-6 w-12 rounded-full bg-blue-100 cursor-pointer">
                  <div className={`transform transition-transform h-6 w-6 rounded-full ${general.installPromptEnabled ? 'bg-blue-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}></div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Splash Screen aktivieren</h3>
                  <p className="text-sm text-gray-600">Zeigt einen Splash Screen beim Start der App an</p>
                </div>
                <div className="h-6 w-12 rounded-full bg-blue-100 cursor-pointer">
                  <div className={`transform transition-transform h-6 w-6 rounded-full ${general.splashScreenEnabled ? 'bg-blue-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}></div>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Benutzerdefinierte Installations-Aufforderung</h3>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Titel</label>
                    <input 
                      type="text" 
                      className="w-full p-2 border rounded-md" 
                      defaultValue={general.customInstallPrompt.title}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Beschreibung</label>
                    <textarea 
                      className="w-full p-2 border rounded-md h-20" 
                      defaultValue={general.customInstallPrompt.description}
                    ></textarea>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Akzeptieren-Button Text</label>
                      <input 
                        type="text" 
                        className="w-full p-2 border rounded-md" 
                        defaultValue={general.customInstallPrompt.acceptButtonText}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Ablehnen-Button Text</label>
                      <input 
                        type="text" 
                        className="w-full p-2 border rounded-md" 
                        defaultValue={general.customInstallPrompt.dismissButtonText}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Erneut anzeigen nach Ablehnung (Tage)</label>
                    <input 
                      type="number" 
                      className="w-full p-2 border rounded-md" 
                      defaultValue={general.customInstallPrompt.showAfterDismissalDays}
                      min="1"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };
  
  const renderOfflineSettings = () => {
    if (!pwaConfig) return null;
    
    const { offline } = pwaConfig;
    
    return (
      <div className="space-y-6">
        <Card className="relative">
          {!offline.enabled && (
            <div className="absolute inset-0 bg-white bg-opacity-60 flex items-center justify-center z-10">
              <div className="text-center p-4 bg-white rounded-lg shadow-lg">
                <CloudOff className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <h3 className="font-medium mb-1">Offline-Modus ist deaktiviert</h3>
                <p className="text-sm text-gray-600 mb-3">Aktivieren Sie den Offline-Modus, um diese Einstellungen zu konfigurieren.</p>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  Aktivieren
                </Button>
              </div>
            </div>
          )}
          
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Offline-Modus</CardTitle>
              <div className="h-6 w-12 rounded-full bg-blue-100 cursor-pointer">
                <div className={`transform transition-transform h-6 w-6 rounded-full ${offline.enabled ? 'bg-blue-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}></div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Caching-Strategie</label>
                <select 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={offline.cachingStrategy}
                >
                  <option value="network-first">Network First</option>
                  <option value="cache-first">Cache First</option>
                  <option value="stale-while-revalidate">Stale While Revalidate</option>
                  <option value="network-only">Network Only</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Bestimmt, wie Anfragen während des Offline-Modus bearbeitet werden</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Maximale Cache-Größe (MB)</label>
                <input 
                  type="number" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={offline.maxCacheSize}
                  min="10"
                />
              </div>
              
              <div className="flex flex-col gap-3">
                <div className="flex items-center gap-2">
                  <input 
                    type="checkbox" 
                    id="dynamicCaching" 
                    className="rounded border-gray-300"
                    checked={offline.dynamicCachingEnabled}
                    onChange={() => {}}
                  />
                  <label htmlFor="dynamicCaching" className="text-sm text-gray-700">
                    Dynamisches Caching aktivieren (Cache-Inhalte basierend auf der Nutzung)
                  </label>
                </div>
                
                <div className="flex items-center gap-2">
                  <input 
                    type="checkbox" 
                    id="appShellCaching" 
                    className="rounded border-gray-300"
                    checked={offline.appsShellCachingEnabled}
                    onChange={() => {}}
                  />
                  <label htmlFor="appShellCaching" className="text-sm text-gray-700">
                    App-Shell Caching aktivieren (für schnelleres Laden)
                  </label>
                </div>
                
                <div className="flex items-center gap-2">
                  <input 
                    type="checkbox" 
                    id="offlinePage" 
                    className="rounded border-gray-300"
                    checked={offline.offlinePageEnabled}
                    onChange={() => {}}
                  />
                  <label htmlFor="offlinePage" className="text-sm text-gray-700">
                    Offline-Seite für nicht gecachte Routen anzeigen
                  </label>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Offline-Seite Pfad</label>
                <input 
                  type="text" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={offline.offlinePagePath}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Zu cachende Routen</label>
                <div className="border rounded-md p-2 max-h-40 overflow-y-auto">
                  {offline.cachedRoutes.map((route, index) => (
                    <div key={index} className="flex items-center justify-between mb-1 last:mb-0">
                      <span className="text-sm">{route}</span>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <Trash className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ))}
                  <div className="flex mt-2 pt-2 border-t">
                    <input 
                      type="text" 
                      className="flex-1 p-1 border rounded-l-md text-sm" 
                      placeholder="/new-route"
                    />
                    <Button className="rounded-l-none bg-blue-600">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Offline-Datenspeicherung</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Wählen Sie, welche Daten lokal gespeichert werden sollen für den Offline-Zugriff:
            </p>
            
            <div className="space-y-3">
              {Object.entries(offline.dataStorage).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between p-2 border rounded-md">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-gray-500" />
                    <span className="text-sm capitalize">{key}</span>
                  </div>
                  <div className="h-6 w-12 rounded-full bg-blue-100 cursor-pointer">
                    <div className={`transform transition-transform h-6 w-6 rounded-full ${value ? 'bg-blue-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}></div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Daten automatisch löschen</h3>
                  <p className="text-sm text-gray-600">Offline-Daten nach einem bestimmten Zeitraum automatisch löschen</p>
                </div>
                <Button variant="outline">
                  Konfigurieren
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };
  
  const renderSyncSettings = () => {
    if (!pwaConfig) return null;
    
    const { sync } = pwaConfig;
    
    return (
      <div className="space-y-6">
        <Card className="relative">
          {!sync.enabled && (
            <div className="absolute inset-0 bg-white bg-opacity-60 flex items-center justify-center z-10">
              <div className="text-center p-4 bg-white rounded-lg shadow-lg">
                <CloudOff className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <h3 className="font-medium mb-1">Synchronisation ist deaktiviert</h3>
                <p className="text-sm text-gray-600 mb-3">Aktivieren Sie die Synchronisation, um Daten im Offline-Modus zu bearbeiten.</p>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  Aktivieren
                </Button>
              </div>
            </div>
          )}
          
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Synchronisation</CardTitle>
              <div className="h-6 w-12 rounded-full bg-blue-100 cursor-pointer">
                <div className={`transform transition-transform h-6 w-6 rounded-full ${sync.enabled ? 'bg-blue-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}></div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-md">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div 
                      className={`h-2 w-2 rounded-full ${networkStatus === 'online' ? 'bg-green-500' : 'bg-red-500'}`}
                    ></div>
                    <span className="font-medium">Status: {networkStatus === 'online' ? 'Online' : 'Offline'}</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={handleNetworkStatusChange}
                  >
                    {networkStatus === 'online' ? 'Als offline simulieren' : 'Als online simulieren'}
                  </Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm">Letzte Synchronisation: {new Date(syncStatus.lastSync).toLocaleString()}</div>
                    <div className="text-sm">Ausstehende Änderungen: {syncStatus.pendingChanges}</div>
                  </div>
                  <Button
                    className={`bg-blue-600 hover:bg-blue-700 text-white text-xs ${syncStatus.syncInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={syncStatus.syncInProgress}
                    onClick={handleSyncNow}
                  >
                    {syncStatus.syncInProgress ? (
                      <>
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                        Synchronisiere...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Jetzt synchronisieren
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <input 
                  type="checkbox" 
                  id="backgroundSync" 
                  className="rounded border-gray-300"
                  checked={sync.backgroundSyncEnabled}
                  onChange={() => {}}
                />
                <label htmlFor="backgroundSync" className="text-sm text-gray-700">
                  Hintergrund-Synchronisation aktivieren
                </label>
              </div>
              
              <div className="flex items-center gap-2">
                <input 
                  type="checkbox" 
                  id="syncOnReconnect" 
                  className="rounded border-gray-300"
                  checked={sync.syncOnReconnect}
                  onChange={() => {}}
                />
                <label htmlFor="syncOnReconnect" className="text-sm text-gray-700">
                  Bei Wiederherstellung der Verbindung automatisch synchronisieren
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Synchronisationsintervall (Minuten)</label>
                <input 
                  type="number" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={sync.syncInterval}
                  min="1"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Wiederholungsstrategie</label>
                <select 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={sync.retryStrategy}
                >
                  <option value="exponential">Exponentiell</option>
                  <option value="fixed">Fest</option>
                  <option value="progressive">Progressiv</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Bestimmt, wie fehlgeschlagene Synchronisierungen wiederholt werden</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Maximale Wiederholungen</label>
                <input 
                  type="number" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={sync.maxRetries}
                  min="1"
                />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Synchronisations-Prioritäten</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Konfigurieren Sie, welche Daten mit welcher Priorität synchronisiert werden:
            </p>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Hohe Priorität</label>
                <select 
                  multiple 
                  className="w-full p-2 border rounded-md h-24" 
                  defaultValue={sync.priorityQueues.highPriority}
                >
                  <option value="deals">Angebote</option>
                  <option value="customers">Kunden</option>
                  <option value="interactions">Interaktionen</option>
                  <option value="tasks">Aufgaben</option>
                  <option value="analytics">Analytik</option>
                  <option value="logs">Logs</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Diese werden sofort synchronisiert</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mittlere Priorität</label>
                <select 
                  multiple 
                  className="w-full p-2 border rounded-md h-24" 
                  defaultValue={sync.priorityQueues.mediumPriority}
                >
                  <option value="deals">Angebote</option>
                  <option value="customers">Kunden</option>
                  <option value="interactions">Interaktionen</option>
                  <option value="tasks">Aufgaben</option>
                  <option value="analytics">Analytik</option>
                  <option value="logs">Logs</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Diese werden nach hoher Priorität synchronisiert</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Niedrige Priorität</label>
                <select 
                  multiple 
                  className="w-full p-2 border rounded-md h-24" 
                  defaultValue={sync.priorityQueues.lowPriority}
                >
                  <option value="deals">Angebote</option>
                  <option value="customers">Kunden</option>
                  <option value="interactions">Interaktionen</option>
                  <option value="tasks">Aufgaben</option>
                  <option value="analytics">Analytik</option>
                  <option value="logs">Logs</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Diese werden synchronisiert, wenn Ressourcen verfügbar sind</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };
  
  const renderNotificationSettings = () => {
    if (!pwaConfig) return null;
    
    const { notifications } = pwaConfig;
    
    return (
      <div className="space-y-6">
        <Card className="relative">
          {!notifications.enabled && (
            <div className="absolute inset-0 bg-white bg-opacity-60 flex items-center justify-center z-10">
              <div className="text-center p-4 bg-white rounded-lg shadow-lg">
                <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <h3 className="font-medium mb-1">Benachrichtigungen sind deaktiviert</h3>
                <p className="text-sm text-gray-600 mb-3">Aktivieren Sie Benachrichtigungen, um diese Einstellungen zu konfigurieren.</p>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  Aktivieren
                </Button>
              </div>
            </div>
          )}
          
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Push-Benachrichtigungen</CardTitle>
              <div className="h-6 w-12 rounded-full bg-blue-100 cursor-pointer">
                <div className={`transform transition-transform h-6 w-6 rounded-full ${notifications.enabled ? 'bg-blue-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}></div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Berechtigungsaufforderung beim ersten Besuch</h3>
                  <p className="text-sm text-gray-600">Aufforderung zur Benachrichtigungsberechtigung beim ersten Besuch</p>
                </div>
                <div className="h-6 w-12 rounded-full bg-blue-100 cursor-pointer">
                  <div className={`transform transition-transform h-6 w-6 rounded-full ${notifications.promptOnFirstVisit ? 'bg-blue-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}></div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Verzögerung der Aufforderung (Tage)</label>
                <input 
                  type="number" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={notifications.promptDelay}
                  min="0"
                />
                <p className="text-xs text-gray-500 mt-1">0 für sofortige Aufforderung, oder Anzahl der Tage vor der Aufforderung</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Standard-Benachrichtigungstitel</label>
                <input 
                  type="text" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={notifications.defaultNotificationTitle}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Benachrichtigungsaktionen</h3>
                  <p className="text-sm text-gray-600">Aktionen in Benachrichtigungen anzeigen</p>
                </div>
                <div className="h-6 w-12 rounded-full bg-blue-100 cursor-pointer">
                  <div className={`transform transition-transform h-6 w-6 rounded-full ${notifications.notificationActions.enabled ? 'bg-blue-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}></div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Maximale Anzahl an Aktionen</label>
                <input 
                  type="number" 
                  className="w-full p-2 border rounded-md" 
                  defaultValue={notifications.notificationActions.maxActions}
                  min="1"
                  max="5"
                />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Benachrichtigungstypen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(notifications.notificationTypes).map(([key, settings]) => (
                <div key={key} className="border rounded-md p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</h3>
                    <div className="h-6 w-12 rounded-full bg-blue-100 cursor-pointer">
                      <div className={`transform transition-transform h-6 w-6 rounded-full ${settings.enabled ? 'bg-blue-500 translate-x-6' : 'bg-gray-400 translate-x-0'}`}></div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center gap-2">
                      <input 
                        type="checkbox" 
                        id={`${key}-grouping`} 
                        className="rounded border-gray-300"
                        checked={settings.grouping}
                        onChange={() => {}}
                      />
                      <label htmlFor={`${key}-grouping`} className="text-sm text-gray-700">
                        Gruppierung
                      </label>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <input 
                        type="checkbox" 
                        id={`${key}-sound`} 
                        className="rounded border-gray-300"
                        checked={settings.sound}
                        onChange={() => {}}
                      />
                      <label htmlFor={`${key}-sound`} className="text-sm text-gray-700">
                        Sound
                      </label>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <input 
                        type="text" 
                        className="w-full p-1 border rounded-md text-sm" 
                        defaultValue={settings.icon}
                        placeholder="/path/to/icon.png"
                      />
                      <Button variant="outline" size="sm" className="h-7 w-7 p-0">
                        <Upload className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              
              <Button variant="outline" className="w-full flex items-center justify-center gap-1">
                <Plus className="h-4 w-4" />
                Benachrichtigungstyp hinzufügen
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Test-Benachrichtigung</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Senden Sie eine Test-Benachrichtigung, um die Konfiguration zu überprüfen:
            </p>
            
            <div className="flex gap-2">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white flex-1 flex items-center justify-center gap-1">
                <Bell className="h-4 w-4" />
                Test-Benachrichtigung senden
              </Button>
              <Button variant="outline" className="flex-1 flex items-center justify-center gap-1">
                <CheckSquare className="h-4 w-4" />
                Berechtigungen prüfen
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Progressive Web App Einstellungen</h1>
        <Button
          className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1"
        >
          <Save className="h-4 w-4" />
          Änderungen speichern
        </Button>
      </div>
      
      {/* Main Navigation Tabs */}
      <div className="border-b mb-6">
        <div className="flex gap-6">
          <button 
            className={`pb-2 px-1 ${activeTab === 'general' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveTab('general')}
          >
            Allgemein
          </button>
          <button 
            className={`pb-2 px-1 ${activeTab === 'offline' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveTab('offline')}
          >
            Offline-Modus
          </button>
          <button 
            className={`pb-2 px-1 ${activeTab === 'sync' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveTab('sync')}
          >
            Synchronisation
          </button>
          <button 
            className={`pb-2 px-1 ${activeTab === 'notifications' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveTab('notifications')}
          >
            Benachrichtigungen
          </button>
        </div>
      </div>
      
      {/* Loading State */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        // Active Tab Content
        <>
          {activeTab === 'general' && renderGeneralSettings()}
          {activeTab === 'offline' && renderOfflineSettings()}
          {activeTab === 'sync' && renderSyncSettings()}
          {activeTab === 'notifications' && renderNotificationSettings()}
        </>
      )}
      
      {/* Bottom Action Buttons */}
      <div className="flex justify-between mt-6">
        <Button variant="outline" className="flex items-center gap-1">
          <Code className="h-4 w-4" />
          Web App Manifest anzeigen
        </Button>
        
        <div className="flex gap-2">
          <Button variant="outline">
            Abbrechen
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1">
            <Save className="h-4 w-4" />
            Änderungen speichern
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PWASettings;