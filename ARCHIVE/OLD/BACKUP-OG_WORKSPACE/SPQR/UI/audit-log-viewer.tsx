import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  FileText, Search, Filter, Download, Scroll, X, AlertCircle, 
  User, Database, Settings, Edit, Trash, RefreshCw, Plus, 
  Lock, Eye, ChevronDown, ChevronUp, Calendar, Clock
} from 'lucide-react';

const AuditLogViewer = () => {
  const [loading, setLoading] = useState(true);
  const [logs, setLogs] = useState([]);
  const [expandedLog, setExpandedLog] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    action: 'all',
    status: 'all',
    user: 'all',
    entity: 'all',
    dateRange: 'today'
  });
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [users, setUsers] = useState([]);
  
  useEffect(() => {
    // Mock data loading
    setTimeout(() => {
      setLogs([
        {
          id: 1,
          timestamp: '2025-03-09T10:15:22',
          user: {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'administrator'
          },
          action: 'create',
          entity: 'customer',
          entityId: 'cus_12345',
          entityName: 'Acme Corp',
          status: 'success',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          details: {
            changes: {
              name: {
                from: null,
                to: 'Acme Corp'
              },
              email: {
                from: null,
                to: '<EMAIL>'
              },
              phone: {
                from: null,
                to: '+****************'
              }
            }
          }
        },
        {
          id: 2,
          timestamp: '2025-03-09T10:08:45',
          user: {
            id: 2,
            name: 'Sales Manager',
            email: '<EMAIL>',
            role: 'manager'
          },
          action: 'update',
          entity: 'deal',
          entityId: 'deal_789',
          entityName: 'Enterprise License Deal',
          status: 'success',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          details: {
            changes: {
              status: {
                from: 'negotiation',
                to: 'closed_won'
              },
              value: {
                from: 10000,
                to: 12500
              }
            }
          }
        },
        {
          id: 3,
          timestamp: '2025-03-09T09:55:12',
          user: {
            id: 3,
            name: 'Support Agent',
            email: '<EMAIL>',
            role: 'agent'
          },
          action: 'read',
          entity: 'customer',
          entityId: 'cus_12345',
          entityName: 'Acme Corp',
          status: 'success',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
          details: {
            accessed: ['contact_info', 'deal_history', 'support_tickets']
          }
        },
        {
          id: 4,
          timestamp: '2025-03-09T09:42:18',
          user: {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'administrator'
          },
          action: 'delete',
          entity: 'document',
          entityId: 'doc_456',
          entityName: 'Contract Draft v1.docx',
          status: 'success',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          details: {
            reason: 'Outdated version'
          }
        },
        {
          id: 5,
          timestamp: '2025-03-09T09:30:54',
          user: {
            id: 2,
            name: 'Sales Manager',
            email: '<EMAIL>',
            role: 'manager'
          },
          action: 'update',
          entity: 'customer',
          entityId: 'cus_54321',
          entityName: 'Globex Corporation',
          status: 'failed',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          details: {
            error: 'Validation failed: Email address is not valid',
            changes: {
              email: {
                from: '<EMAIL>',
                to: 'invalid-email'
              }
            }
          }
        },
        {
          id: 6,
          timestamp: '2025-03-09T09:15:02',
          user: {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'administrator'
          },
          action: 'permission_change',
          entity: 'user',
          entityId: 'usr_789',
          entityName: 'John Smith',
          status: 'success',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          details: {
            changes: {
              role: {
                from: 'agent',
                to: 'manager'
              },
              permissions: {
                added: ['edit_deals', 'approve_discounts'],
                removed: []
              }
            }
          }
        },
        {
          id: 7,
          timestamp: '2025-03-09T09:02:35',
          user: {
            id: 4,
            name: 'System',
            email: '<EMAIL>',
            role: 'system'
          },
          action: 'backup',
          entity: 'system',
          entityId: 'backup_123',
          entityName: 'Daily Backup',
          status: 'success',
          ipAddress: 'internal',
          userAgent: 'CronJob/1.0',
          details: {
            backupSize: '1.2GB',
            backupLocation: 'gs://ai-crm-backups/daily/2025-03-09/',
            duration: '145s'
          }
        }
      ]);
      
      setUsers([
        { id: 1, name: 'Admin User', email: '<EMAIL>', role: 'administrator' },
        { id: 2, name: 'Sales Manager', email: '<EMAIL>', role: 'manager' },
        { id: 3, name: 'Support Agent', email: '<EMAIL>', role: 'agent' },
        { id: 4, name: 'System', email: '<EMAIL>', role: 'system' }
      ]);
      
      setLoading(false);
    }, 1500);
  }, []);
  
  const getActionIcon = (action) => {
    switch (action) {
      case 'create':
        return <Plus className="h-5 w-5 text-green-500" />;
      case 'update':
        return <Edit className="h-5 w-5 text-blue-500" />;
      case 'delete':
        return <Trash className="h-5 w-5 text-red-500" />;
      case 'read':
        return <Eye className="h-5 w-5 text-gray-500" />;
      case 'permission_change':
        return <Lock className="h-5 w-5 text-purple-500" />;
      case 'backup':
        return <Database className="h-5 w-5 text-yellow-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };
  
  const getEntityIcon = (entity) => {
    switch (entity) {
      case 'customer':
        return <User className="h-5 w-5 text-blue-500" />;
      case 'deal':
        return <FileText className="h-5 w-5 text-green-500" />;
      case 'document':
        return <FileText className="h-5 w-5 text-yellow-500" />;
      case 'user':
        return <User className="h-5 w-5 text-purple-500" />;
      case 'system':
        return <Settings className="h-5 w-5 text-gray-500" />;
      default:
        return <Database className="h-5 w-5 text-gray-500" />;
    }
  };
  
  const getStatusBadge = (status) => {
    switch (status) {
      case 'success':
        return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Erfolgreich</span>;
      case 'failed':
        return <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Fehlgeschlagen</span>;
      case 'warning':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Warnung</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">{status}</span>;
    }
  };