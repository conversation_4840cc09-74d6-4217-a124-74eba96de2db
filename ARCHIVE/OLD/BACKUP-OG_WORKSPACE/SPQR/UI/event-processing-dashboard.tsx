  // Filter events based on search and filters
  const filteredEvents = events.filter(event => {
    // Filter by search query
    if (searchQuery && 
        !event.id.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !event.type.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Filter by status
    if (filters.status !== 'all' && event.status !== filters.status) {
      return false;
    }
    
    // Filter by event type
    if (filters.type !== 'all' && !event.type.startsWith(filters.type)) {
      return false;
    }
    
    // Filter by timeframe
    if (filters.timeframe === 'today') {
      const today = new Date().toISOString().split('T')[0];
      const eventDate = new Date(event.created).toISOString().split('T')[0];
      if (eventDate !== today) {
        return false;
      }
    }
    
    return true;
  });
  
  const renderOverviewTab = () => {
    return (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Heutige Events</p>
                  <h3 className="text-2xl font-bold">{dashboardMetrics.todayEvents.toLocaleString()}</h3>
                </div>
                <Activity className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Erfolgsrate</p>
                  <h3 className="text-2xl font-bold">{dashboardMetrics.successRate}%</h3>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Queue-Größe</p>
                  <h3 className="text-2xl font-bold">{dashboardMetrics.queueSizeTotal}</h3>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Verarbeitungszeit</p>
                  <h3 className="text-2xl font-bold">{formatDuration(dashboardMetrics.avgProcessingTime)}</h3>
                </div>
                <RefreshCw className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Stündliche Ereignisrate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={dashboardMetrics.hourlyEventRate}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#3b82f6" name="Events" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Event-Typen</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      layout="vertical"
                      data={dashboardMetrics.eventTypeBreakdown}
                      margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
                    >
                      <XAxis type="number" />
                      <YAxis dataKey="type" type="category" />
                      <Tooltip />
                      <Bar dataKey="count" fill="#3b82f6" name="Anzahl" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Event-Queues</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {eventQueues.map(queue => (
                <div key={queue.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(queue.status)}
                      <h3 className="font-medium">{queue.name}</h3>
                    </div>
                    <div className="flex items-center gap-2">
                      {queue.status === 'active' ? (
                        <Button variant="outline" size="sm" className="text-yellow-600">
                          <PauseCircle className="h-4 w-4 mr-1" /> Pausieren
                        </Button>
                      ) : (
                        <Button variant="outline" size="sm" className="text-green-600">
                          <PlayCircle className="h-4 w-4 mr-1" /> Fortsetzen
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Sliders className="h-4 w-4 mr-1" /> Konfigurieren
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-2">
                    <div>
                      <p className="text-xs text-gray-500">Queue-Größe</p>
                      <p className="font-medium">{queue.size}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Verarbeitungsrate</p>
                      <p className="font-medium">{queue.processingRate}/min</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Ältestes Event</p>
                      <p className="font-medium">{new Date(queue.oldestEvent).toLocaleTimeString()}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Fehlerrate</p>
                      <p className="font-medium">{queue.errorRate}%</p>
                    </div>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        queue.errorRate > 5 ? 'bg-red-500' : 
                        queue.errorRate > 2 ? 'bg-yellow-500' : 
                        'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(100, 100 - queue.errorRate)}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </>
    );
  };
  
  const renderEventsTab = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Events</h2>
          <div className="flex items-center gap-2">
            <Button variant="outline" className="flex items-center gap-1">
              <Filter className="h-4 w-4" />
              Filter
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1">
              <PlusCircle className="h-4 w-4" />
              Event erstellen
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="md:col-span-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Events durchsuchen..."
                className="w-full px-10 py-2 border rounded-md"
                value={searchQuery}
                onChange={handleSearchChange}
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              {searchQuery && (
                <button 
                  className="absolute right-3 top-2.5"
                  onClick={() => setSearchQuery('')}
                >
                  <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>
          </div>
          
          <div>
            <select 
              className="w-full p-2 border rounded-md"
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="all">Alle Status</option>
              <option value="completed">Abgeschlossen</option>
              <option value="processing">Wird verarbeitet</option>
              <option value="failed">Fehlgeschlagen</option>
              <option value="pending">Ausstehend</option>
            </select>
          </div>
        </div>
        
        <div className="mb-6">
          <div className="flex gap-2 flex-wrap">
            {/* Event type filter pills */}
            <div className="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center">
              <span className="mr-1 text-gray-600">Typ:</span>
              <select 
                className="bg-transparent border-none text-blue-600 font-medium focus:outline-none cursor-pointer"
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
              >
                <option value="all">Alle Typen</option>
                <option value="customer.">Customer</option>
                <option value="deal.">Deal</option>
                <option value="document.">Document</option>
                <option value="email.">Email</option>
                <option value="payment.">Payment</option>
              </select>
            </div>
            
            {/* Timeframe filter pills */}
            <div className="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center">
              <span className="mr-1 text-gray-600">Zeitraum:</span>
              <select 
                className="bg-transparent border-none text-blue-600 font-medium focus:outline-none cursor-pointer"
                value={filters.timeframe}
                onChange={(e) => handleFilterChange('timeframe', e.target.value)}
              >
                <option value="today">Heute</option>
                <option value="week">Diese Woche</option>
                <option value="month">Dieser Monat</option>
                <option value="all">Alle</option>
              </select>
            </div>
          </div>
        </div>
        
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 text-sm text-gray-500">
                  <tr>
                    <th className="py-3 px-4 text-left">ID</th>
                    <th className="py-3 px-4 text-left">Typ</th>
                    <th className="py-3 px-4 text-left">Status</th>
                    <th className="py-3 px-4 text-left">Erstellt</th>
                    <th className="py-3 px-4 text-left">Dauer</th>
                    <th className="py-3 px-4 text-left">Workflow</th>
                    <th className="py-3 px-4 text-left">Queue</th>
                    <th className="py-3 px-4 text-left">Aktionen</th>
                  </tr>
                </thead>
                <tbody className="divide-y">
                  {loading ? (
                    <tr>
                      <td colSpan={8} className="py-10 text-center">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                        </div>
                      </td>
                    </tr>
                  ) : filteredEvents.length === 0 ? (
                    <tr>
                      <td colSpan={8} className="py-8 text-center text-gray-500">
                        Keine Events gefunden. Passen Sie Ihre Suchkriterien an.
                      </td>
                    </tr>
                  ) : (
                    filteredEvents.map(event => (
                      <tr key={event.id} className="hover:bg-gray-50">
                        <td className="py-3 px-4 font-medium">{event.id}</td>
                        <td className="py-3 px-4">
                          <span className="font-mono text-sm">{event.type}</span>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-1">
                            {getStatusIcon(event.status)}
                            <span>{getStatusText(event.status)}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-sm">
                          {new Date(event.created).toLocaleTimeString()}
                        </td>
                        <td className="py-3 px-4">
                          {event.duration ? formatDuration(event.duration) : '-'}
                        </td>
                        <td className="py-3 px-4 text-sm">
                          {event.workflowName || '-'}
                        </td>
                        <td className="py-3 px-4 text-sm">
                          {event.queue}
                        </td>
                        <td className="py-3 px-4">
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="text-xs"
                            onClick={() => handleViewEventDetail(event)}
                          >
                            Details
                          </Button>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
        
        {isEventDetailOpen && selectedEvent && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
              <div className="sticky top-0 bg-white border-b p-4 flex justify-between items-center">
                <h3 className="text-lg font-bold">Event Details: {selectedEvent.id}</h3>
                <Button variant="ghost" size="sm" onClick={handleCloseEventDetail}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h4 className="font-medium mb-3">Event Informationen</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Typ:</span>
                        <span className="font-mono">{selectedEvent.type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Status:</span>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(selectedEvent.status)}
                          <span>{getStatusText(selectedEvent.status)}</span>
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Erstellt:</span>
                        <span>{new Date(selectedEvent.created).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Abgeschlossen:</span>
                        <span>{selectedEvent.completed ? new Date(selectedEvent.completed).toLocaleString() : '-'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Dauer:</span>
                        <span>{selectedEvent.duration ? formatDuration(selectedEvent.duration) : '-'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Workflow:</span>
                        <span>{selectedEvent.workflowName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Queue:</span>
                        <span>{selectedEvent.queue}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Wiederholungen:</span>
                        <span>{selectedEvent.retries}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-3">Payload</h4>
                    <div className="bg-gray-100 p-3 rounded-md overflow-auto max-h-60">
                      <pre className="text-xs">{JSON.stringify(selectedEvent.payload, null, 2)}</pre>
                    </div>
                    
                    {selectedEvent.result && (
                      <>
                        <h4 className="font-medium mb-2 mt-4">Ergebnis</h4>
                        <div className="bg-gray-100 p-3 rounded-md overflow-auto max-h-40">
                          <pre className="text-xs">{JSON.stringify(selectedEvent.result, null, 2)}</pre>
                        </div>
                      </>
                    )}
                    
                    {selectedEvent.error && (
                      <>
                        <h4 className="font-medium mb-2 mt-4 text-red-600">Fehler</h4>
                        <div className="bg-red-50 p-3 rounded-md overflow-auto max-h-40 border border-red-200">
                          <pre className="text-xs text-red-600">{selectedEvent.error}</pre>
                        </div>
                      </>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-end mt-6 gap-2">
                  {selectedEvent.status === 'failed' && (
                    <Button variant="outline" className="flex items-center gap-1">
                      <RotateCcw className="h-4 w-4" />
                      Erneut versuchen
                    </Button>
                  )}
                  
                  <Button className="bg-blue-600 text-white flex items-center gap-1">
                    <ArrowRight className="h-4 w-4" />
                    Zum Workflow
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </>
    );
  };
  
  const renderQueuesTab = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Event Queues</h2>
          <div className="flex items-center gap-2">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1">
              <PlusCircle className="h-4 w-4" />
              Neue Queue erstellen
            </Button>
          </div>
        </div>
        
        <div className="space-y-6">
          {eventQueues.map(queue => (
            <Card key={queue.id}>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center gap-2">
                    {getStatusIcon(queue.status)}
                    {queue.name}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {queue.status === 'active' ? (
                      <Button variant="outline" className="text-yellow-600">
                        <PauseCircle className="h-4 w-4 mr-1" /> Pausieren
                      </Button>
                    ) : (
                      <Button variant="outline" className="text-green-600">
                        <PlayCircle className="h-4 w-4 mr-1" /> Fortsetzen
                      </Button>
                    )}
                    <Button variant="outline">
                      <Sliders className="h-4 w-4 mr-1" /> Konfigurieren
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-gray-600">Warteschlangengröße</span>
                      <span className="font-medium">{queue.size}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          queue.size > 100 ? 'bg-red-500' : 
                          queue.size > 50 ? 'bg-yellow-500' : 
                          'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(100, queue.size / 2)}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-gray-600">Verarbeitungsrate</span>
                      <span className="font-medium">{queue.processingRate}/min</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="h-2 rounded-full bg-blue-500"
                        style={{ width: `${Math.min(100, queue.processingRate)}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-gray-600">Fehlerrate</span>
                      <span className="font-medium">{queue.errorRate}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          queue.errorRate > 5 ? 'bg-red-500' : 
                          queue.errorRate > 2 ? 'bg-yellow-500' : 
                          'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(100, queue.errorRate * 10)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-sm mb-3">Allgemeine Informationen</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Ältestes Event:</span>
                        <span>{new Date(queue.oldestEvent).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Wiederholungsversuche:</span>
                        <span>{queue.retryCount}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Status:</span>
                        <span className="flex items-center gap-1">
                          {getStatusIcon(queue.status)} {getStatusText(queue.status)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-sm mb-3">Leistungsmetriken</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Durchschn. Latenz:</span>
                        <span>{formatDuration(queue.processingMetrics.averageLatency)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">P95 Latenz:</span>
                        <span>{formatDuration(queue.processingMetrics.p95Latency)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">P99 Latenz:</span>
                        <span>{formatDuration(queue.processingMetrics.p99Latency)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Durchsatz:</span>
                        <span>{queue.processingMetrics.throughput} Events/min</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2 mt-6">
                  <Button variant="outline" className="flex items-center gap-1">
                    <SkipForward className="h-4 w-4" />
                    Alle verarbeiten
                  </Button>
                  <Button variant="outline" className="flex items-center gap-1 text-red-600">
                    <X className="h-4 w-4" />
                    Queue leeren
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    );
  };
  
  const renderWebhooksTab = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Webhooks</h2>
          <div className="flex items-center gap-2">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1">
              <PlusCircle className="h-4 w-4" />
              Neuen Webhook erstellen
            </Button>
          </div>
        </div>
        
        <div className="space-y-6">
          {webhooks.map(webhook => (
            <Card key={webhook.id}>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center gap-2">
                    <Webhook className="h-5 w-5 text-blue-500" />
                    {webhook.name}
                  </CardTitle>
                  <div className="flex items-center gap-1">
                    {webhook.status === 'active' ? (
                      <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                        Aktiv
                      </span>
                    ) : (
                      <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                        Inaktiv
                      </span>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div>
                  <h3 className="font-medium text-sm mb-2">Webhook Endpunkt</h3>
                  <div className="flex items-center">
                    <div className="bg-gray-100 text-gray-600 px-3 py-2 rounded-l-md border border-r-0 font-mono text-sm">
                      https://your-domain.com
                    </div>
                    <div className="bg-white px-3 py-2 rounded-r-md border text-gray-900 font-mono text-sm flex-1">
                      {webhook.endpoint}
                    </div>
                    <Button variant="ghost" size="sm" className="ml-2">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                  <div>
                    <h3 className="font-medium text-sm mb-3">Event Statistiken</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Gesamt Events:</span>
                        <span>{webhook.eventCount.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Zuletzt ausgelöst:</span>
                        <span>{new Date(webhook.lastTriggered).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Erstellt am:</span>
                        <span>{new Date(webhook.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-sm mb-3">Sicherheit</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Secret Key:</span>
                        <div className="flex items-center gap-1">
                          <span className="font-mono">{webhook.secretKey}</span>
                          <Button variant="ghost" size="sm" className="h-5 w-5 p-0">
                            <Eye className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Signatur-Header:</span>
                        <span className="font-mono">X-Webhook-Signature</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-sm mb-3">Aktionen</h3>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full flex items-center justify-center gap-1 text-xs">
                        <Lock className="h-3.5 w-3.5" />
                        Secret Key neu generieren
                      </Button>
                      <Button variant="outline" className="w-full flex items-center justify-center gap-1 text-xs">
                        <FileJson className="h-3.5 w-3.5" />
                        Test-Payload senden
                      </Button>
                      {webhook.status === 'active' ? (
                        <Button variant="outline" className="w-full flex items-center justify-center gap-1 text-xs text-yellow-600">
                          <PauseCircle className="h-3.5 w-3.5" />
                          Deaktivieren
                        </Button>
                      ) : (
                        <Button variant="outline" className="w-full flex items-center justify-center gap-1 text-xs text-green-600">
                          <PlayCircle className="h-3.5 w-3.5" />
                          Aktivieren
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    );
  };
  
  const renderWorkflowsTab = () => {
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Workflows</h2>
          <div className="flex items-center gap-2">
            <Button variant="outline" className="flex items-center gap-1">
              <RefreshCw className="h-4 w-4" />
              Von n8n synchronisieren
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1">
              <PlusCircle className="h-4 w-4" />
              Zum n8n Editor
            </Button>
          </div>
        </div>
        
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Workflows durchsuchen..."
              className="w-full px-10 py-2 border rounded-md"
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
        </div>
        
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 text-sm text-gray-500">
                  <tr>
                    <th className="py-3 px-4 text-left">Workflow</th>
                    <th className="py-3 px-4 text-left">Event-Typen</th>
                    <th className="py-3 px-4 text-left">Status</th>
                    <th className="py-3 px-4 text-right">Ausführungen</th>
                    <th className="py-3 px-4 text-right">Erfolgsrate</th>
                    <th className="py-3 px-4 text-right">Avg. Zeit</th>
                    <th className="py-3 px-4 text-right">Aktionen</th>
                  </tr>
                </thead>
                <tbody className="divide-y">
                  {loading ? (
                    <tr>
                      <td colSpan={7} className="py-10 text-center">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    workflows.map(workflow => (
                      <tr key={workflow.id} className="hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="font-medium">{workflow.name}</div>
                          <div className="text-xs text-gray-500">{workflow.id}</div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex flex-wrap gap-1">
                            {workflow.eventTypes.map(type => (
                              <span key={type} className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                {type}
                              </span>
                            ))}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-1">
                            {getStatusIcon(workflow.status)}
                            <span>{getStatusText(workflow.status)}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-right">
                          {workflow.executionCount.toLocaleString()}
                        </td>
                        <td className="py-3 px-4 text-right">
                          <span className={workflow.successRate >= 95 ? 'text-green-600' : workflow.successRate >= 90 ? 'text-yellow-600' : 'text-red-600'}>
                            {workflow.successRate}%
                          </span>
                        </td>
                        <td className="py-3 px-4 text-right">
                          {formatDuration(workflow.avgExecutionTime)}
                        </td>
                        <td className="py-3 px-4 text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" size="sm" className="text-xs">
                              <ArrowRight className="h-3.5 w-3.5 mr-1" /> n8n
                            </Button>
                            <Button variant="outline" size="sm" className="text-xs">
                              <Activity className="h-3.5 w-3.5 mr-1" /> Log
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </>
    );
  };
  
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Event Processing</h1>
      </div>
      
      {/* Main Navigation Tabs */}
      <div className="border-b mb-6">
        <div className="flex gap-6">
          <button 
            className={`pb-2 px-1 ${activeTab === 'overview' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveTab('overview')}
          >
            Übersicht
          </button>
          <button 
            className={`pb-2 px-1 ${activeTab === 'events' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveTab('events')}
          >
            Events
          </button>
          <button 
            className={`pb-2 px-1 ${activeTab === 'queues' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveTab('queues')}
          >
            Queues
          </button>
          <button 
            className={`pb-2 px-1 ${activeTab === 'webhooks' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveTab('webhooks')}
          >
            Webhooks
          </button>
          <button 
            className={`pb-2 px-1 ${activeTab === 'workflows' ? 'border-b-2 border-blue-600 text-blue-600 font-medium' : 'text-gray-500'}`}
            onClick={() => setActiveTab('workflows')}
          >
            Workflows
          </button>
        </div>
      </div>
      
      {/* Active Tab Content */}
      {loading && !dashboardMetrics.totalEvents ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : activeTab === 'overview' ? (
        renderOverviewTab()
      ) : activeTab === 'events' ? (
        renderEventsTab()
      ) : activeTab === 'queues' ? (
        renderQueuesTab()
      ) : activeTab === 'webhooks' ? (
        renderWebhooksTab()
      ) : (
        renderWorkflowsTab()
      )}
    </div>
  );
};

export default EventProcessingDashboard;import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LineChart, Line, BarChart, Bar, ResponsiveContainer, XAxis, YAxis, Tooltip, Legend } from 'recharts';
import { 
  PlayCircle, PauseCircle, RotateCcw, AlertCircle, CheckCircle, 
  Activity, Clock, XCircle, Filter, Search, ArrowRight, Sliders,
  Lock, SkipForward, FileJson, Share2, Webhook, 
  RefreshCw, PlusCircle, X, ChevronDown, ChevronUp
} from 'lucide-react';

const EventProcessingDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview'); // overview, events, queues, webhooks, workflows
  const [eventQueues, setEventQueues] = useState([]);
  const [events, setEvents] = useState([]);
  const [webhooks, setWebhooks] = useState([]);
  const [workflows, setWorkflows] = useState([]);
  const [dashboardMetrics, setDashboardMetrics] = useState({});
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [isEventDetailOpen, setIsEventDetailOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    timeframe: 'today'
  });
  
  useEffect(() => {
    // Mock data loading
    setTimeout(() => {
      setEventQueues([
        {
          id: 1,
          name: 'Standard-Queue',
          status: 'active',
          size: 12,
          processingRate: 42,
          oldestEvent: '2025-03-09T10:05:23',
          errorRate: 3.2,
          retryCount: 5,
          processingMetrics: {
            averageLatency: 320, // ms
            p95Latency: 850, // ms
            p99Latency: 1250, // ms
            throughput: 40 // events per minute
          }
        },
        {
          id: 2,
          name: 'High-Priority',
          status: 'active',
          size: 3,
          processingRate: 58,
          oldestEvent: '2025-03-09T10:14:47',
          errorRate: 1.5,
          retryCount: 2,
          processingMetrics: {
            averageLatency: 180, // ms
            p95Latency: 450, // ms
            p99Latency: 720, // ms
            throughput: 55 // events per minute
          }
        },
        {
          id: 3,
          name: 'Bulk-Processing',
          status: 'paused',
          size: 248,
          processingRate: 0,
          oldestEvent: '2025-03-09T08:32:15',
          errorRate: 4.7,
          retryCount: 17,
          processingMetrics: {
            averageLatency: 680, // ms
            p95Latency: 1250, // ms
            p99Latency: 1850, // ms
            throughput: 28 // events per minute
          }
        }
      ]);
      
      setEvents([
        {
          id: 'evt_9876543',
          type: 'customer.created',
          status: 'completed',
          created: '2025-03-09T10:12:32',
          completed: '2025-03-09T10:12:34',
          duration: 2150, // ms
          payload: {
            customerId: 'cus_12345',
            name: 'Acme Corp',
            email: '<EMAIL>',
            industry: 'Manufacturing'
          },
          result: {
            success: true,
            updates: ['CRM record created', 'Welcome email scheduled']
          },
          workflowId: 'wf_123',
          workflowName: 'New Customer Onboarding',
          queue: 'High-Priority',
          retries: 0,
          error: null
        },
        {
          id: 'evt_9876542',
          type: 'deal.stage_changed',
          status: 'completed',
          created: '2025-03-09T10:08:15',
          completed: '2025-03-09T10:08:16',
          duration: 1320, // ms
          payload: {
            dealId: 'deal_456',
            newStage: 'negotiation',
            previousStage: 'proposal',
            value: 25000
          },
          result: {
            success: true,
            updates: ['Stage updated', 'Sales manager notified']
          },
          workflowId: 'wf_124',
          workflowName: 'Deal Stage Progression',
          queue: 'Standard-Queue',
          retries: 0,
          error: null
        },
        {
          id: 'evt_9876541',
          type: 'document.uploaded',
          status: 'processing',
          created: '2025-03-09T10:14:55',
          completed: null,
          duration: null,
          payload: {
            documentId: 'doc_789',
            filename: 'Contract_Draft_v2.pdf',
            uploadedBy: 'usr_456',
            size: 2456789
          },
          result: null,
          workflowId: 'wf_125',
          workflowName: 'Document Processing',
          queue: 'Bulk-Processing',
          retries: 0,
          error: null
        },
        {
          id: 'evt_9876540',
          type: 'email.bounced',
          status: 'failed',
          created: '2025-03-09T09:58:23',
          completed: '2025-03-09T09:58:25',
          duration: 2450, // ms
          payload: {
            emailId: 'email_123',
            recipient: '<EMAIL>',
            subject: 'Your Monthly Invoice',
            bounceType: 'hard'
          },
          result: null,
          workflowId: 'wf_126',
          workflowName: 'Email Bounce Handler',
          queue: 'Standard-Queue',
          retries: 3,
          error: 'Failed to update contact record: Database timeout'
        },
        {
          id: 'evt_9876539',
          type: 'payment.succeeded',
          status: 'completed',
          created: '2025-03-09T09:45:12',
          completed: '2025-03-09T09:45:14',
          duration: 1850, // ms
          payload: {
            paymentId: 'pay_789',
            amount: 1299.99,
            currency: 'EUR',
            customerId: 'cus_456'
          },
          result: {
            success: true,
            updates: ['Invoice marked as paid', 'Receipt sent']
          },
          workflowId: 'wf_127',
          workflowName: 'Payment Processing',
          queue: 'High-Priority',
          retries: 0,
          error: null
        }
      ]);
      
      setWebhooks([
        {
          id: 1,
          name: 'Stripe Payments',
          endpoint: '/api/webhooks/stripe',
          status: 'active',
          createdAt: '2025-01-15T09:23:45',
          lastTriggered: '2025-03-09T09:45:10',
          eventCount: 843,
          secretKey: 'whsec_***'
        },
        {
          id: 2,
          name: 'GitHub Repository',
          endpoint: '/api/webhooks/github',
          status: 'active',
          createdAt: '2025-01-22T14:36:12',
          lastTriggered: '2025-03-08T16:22:35',
          eventCount: 127,
          secretKey: 'gh_***'
        },
        {
          id: 3,
          name: 'Helpdesk Tickets',
          endpoint: '/api/webhooks/helpdesk',
          status: 'inactive',
          createdAt: '2025-02-18T11:15:30',
          lastTriggered: '2025-03-01T10:12:44',
          eventCount: 65,
          secretKey: 'hd_***'
        }
      ]);
      
      setWorkflows([
        {
          id: 'wf_123',
          name: 'New Customer Onboarding',
          status: 'active',
          eventTypes: ['customer.created'],
          executionCount: 324,
          successRate: 98.2,
          avgExecutionTime: 2300, // ms
          lastExecuted: '2025-03-09T10:12:34',
          n8nId: 123456
        },
        {
          id: 'wf_124',
          name: 'Deal Stage Progression',
          status: 'active',
          eventTypes: ['deal.stage_changed'],
          executionCount: 587,
          successRate: 99.5,
          avgExecutionTime: 1280, // ms
          lastExecuted: '2025-03-09T10:08:16',
          n8nId: 123457
        },
        {
          id: 'wf_125',
          name: 'Document Processing',
          status: 'active',
          eventTypes: ['document.uploaded'],
          executionCount: 892,
          successRate: 94.8,
          avgExecutionTime: 12500, // ms
          lastExecuted: '2025-03-09T09:45:22',
          n8nId: 123458
        },
        {
          id: 'wf_126',
          name: 'Email Bounce Handler',
          status: 'active',
          eventTypes: ['email.bounced'],
          executionCount: 156,
          successRate: 91.2,
          avgExecutionTime: 2100, // ms
          lastExecuted: '2025-03-09T09:58:25',
          n8nId: 123459
        },
        {
          id: 'wf_127',
          name: 'Payment Processing',
          status: 'active',
          eventTypes: ['payment.succeeded', 'payment.failed'],
          executionCount: 1243,
          successRate: 97.8,
          avgExecutionTime: 1750, // ms
          lastExecuted: '2025-03-09T09:45:14',
          n8nId: 123460
        }
      ]);
      
      setDashboardMetrics({
        totalEvents: 45682,
        todayEvents: 1352,
        successRate: 96.8,
        avgProcessingTime: 1850, // ms
        queueSizeTotal: 263,
        activeQueues: 2,
        errorRate: 3.2,
        hourlyEventRate: [
          { hour: '00:00', count: 12 },
          { hour: '01:00', count: 8 },
          { hour: '02:00', count: 5 },
          { hour: '03:00', count: 3 },
          { hour: '04:00', count: 2 },
          { hour: '05:00', count: 4 },
          { hour: '06:00', count: 15 },
          { hour: '07:00', count: 42 },
          { hour: '08:00', count: 78 },
          { hour: '09:00', count: 120 },
          { hour: '10:00', count: 145 },
          { hour: '11:00', count: 0 }, // Future hours
          { hour: '12:00', count: 0 },
          { hour: '13:00', count: 0 },
          { hour: '14:00', count: 0 },
          { hour: '15:00', count: 0 },
          { hour: '16:00', count: 0 },
          { hour: '17:00', count: 0 },
          { hour: '18:00', count: 0 },
          { hour: '19:00', count: 0 },
          { hour: '20:00', count: 0 },
          { hour: '21:00', count: 0 },
          { hour: '22:00', count: 0 },
          { hour: '23:00', count: 0 }
        ],
        eventTypeBreakdown: [
          { type: 'customer.*', count: 843 },
          { type: 'deal.*', count: 1256 },
          { type: 'document.*', count: 892 },
          { type: 'email.*', count: 578 },
          { type: 'payment.*', count: 1243 },
          { type: 'other', count: 456 }
        ]
      });
      
      setLoading(false);
    }, 1500);
  }, []);
  
  const handleViewEventDetail = (event) => {
    setSelectedEvent(event);
    setIsEventDetailOpen(true);
  };
  
  const handleCloseEventDetail = () => {
    setIsEventDetailOpen(false);
    setSelectedEvent(null);
  };
  
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };
  
  const handleFilterChange = (field, value) => {
    setFilters({
      ...filters,
      [field]: value
    });
  };
  
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'paused':
        return <PauseCircle className="h-5 w-5 text-yellow-500" />;
      case 'inactive':
        return <XCircle className="h-5 w-5 text-gray-500" />;
      default:
        return <Activity className="h-5 w-5 text-gray-500" />;
    }
  };
  
  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Abgeschlossen';
      case 'processing':
        return 'Wird verarbeitet';
      case 'failed':
        return 'Fehlgeschlagen';
      case 'pending':
        return 'Ausstehend';
      case 'active':
        return 'Aktiv';
      case 'paused':
        return 'Pausiert';
      case 'inactive':
        return 'Inaktiv';
      default:
        return status;
    }
  };
  
  const formatDuration = (ms) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${Math.floor(ms / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`;
  };
  
  // Filter