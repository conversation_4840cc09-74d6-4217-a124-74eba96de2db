import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Play, Pause, Edit, Trash2, Plus, RefreshCw, 
  Brain, MessageSquare, ArrowRightLeft, Eye, Settings
} from 'lucide-react';

const AgentManager = () => {
  const [agents, setAgents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [view, setView] = useState('list'); // list, details, config

  useEffect(() => {
    // Simulate data loading
    const fetchAgents = async () => {
      setLoading(true);
      
      // This would be replaced with actual API calls
      setTimeout(() => {
        setAgents([
          {
            id: 1,
            name: 'Kundenservice-Agent',
            type: 'support',
            model: '<PERSON>',
            status: 'active',
            createdAt: '2025-02-15',
            lastActive: '2025-03-09T10:24:12',
            description: 'Beantwortet Kundenfragen und hilft bei Support-Anfragen.',
            successRate: 96.5,
            interactions: 3452,
            avgResponseTime: 1.2,
            knowledgeSources: ['FAQ-Datenbank', 'Produkthandbücher', 'Kundenverlauf'],
            tools: ['Email-Versand', 'Ticket-Erstellung', 'Produktsuche']
          },
          {
            id: 2,
            name: 'Recherche-Agent',
            type: 'research',
            model: 'OpenAI',
            status: 'active',
            createdAt: '2025-01-18',
            lastActive: '2025-03-09T09:15:08',
            description: 'Durchsucht Dokumente und extrahiert relevante Informationen.',
            successRate: 92.8,
            interactions: 1876,
            avgResponseTime: 2.7,
            knowledgeSources: ['Unternehmensdokumente', 'Externe APIs', 'Forschungsdatenbanken'],
            tools: ['Dokumentenanalyse', 'Web-Suche', 'Datenextraktion']
          },
          {
            id: 3,
            name: 'Verkaufs-Agent',
            type: 'sales',
            model: 'Claude',
            status: 'inactive',
            createdAt: '2025-02-01',
            lastActive: '2025-03-08T14:35:22',
            description: 'Unterstützt bei Verkaufsgesprächen und generiert Angebote.',
            successRate: 89.2,
            interactions: 1245,
            avgResponseTime: 1.8,
            knowledgeSources: ['Produktkatalog', 'Preisliste', 'Verkaufsskripte'],
            tools: ['Angebotserstellung', 'Kalenderzugriff', 'CRM-Integration']
          },
          {
            id: 4,
            name: 'Terminplanung-Agent',
            type: 'scheduling',
            model: 'Local LLM',
            status: 'active',
            createdAt: '2025-03-01',
            lastActive: '2025-03-09T08:42:56',
            description: 'Organisiert Meetings und verwaltet Termine.',
            successRate: 98.1,
            interactions: 945,
            avgResponseTime: 1.0,
            knowledgeSources: ['Kalender-Daten', 'Besprechungsrichtlinien'],
            tools: ['Kalenderzugriff', 'E-Mail-Benachrichtigung', 'Raumreservierung']
          },
        ]);
        setLoading(false);
      }, 1000);
    };

    fetchAgents();
  }, []);

  const getAgentIcon = (type) => {
    switch(type) {
      case 'support':
        return <MessageSquare className="h-6 w-6 text-blue-500" />;
      case 'research':
        return <Brain className="h-6 w-6 text-purple-500" />;
      case 'sales':
        return <ArrowRightLeft className="h-6 w-6 text-green-500" />;
      case 'scheduling':
        return <RefreshCw className="h-6 w-6 text-orange-500" />;
      default:
        return <Brain className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'bg-green-500' : 'bg-gray-500';
  };

  const handleViewAgent = (agent) => {
    setSelectedAgent(agent);
    setView('details');
  };

  const handleConfigAgent = (agent) => {
    setSelectedAgent(agent);
    setView('config');
  };

  const handleBackToList = () => {
    setSelectedAgent(null);
    setView('list');
  };

  const renderAgentList = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      );
    }

    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">SPQR Agent Manager</h1>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Neuen Agenten erstellen
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {agents.map((agent) => (
            <Card key={agent.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    {getAgentIcon(agent.type)}
                    <CardTitle>{agent.name}</CardTitle>
                  </div>
                  <div className={`rounded-full h-3 w-3 ${getStatusColor(agent.status)}`}></div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">{agent.description}</p>
                
                <div className="flex items-center justify-between mb-4">
                  <div className="text-xs text-gray-500">Erfolgsrate</div>
                  <div className="font-medium">{agent.successRate}%</div>
                </div>
                
                <div className="flex items-center justify-between mb-4">
                  <div className="text-xs text-gray-500">Modell</div>
                  <div className="font-medium">{agent.model}</div>
                </div>
                
                <div className="flex items-center justify-between mb-5">
                  <div className="text-xs text-gray-500">Antwortzeit</div>
                  <div className="font-medium">{agent.avgResponseTime}s</div>
                </div>
                
                <div className="flex flex-wrap gap-2 mt-4">
                  <Button variant="outline" className="text-xs flex items-center gap-1 border-gray-200" onClick={() => handleViewAgent(agent)}>
                    <Eye className="h-3 w-3" />
                    Details
                  </Button>
                  <Button variant="outline" className="text-xs flex items-center gap-1 border-gray-200" onClick={() => handleConfigAgent(agent)}>
                    <Settings className="h-3 w-3" />
                    Konfigurieren
                  </Button>
                  {agent.status === 'active' ? (
                    <Button variant="outline" className="text-xs flex items-center gap-1 border-gray-200 text-yellow-600">
                      <Pause className="h-3 w-3" />
                      Pausieren
                    </Button>
                  ) : (
                    <Button variant="outline" className="text-xs flex items-center gap-1 border-gray-200 text-green-600">
                      <Play className="h-3 w-3" />
                      Aktivieren
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    );
  };

  const renderAgentDetails = () => {
    if (!selectedAgent) return null;
    
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToList}>
            Zurück
          </Button>
          <h1 className="text-2xl font-bold">{selectedAgent.name}</h1>
          <div className={`ml-4 rounded-full h-3 w-3 ${getStatusColor(selectedAgent.status)}`}></div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Agent Übersicht</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium mb-4">Grundinformationen</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Typ:</span>
                        <span>{selectedAgent.type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Modell:</span>
                        <span>{selectedAgent.model}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Status:</span>
                        <span>{selectedAgent.status === 'active' ? 'Aktiv' : 'Inaktiv'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Erstellt am:</span>
                        <span>{selectedAgent.createdAt}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Letzte Aktivität:</span>
                        <span>{new Date(selectedAgent.lastActive).toLocaleTimeString('de-DE')}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-medium mb-4">Leistungsmetriken</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Erfolgsrate:</span>
                        <span>{selectedAgent.successRate}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Interaktionen:</span>
                        <span>{selectedAgent.interactions.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Durchschn. Antwortzeit:</span>
                        <span>{selectedAgent.avgResponseTime}s</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-6">
                  <h3 className="font-medium mb-2">Beschreibung</h3>
                  <p className="text-gray-700">{selectedAgent.description}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Aktivitätsprotokoll</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center text-gray-500 py-8">
                  Aktivitätsprotokolle würden hier angezeigt werden
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div>
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Wissensquellen</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {selectedAgent.knowledgeSources.map((source, idx) => (
                    <li key={idx} className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-blue-500 mr-2"></div>
                      {source}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Verfügbare Tools</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {selectedAgent.tools.map((tool, idx) => (
                    <li key={idx} className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-purple-500 mr-2"></div>
                      {tool}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
            
            <div className="flex gap-3 mt-6">
              <Button className="flex items-center gap-2 flex-1">
                <Edit className="h-4 w-4" />
                Bearbeiten
              </Button>
              <Button variant="outline" className="flex items-center gap-2 flex-1 text-red-600 border-red-200">
                <Trash2 className="h-4 w-4" />
                Löschen
              </Button>
            </div>
          </div>
        </div>
      </>
    );
  };

  const renderAgentConfig = () => {
    if (!selectedAgent) return null;
    
    return (
      <>
        <div className="flex items-center mb-6">
          <Button variant="outline" className="mr-4" onClick={handleBackToList}>
            Zurück
          </Button>
          <h1 className="text-2xl font-bold">{selectedAgent.name} konfigurieren</h1>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Agentkonfiguration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 p-4 rounded-md text-center">
              <p className="text-gray-500">Hier würde ein vollständiges Konfigurationsformular mit den entsprechenden Einstellungen für den Agenten angezeigt werden.</p>
              <p className="text-gray-500 mt-2">Dazu gehören Modellparameter, Werkzeuge, Wissensquellen, Prompt-Templates und mehr.</p>
            </div>
          </CardContent>
        </Card>
      </>
    );
  };

  // Render view based on current state
  const renderView = () => {
    switch(view) {
      case 'details':
        return renderAgentDetails();
      case 'config':
        return renderAgentConfig();
      case 'list':
      default:
        return renderAgentList();
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {renderView()}
    </div>
  );
};

export default AgentManager;
