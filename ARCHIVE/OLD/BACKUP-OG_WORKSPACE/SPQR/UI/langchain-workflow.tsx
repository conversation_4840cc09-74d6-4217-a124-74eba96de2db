import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Plus, Save, Play, Download, Upload, 
  X, Check, ArrowRight, Database, FileText, 
  ExternalLink, Code, MessageSquare, Brain
} from 'lucide-react';

// This would be a real graph library in production
// Here we're just mocking a simple graph for visualization
const GraphNode = ({ node, position, selected, onSelect }) => {
  const getNodeColor = (type) => {
    switch(type) {
      case 'llm':
        return 'bg-blue-100 border-blue-500 text-blue-700';
      case 'tool':
        return 'bg-purple-100 border-purple-500 text-purple-700';
      case 'router':
        return 'bg-yellow-100 border-yellow-500 text-yellow-700';
      case 'input':
        return 'bg-green-100 border-green-500 text-green-700';
      case 'output':
        return 'bg-red-100 border-red-500 text-red-700';
      default:
        return 'bg-gray-100 border-gray-500 text-gray-700';
    }
  };

  const getNodeIcon = (type) => {
    switch(type) {
      case 'llm':
        return <Brain className="h-4 w-4" />;
      case 'tool':
        return <Code className="h-4 w-4" />;
      case 'router':
        return <ArrowRight className="h-4 w-4" />;
      case 'input':
        return <FileText className="h-4 w-4" />;
      case 'output':
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  return (
    <div 
      className={`absolute flex items-center px-3 py-2 rounded-lg border-2 cursor-pointer transition-all ${getNodeColor(node.type)} ${selected ? 'ring-2 ring-offset-2 ring-blue-500' : ''}`}
      style={{ 
        left: position.x, 
        top: position.y,
        minWidth: '120px',
        zIndex: selected ? 10 : 1
      }}
      onClick={() => onSelect(node.id)}
    >
      <div className="mr-2">
        {getNodeIcon(node.type)}
      </div>
      <div className="font-medium text-sm">{node.label}</div>
    </div>
  );
};

const Edge = ({ start, end }) => {
  // Calculate a simple curved line between points
  const midX = (start.x + end.x) / 2;
  const midY = (start.y + end.y) / 2 - 20;
  
  const path = `M ${start.x + 60} ${start.y + 15} Q ${midX} ${midY}, ${end.x} ${end.y + 15}`;
  
  return (
    <svg className="absolute top-0 left-0 w-full h-full pointer-events-none">
      <path 
        d={path}
        stroke="#94a3b8"
        strokeWidth="2"
        fill="none"
        markerEnd="url(#arrowhead)"
      />
      <defs>
        <marker
          id="arrowhead"
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon points="0 0, 10 3.5, 0 7" fill="#94a3b8" />
        </marker>
      </defs>
    </svg>
  );
};

const LangGraphWorkflowDesigner = () => {
  // Sample workflow data
  const [nodes, setNodes] = useState([
    { id: 1, type: 'input', label: 'User Input', x: 50, y: 100 },
    { id: 2, type: 'llm', label: 'Parse Intent', x: 220, y: 100 },
    { id: 3, type: 'router', label: 'Route Request', x: 400, y: 100 },
    { id: 4, type: 'tool', label: 'Search KB', x: 220, y: 220 },
    { id: 5, type: 'llm', label: 'Generate Response', x: 580, y: 100 },
    { id: 6, type: 'output', label: 'Final Answer', x: 750, y: 100 }
  ]);
  
  const [edges, setEdges] = useState([
    { id: 1, source: 1, target: 2 },
    { id: 2, source: 2, target: 3 },
    { id: 3, source: 3, target: 4 },
    { id: 4, source: 4, target: 5 },
    { id: 5, source: 3, target: 5 },
    { id: 6, source: 5, target: 6 }
  ]);
  
  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedEdge, setSelectedEdge] = useState(null);
  const [workflowName, setWorkflowName] = useState('Kundenanfragen-Workflow');
  const [templates, setTemplates] = useState([
    { id: 1, name: 'Einfacher Q&A Agent' },
    { id: 2, name: 'Recherche-Workflow' },
    { id: 3, name: 'Multi-Tool Agent' },
    { id: 4, name: 'Konversationsflow' }
  ]);
  
  const handleNodeSelect = (nodeId) => {
    const node = nodes.find(n => n.id === nodeId);
    setSelectedNode(node);
    setSelectedEdge(null);
  };
  
  const getNodePosition = (nodeId) => {
    const node = nodes.find(n => n.id === nodeId);
    return { x: node.x, y: node.y };
  };
  
  const renderNodeDetails = () => {
    if (!selectedNode) return null;
    
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">{selectedNode.label}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-4">
            <div>
              <label className="block text-gray-700 font-medium mb-1">Node Type</label>
              <select 
                className="w-full p-2 border border-gray-300 rounded-md"
                value={selectedNode.type}
                disabled={selectedNode.type === 'input' || selectedNode.type === 'output'}
              >
                <option value="llm">LLM (Sprachmodell)</option>
                <option value="tool">Tool (Werkzeug)</option>
                <option value="router">Router (Entscheidung)</option>
                <option value="input">Input (Eingabe)</option>
                <option value="output">Output (Ausgabe)</option>
              </select>
            </div>
            
            <div>
              <label className="block text-gray-700 font-medium mb-1">Name</label>
              <input 
                type="text" 
                className="w-full p-2 border border-gray-300 rounded-md"
                value={selectedNode.label}
              />
            </div>
            
            {selectedNode.type === 'llm' && (
              <div>
                <label className="block text-gray-700 font-medium mb-1">LLM Model</label>
                <select className="w-full p-2 border border-gray-300 rounded-md">
                  <option>Claude</option>
                  <option>OpenAI</option>
                  <option>Lokales LLM</option>
                </select>
                
                <div className="mt-4">
                  <label className="block text-gray-700 font-medium mb-1">Prompt Template</label>
                  <textarea 
                    className="w-full p-2 border border-gray-300 rounded-md h-20"
                    placeholder="Geben Sie hier den Prompt ein..."
                  ></textarea>
                </div>
              </div>
            )}
            
            {selectedNode.type === 'tool' && (
              <div>
                <label className="block text-gray-700 font-medium mb-1">Tool-Type</label>
                <select className="w-full p-2 border border-gray-300 rounded-md">
                  <option>Knowledge Base Search</option>
                  <option>Web Search</option>
                  <option>API Call</option>
                  <option>Database Query</option>
                </select>
                
                <div className="mt-4">
                  <label className="block text-gray-700 font-medium mb-1">Parameter</label>
                  <textarea 
                    className="w-full p-2 border border-gray-300 rounded-md h-20"
                    placeholder="Tool-Parameter als JSON..."
                  ></textarea>
                </div>
              </div>
            )}
            
            {selectedNode.type === 'router' && (
              <div>
                <label className="block text-gray-700 font-medium mb-1">Routing Kriterien</label>
                <textarea 
                  className="w-full p-2 border border-gray-300 rounded-md h-20"
                  placeholder="Bedingungen für das Routing..."
                ></textarea>
              </div>
            )}
            
            <div className="flex justify-end space-x-2 pt-2">
              <Button
                variant="outline"
                className="flex items-center gap-1 text-red-600"
              >
                <X className="h-4 w-4" />
                Delete
              </Button>
              <Button
                className="flex items-center gap-1"
              >
                <Check className="h-4 w-4" />
                Apply
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Top bar */}
      <div className="bg-white p-4 border-b flex items-center justify-between">
        <div className="flex items-center">
          <h1 className="text-xl font-bold text-gray-800 mr-4">LangGraph Workflow Designer</h1>
          <div className="border-l pl-4">
            <input 
              type="text" 
              value={workflowName}
              onChange={(e) => setWorkflowName(e.target.value)}
              className="border rounded p-1 text-sm"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="flex items-center gap-1 text-sm"
          >
            <Upload className="h-4 w-4" />
            Import
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-1 text-sm"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button
            className="flex items-center gap-1 text-sm bg-blue-600 text-white"
          >
            <Save className="h-4 w-4" />
            Save
          </Button>
          <Button
            className="flex items-center gap-1 text-sm bg-green-600 text-white"
          >
            <Play className="h-4 w-4" />
            Test
          </Button>
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left sidebar - Node templates */}
        <div className="w-64 bg-white border-r p-4 overflow-y-auto">
          <h2 className="font-bold mb-4">Komponenten</h2>
          
          <div className="space-y-2">
            <div className="font-medium text-sm text-gray-500 mt-4 mb-2">LLM Nodes</div>
            <div className="flex items-center p-2 border rounded-md hover:bg-gray-50 cursor-pointer">
              <Brain className="h-4 w-4 text-blue-500 mr-2" />
              <span className="text-sm">LLM Prompt</span>
            </div>
            <div className="flex items-center p-2 border rounded-md hover:bg-gray-50 cursor-pointer">
              <Brain className="h-4 w-4 text-blue-500 mr-2" />
              <span className="text-sm">RAG Prompt</span>
            </div>
            
            <div className="font-medium text-sm text-gray-500 mt-4 mb-2">Tools</div>
            <div className="flex items-center p-2 border rounded-md hover:bg-gray-50 cursor-pointer">
              <Database className="h-4 w-4 text-purple-500 mr-2" />
              <span className="text-sm">Knowledge Base</span>
            </div>
            <div className="flex items-center p-2 border rounded-md hover:bg-gray-50 cursor-pointer">
              <ExternalLink className="h-4 w-4 text-purple-500 mr-2" />
              <span className="text-sm">Web Search</span>
            </div>
            <div className="flex items-center p-2 border rounded-md hover:bg-gray-50 cursor-pointer">
              <Code className="h-4 w-4 text-purple-500 mr-2" />
              <span className="text-sm">API Call</span>
            </div>
            
            <div className="font-medium text-sm text-gray-500 mt-4 mb-2">Flow Control</div>
            <div className="flex items-center p-2 border rounded-md hover:bg-gray-50 cursor-pointer">
              <ArrowRight className="h-4 w-4 text-yellow-500 mr-2" />
              <span className="text-sm">Router</span>
            </div>
            <div className="flex items-center p-2 border rounded-md hover:bg-gray-50 cursor-pointer">
              <FileText className="h-4 w-4 text-green-500 mr-2" />
              <span className="text-sm">Input</span>
            </div>
            <div className="flex items-center p-2 border rounded-md hover:bg-gray-50 cursor-pointer">
              <MessageSquare className="h-4 w-4 text-red-500 mr-2" />
              <span className="text-sm">Output</span>
            </div>
            
            <div className="font-medium text-sm text-gray-500 mt-6 mb-2">Templates</div>
            {templates.map(template => (
              <div 
                key={template.id}
                className="flex items-center p-2 border rounded-md hover:bg-gray-50 cursor-pointer"
              >
                <span className="text-sm">{template.name}</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Main canvas */}
        <div className="flex-1 overflow-auto relative">
          <div className="w-full h-full min-h-[800px] relative">
            {/* Render edges */}
            {edges.map(edge => (
              <Edge 
                key={edge.id}
                start={getNodePosition(edge.source)}
                end={getNodePosition(edge.target)}
              />
            ))}
            
            {/* Render nodes */}
            {nodes.map(node => (
              <GraphNode
                key={node.id}
                node={node}
                position={{ x: node.x, y: node.y }}
                selected={selectedNode && selectedNode.id === node.id}
                onSelect={handleNodeSelect}
              />
            ))}
          </div>
        </div>
        
        {/* Right sidebar - Node properties */}
        <div className="w-80 bg-white border-l p-4 overflow-y-auto">
          <h2 className="font-bold mb-4">Eigenschaften</h2>
          
          {selectedNode ? (
            renderNodeDetails()
          ) : (
            <div className="text-gray-500 text-sm">
              Wählen Sie einen Knoten aus, um seine Eigenschaften zu bearbeiten.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LangGraphWorkflowDesigner;
