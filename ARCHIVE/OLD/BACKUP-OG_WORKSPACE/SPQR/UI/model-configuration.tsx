import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Save, Plus, Trash2, RefreshCw, Check, ArrowRight, Brain } from 'lucide-react';

const ModelConfigurationPanel = () => {
  const [loading, setLoading] = useState(true);
  const [models, setModels] = useState([]);
  const [selectedModel, setSelectedModel] = useState(null);
  const [defaultModel, setDefaultModel] = useState(null);
  
  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      const mockModels = [
        {
          id: 'claude-3-opus',
          provider: 'anthropic',
          name: 'Claude 3 Opus',
          isDefault: true,
          isActive: true,
          contextWindow: 200000,
          inputCostPer1kTokens: 0.015,
          outputCostPer1kTokens: 0.075,
          config: {
            temperature: 0.7,
            topP: 0.9,
            maxTokens: 4000,
            stopSequences: [],
            systemPrompt: "<PERSON>, ein KI-Assistent von Anthropic. Beantworte Fragen hilfreich, harmlos und ehrlich, auch wenn die Frage vereinfachend, mehrdeutig oder faktisch falsch ist.",
            apiTimeout: 120000,
            apiKey: "sk-ant-api-key...masked...",
            apiRetries: 3
          },
          usageStats: {
            dailyTokensUsed: 2450000,
            monthlyCost: 158.75,
            averageResponseTime: 1.8
          }
        },
        {
          id: 'gpt-4-turbo',
          provider: 'openai',
          name: 'GPT-4 Turbo',
          isDefault: false,
          isActive: true,
          contextWindow: 128000,
          inputCostPer1kTokens: 0.01,
          outputCostPer1kTokens: 0.03,
          config: {
            temperature: 0.8,
            topP: 1.0,
            maxTokens: 4000,
            stopSequences: [],
            systemPrompt: "Du bist ein KI-Assistent, der hilfreiche, genaue und sichere Antworten gibt.",
            apiTimeout: 90000,
            apiKey: "sk-openai-key...masked...",
            apiRetries: 2
          },
          usageStats: {
            dailyTokensUsed: 1820000,
            monthlyCost: 86.40,
            averageResponseTime: 2.1
          }
        },
        {
          id: 'llama-3-70b',
          provider: 'local',
          name: 'Llama 3 (70B)',
          isDefault: false,
          isActive: true,
          contextWindow: 8192,
          inputCostPer1kTokens: 0.0,
          outputCostPer1kTokens: 0.0,
          config: {
            temperature: 0.7,
            topP: 0.9,
            maxTokens: 2000,
            stopSequences: [],
            systemPrompt: "Du bist ein hilfreicher KI-Assistent.",
            apiTimeout: 60000,
            serverEndpoint: "http://localhost:8080/v1/completions",
            apiRetries: 1
          },
          usageStats: {
            dailyTokensUsed: 950000,
            monthlyCost: 0,
            averageResponseTime: 3.5
          }
        }
      ];
      
      setModels(mockModels);
      setDefaultModel(mockModels.find(m => m.isDefault));
      setSelectedModel(mockModels.find(m => m.isDefault));
      setLoading(false);
    }, 1000);
  }, []);

  const handleModelSelect = (model) => {
    setSelectedModel(model);
  };

  const handleSetDefault = (modelId) => {
    setModels(models.map(model => ({
      ...model,
      isDefault: model.id === modelId
    })));
    setDefaultModel(models.find(m => m.id === modelId));
  };

  const handleToggleActive = (modelId) => {
    setModels(models.map(model => {
      if (model.id === modelId) {
        return {
          ...model,
          isActive: !model.isActive
        };
      }
      return model;
    }));
  };

  const getProviderIcon = (provider) => {
    switch(provider) {
      case 'anthropic':
        return <span className="text-purple-600 font-bold text-sm">C</span>;
      case 'openai':
        return <span className="text-green-600 font-bold text-sm">G</span>;
      case 'local':
        return <span className="text-blue-600 font-bold text-sm">L</span>;
      default:
        return <span className="text-gray-600 font-bold text-sm">?</span>;
    }
  };

  const getProviderColor = (provider) => {
    switch(provider) {
      case 'anthropic':
        return 'border-purple-200 bg-purple-50';
      case 'openai':
        return 'border-green-200 bg-green-50';
      case 'local':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const formatCost = (cost) => {
    return cost.toFixed(3);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">LLM-Modellkonfiguration</h1>
          <p className="text-gray-600">Verwalten Sie Sprachmodelle und deren Einstellungen</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            Neues Modell
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Model List */}
        <div className="lg:col-span-1">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Verfügbare Modelle</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {models.map(model => (
                  <div
                    key={model.id}
                    className={`p-3 border rounded-lg cursor-pointer flex items-center justify-between transition-colors ${
                      selectedModel && selectedModel.id === model.id ? 'border-blue-400 ring-1 ring-blue-400' : 'hover:bg-gray-50'
                    } ${!model.isActive ? 'opacity-60' : ''}`}
                    onClick={() => handleModelSelect(model)}
                  >
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getProviderColor(model.provider)}`}>
                        {getProviderIcon(model.provider)}
                      </div>
                      <div className="ml-3">
                        <div className="font-medium">{model.name}</div>
                        <div className="text-xs text-gray-500">{model.provider}</div>
                      </div>
                    </div>
                    {model.isDefault && (
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">Standard</span>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Model Configuration */}
        <div className="lg:col-span-3">
          {selectedModel ? (
            <div className="space-y-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>{selectedModel.name}</CardTitle>
                  <div className="flex gap-2">
                    {!selectedModel.isDefault && (
                      <Button
                        variant="outline"
                        className="text-sm"
                        onClick={() => handleSetDefault(selectedModel.id)}
                      >
                        Als Standard festlegen
                      </Button>
                    )}
                    <Button
                      variant={selectedModel.isActive ? "outline" : ""}
                      className={`text-sm ${selectedModel.isActive ? "text-yellow-600" : "bg-green-600 text-white"}`}
                      onClick={() => handleToggleActive(selectedModel.id)}
                    >
                      {selectedModel.isActive ? "Deaktivieren" : "Aktivieren"}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-500">Kontextfenster</div>
                      <div className="font-bold text-xl">{selectedModel.contextWindow.toLocaleString()} Tokens</div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-500">Input-Kosten (pro 1K Tokens)</div>
                      <div className="font-bold text-xl">${formatCost(selectedModel.inputCostPer1kTokens)}</div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-500">Output-Kosten (pro 1K Tokens)</div>
                      <div className="font-bold text-xl">${formatCost(selectedModel.outputCostPer1kTokens)}</div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="font-medium text-lg mb-4">Modellkonfiguration</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-700 text-sm font-medium mb-1">Temperature</label>
                        <div className="flex items-center gap-4">
                          <input 
                            type="range" 
                            min="0" 
                            max="2" 
                            step="0.1"
                            className="flex-1"
                            value={selectedModel.config.temperature}
                          />
                          <span className="w-12 text-center">{selectedModel.config.temperature}</span>
                        </div>
                      </div>
                      <div>
                        <label className="block text-gray-700 text-sm font-medium mb-1">Top P</label>
                        <div className="flex items-center gap-4">
                          <input 
                            type="range" 
                            min="0" 
                            max="1" 
                            step="0.05"
                            className="flex-1"
                            value={selectedModel.config.topP}
                          />
                          <span className="w-12 text-center">{selectedModel.config.topP}</span>
                        </div>
                      </div>
                      <div>
                        <label className="block text-gray-700 text-sm font-medium mb-1">Max Tokens</label>
                        <input 
                          type="number" 
                          className="w-full p-2 border rounded-md"
                          value={selectedModel.config.maxTokens}
                        />
                      </div>
                      <div>
                        <label className="block text-gray-700 text-sm font-medium mb-1">API Timeout (ms)</label>
                        <input 
                          type="number" 
                          className="w-full p-2 border rounded-md"
                          value={selectedModel.config.apiTimeout}
                        />
                      </div>
                      <div>
                        <label className="block text-gray-700 text-sm font-medium mb-1">API-Wiederholungen</label>
                        <input 
                          type="number" 
                          className="w-full p-2 border rounded-md"
                          value={selectedModel.config.apiRetries}
                        />
                      </div>
                      {selectedModel.provider === 'local' && (
                        <div>
                          <label className="block text-gray-700 text-sm font-medium mb-1">Server-Endpunkt</label>
                          <input 
                            type="text" 
                            className="w-full p-2 border rounded-md"
                            value={selectedModel.config.serverEndpoint}
                          />
                        </div>
                      )}
                      {(selectedModel.provider === 'anthropic' || selectedModel.provider === 'openai') && (
                        <div>
                          <label className="block text-gray-700 text-sm font-medium mb-1">API-Schlüssel</label>
                          <input 
                            type="password" 
                            className="w-full p-2 border rounded-md"
                            value={selectedModel.config.apiKey}
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="block text-gray-700 text-sm font-medium mb-1">System-Prompt</label>
                    <textarea
                      className="w-full p-2 border rounded-md h-32 font-mono text-sm"
                      value={selectedModel.config.systemPrompt}
                    ></textarea>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      className="bg-blue-600 text-white flex items-center gap-1"
                    >
                      <Save className="h-4 w-4" />
                      Konfiguration speichern
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Nutzungsstatistiken</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-500">Tägliche Token-Nutzung</div>
                      <div className="font-bold text-xl">{selectedModel.usageStats.dailyTokensUsed.toLocaleString()}</div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-500">Monatliche Kosten (geschätzt)</div>
                      <div className="font-bold text-xl">${selectedModel.usageStats.monthlyCost.toFixed(2)}</div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm text-gray-500">Durchschn. Antwortzeit</div>
                      <div className="font-bold text-xl">{selectedModel.usageStats.averageResponseTime}s</div>
                    </div>
                  </div>

                  <div className="mt-4 text-center text-gray-500">
                    Detaillierte Nutzungsanalysen würden hier als Diagramme angezeigt werden.
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center text-gray-500">
                <Brain className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p>Wählen Sie ein Modell aus, um die Konfiguration anzuzeigen</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModelConfigurationPanel;
