-- Event Processing related tables

-- EventProcessing table for tracking workflow execution in n8n
CREATE TABLE IF NOT EXISTS "EventProcessings" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "eventId" UUID NOT NULL REFERENCES "Events"("id") ON DELETE CASCADE,
  "workflowId" VARCHAR(255) NOT NULL,
  "executionId" VARCHAR(255),
  "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
  "startedAt" TIMESTAMP WITH TIME ZONE,
  "completedAt" TIMESTAMP WITH TIME ZONE,
  "result" JSONB,
  "error" TEXT,
  "retries" INTEGER DEFAULT 0,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for EventProcessing
CREATE INDEX IF NOT EXISTS "event_processings_event_id_idx" ON "EventProcessings"("eventId");
CREATE INDEX IF NOT EXISTS "event_processings_status_idx" ON "EventProcessings"("status");
CREATE INDEX IF NOT EXISTS "event_processings_workflow_id_idx" ON "EventProcessings"("workflowId");

-- Notifications table
CREATE TABLE IF NOT EXISTS "Notifications" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "userId" UUID REFERENCES "Users"("id") ON DELETE CASCADE,
  "title" VARCHAR(255) NOT NULL,
  "message" TEXT NOT NULL,
  "type" VARCHAR(50) NOT NULL,
  "status" VARCHAR(50) NOT NULL DEFAULT 'unread',
  "data" JSONB,
  "eventId" UUID REFERENCES "Events"("id") ON DELETE SET NULL,
  "expiresAt" TIMESTAMP WITH TIME ZONE,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for Notifications
CREATE INDEX IF NOT EXISTS "notifications_user_id_idx" ON "Notifications"("userId");
CREATE INDEX IF NOT EXISTS "notifications_status_idx" ON "Notifications"("status");
CREATE INDEX IF NOT EXISTS "notifications_created_at_idx" ON "Notifications"("createdAt" DESC);
CREATE INDEX IF NOT EXISTS "notifications_event_id_idx" ON "Notifications"("eventId");

-- Webhooks table
CREATE TABLE IF NOT EXISTS "Webhooks" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "endpoint" VARCHAR(255) NOT NULL UNIQUE,
  "secret" VARCHAR(255) NOT NULL,
  "targetService" VARCHAR(100),
  "active" BOOLEAN DEFAULT true,
  "userId" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for Webhooks
CREATE INDEX IF NOT EXISTS "webhooks_endpoint_idx" ON "Webhooks"("endpoint");
CREATE INDEX IF NOT EXISTS "webhooks_active_idx" ON "Webhooks"("active");

-- n8n Workflow Configuration
CREATE TABLE IF NOT EXISTS "N8nWorkflows" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "workflowId" VARCHAR(255) NOT NULL UNIQUE,
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "eventTypes" VARCHAR(255)[] NOT NULL,
  "active" BOOLEAN DEFAULT true,
  "config" JSONB,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for N8nWorkflows
CREATE INDEX IF NOT EXISTS "n8n_workflows_active_idx" ON "N8nWorkflows"("active");
CREATE INDEX IF NOT EXISTS "n8n_workflows_event_types_idx" ON "N8nWorkflows" USING GIN ("eventTypes");

-- Functions to help with event processing
CREATE OR REPLACE FUNCTION notify_event_created()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify('event_created', NEW.id::text);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to notify when a new event is created
DROP TRIGGER IF EXISTS event_created_trigger ON "Events";
CREATE TRIGGER event_created_trigger
AFTER INSERT ON "Events"
FOR EACH ROW
EXECUTE FUNCTION notify_event_created();
