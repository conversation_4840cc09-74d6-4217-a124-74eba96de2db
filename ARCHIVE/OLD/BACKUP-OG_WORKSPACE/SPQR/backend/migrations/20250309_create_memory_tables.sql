-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create memory tables for conversation history (replacing Zep Memory)
CREATE TABLE IF NOT EXISTS memory_sessions (
    id UUID PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'::JSONB
);

CREATE TABLE IF NOT EXISTS memory_messages (
    id UUID PRIMARY KEY,
    session_id UUID NOT NULL REFERENCES memory_sessions(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL, -- 'user', 'assistant', 'system'
    content TEXT NOT NULL,
    embedding VECTOR(1536), -- For semantic search
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'::J<PERSON>N<PERSON>,
    tokens_used INTEGER DEFAULT 0
);

CREATE INDEX IF NOT EXISTS idx_memory_messages_session_id ON memory_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_memory_messages_created_at ON memory_messages(created_at);

-- Create memory summaries table
CREATE TABLE IF NOT EXISTS memory_summaries (
    id UUID PRIMARY KEY,
    session_id UUID NOT NULL REFERENCES memory_sessions(id) ON DELETE CASCADE,
    summary TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    start_message_id UUID REFERENCES memory_messages(id),
    end_message_id UUID REFERENCES memory_messages(id),
    tokens_used INTEGER DEFAULT 0
);

CREATE INDEX IF NOT EXISTS idx_memory_summaries_session_id ON memory_summaries(session_id);

-- Create memory search index using pgvector
CREATE INDEX IF NOT EXISTS idx_memory_messages_embedding ON memory_messages USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);

-- Create function to search memory by similarity
CREATE OR REPLACE FUNCTION search_memory(
    p_query_embedding VECTOR(1536),
    p_session_id UUID,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE (
    id UUID,
    session_id UUID,
    role VARCHAR(50),
    content TEXT,
    similarity FLOAT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.session_id,
        m.role,
        m.content,
        1 - (m.embedding <-> p_query_embedding) AS similarity,
        m.created_at
    FROM
        memory_messages m
    WHERE
        m.session_id = p_session_id
        AND m.embedding IS NOT NULL
    ORDER BY
        m.embedding <-> p_query_embedding
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Create function to search across all memory sessions
CREATE OR REPLACE FUNCTION search_all_memory(
    p_query_embedding VECTOR(1536),
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE (
    id UUID,
    session_id UUID,
    role VARCHAR(50),
    content TEXT,
    similarity FLOAT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.session_id,
        m.role,
        m.content,
        1 - (m.embedding <-> p_query_embedding) AS similarity,
        m.created_at
    FROM
        memory_messages m
    WHERE
        m.embedding IS NOT NULL
    ORDER BY
        m.embedding <-> p_query_embedding
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Create tables for LangGraph persistence
CREATE TABLE IF NOT EXISTS langgraph_states (
    id UUID PRIMARY KEY,
    graph_id VARCHAR(255) NOT NULL,
    state JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_id VARCHAR(255),
    metadata JSONB DEFAULT '{}'::JSONB
);

CREATE INDEX IF NOT EXISTS idx_langgraph_states_graph_id ON langgraph_states(graph_id);
CREATE INDEX IF NOT EXISTS idx_langgraph_states_user_id ON langgraph_states(user_id);

-- Create table for storing embeddings for RAG
CREATE TABLE IF NOT EXISTS embeddings (
    id UUID PRIMARY KEY,
    content TEXT NOT NULL,
    embedding VECTOR(1536) NOT NULL,
    metadata JSONB DEFAULT '{}'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_embeddings_embedding ON embeddings USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);

-- Create function to search embeddings by similarity
CREATE OR REPLACE FUNCTION search_embeddings(
    p_query_embedding VECTOR(1536),
    p_limit INTEGER DEFAULT 5,
    p_metadata_filter JSONB DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    content TEXT,
    similarity FLOAT,
    metadata JSONB
) AS $$
BEGIN
    IF p_metadata_filter IS NULL THEN
        RETURN QUERY
        SELECT
            e.id,
            e.content,
            1 - (e.embedding <-> p_query_embedding) AS similarity,
            e.metadata
        FROM
            embeddings e
        ORDER BY
            e.embedding <-> p_query_embedding
        LIMIT p_limit;
    ELSE
        RETURN QUERY
        SELECT
            e.id,
            e.content,
            1 - (e.embedding <-> p_query_embedding) AS similarity,
            e.metadata
        FROM
            embeddings e
        WHERE
            e.metadata @> p_metadata_filter
        ORDER BY
            e.embedding <-> p_query_embedding
        LIMIT p_limit;
    END IF;
END;
$$ LANGUAGE plpgsql;
