-- pgvector-Erweiterung aktivieren
CREATE EXTENSION IF NOT EXISTS vector;

-- Tabelle für Wissensfragmente
CREATE TABLE IF NOT EXISTS knowledge_chunks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL,
  document_name TEXT NOT NULL,
  content TEXT NOT NULL,
  metadata JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabelle für Einbettungen
CREATE TABLE IF NOT EXISTS embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chunk_id UUID NOT NULL REFERENCES knowledge_chunks(id) ON DELETE CASCADE,
  embedding VECTOR(1536) NOT NULL,
  model_name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabelle für Abfragehistorie
CREATE TABLE IF NOT EXISTS query_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  query TEXT NOT NULL,
  embedding VECTOR(1536),
  results JSONB NOT NULL DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabelle für Gesprächsspeicher
CREATE TABLE IF NOT EXISTS conversation_memory (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  conversation_id UUID NOT NULL,
  message JSONB NOT NULL,
  turn_number INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indizierung für Leistungsoptimierung
CREATE INDEX IF NOT EXISTS idx_knowledge_chunks_document_id ON knowledge_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_embeddings_chunk_id ON embeddings(chunk_id);
CREATE INDEX IF NOT EXISTS idx_query_history_user_id ON query_history(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_memory_user_id ON conversation_memory(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_memory_conversation_id ON conversation_memory(conversation_id);

-- Ähnlichkeitssuche-Funktion
CREATE OR REPLACE FUNCTION similarity_search(
  query_embedding VECTOR(1536),
  match_threshold FLOAT,
  match_count INT
) RETURNS TABLE (
  id UUID,
  chunk_id UUID,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT
    e.id,
    e.chunk_id,
    kc.content,
    kc.metadata,
    1 - (e.embedding <=> query_embedding) AS similarity
  FROM
    embeddings e
    JOIN knowledge_chunks kc ON e.chunk_id = kc.id
  WHERE
    1 - (e.embedding <=> query_embedding) > match_threshold
  ORDER BY
    similarity DESC
  LIMIT match_count;
END;
$$;

-- Hybridsuche-Funktion (kombiniert semantische Suche mit Stichwortsuche)
CREATE OR REPLACE FUNCTION hybrid_search(
  query_text TEXT,
  query_embedding VECTOR(1536),
  match_threshold FLOAT,
  match_count INT
) RETURNS TABLE (
  id UUID,
  chunk_id UUID,
  content TEXT,
  metadata JSONB,
  similarity FLOAT,
  combined_score FLOAT
) LANGUAGE plpgsql AS $$
DECLARE
  semantic_weight FLOAT := 0.8;
  keyword_weight FLOAT := 0.2;
BEGIN
  RETURN QUERY
  WITH semantic_results AS (
    SELECT
      e.id,
      e.chunk_id,
      kc.content,
      kc.metadata,
      1 - (e.embedding <=> query_embedding) AS similarity
    FROM
      embeddings e
      JOIN knowledge_chunks kc ON e.chunk_id = kc.id
    WHERE
      1 - (e.embedding <=> query_embedding) > match_threshold
  ),
  keyword_results AS (
    SELECT
      e.id,
      e.chunk_id,
      kc.content,
      kc.metadata,
      ts_rank(to_tsvector('german', kc.content), to_tsquery('german', query_text)) AS keyword_score
    FROM
      embeddings e
      JOIN knowledge_chunks kc ON e.chunk_id = kc.id
    WHERE
      kc.content ILIKE '%' || query_text || '%'
  )
  SELECT
    sr.id,
    sr.chunk_id,
    sr.content,
    sr.metadata,
    sr.similarity,
    COALESCE(sr.similarity * semantic_weight, 0) + 
    COALESCE(kr.keyword_score * keyword_weight, 0) AS combined_score
  FROM
    semantic_results sr
    LEFT JOIN keyword_results kr ON sr.id = kr.id
  ORDER BY
    combined_score DESC
  LIMIT match_count;
END;
$$;

-- IVFFLAT-Index auf embeddings für schnellere Ähnlichkeitssuche
-- Hinweis: Dies sollte nach dem Befüllen der Tabelle mit ausreichend Daten durchgeführt werden
-- CREATE INDEX IF NOT EXISTS embeddings_vector_idx ON embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
