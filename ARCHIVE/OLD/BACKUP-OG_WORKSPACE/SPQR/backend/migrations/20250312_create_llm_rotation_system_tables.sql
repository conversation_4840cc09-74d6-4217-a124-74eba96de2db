-- LLM Rotation System Tables Migration
-- Version: 1.0
-- Date: 2025-03-12

-- Create LLM providers table
CREATE TABLE IF NOT EXISTS llm_providers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  api_keys JSONB NOT NULL, -- Array of API keys for rotation
  models JSONB NOT NULL, -- Array of supported models with their capabilities
  base_url VARCHAR(255), -- Base URL for API calls
  rate_limits JSONB NOT NULL, -- Rate limits for the provider
  current_usage JSONB NOT NULL, -- Current usage statistics
  tier VARCHAR(50) NOT NULL CHECK (tier IN ('premium', 'standard', 'basic')), -- Provider tier
  status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'rate-limited')), -- Provider status
  capabilities JSONB NOT NULL, -- Array of provider capabilities
  cost_per_token JSONB NOT NULL, -- Cost per token for input and output
  quality_score FLOAT NOT NULL DEFAULT 0.5, -- Quality score (0-1)
  reliability_score FLOAT NOT NULL DEFAULT 0.5, -- Reliability score (0-1)
  speed_score FLOAT NOT NULL DEFAULT 0.5, -- Speed score (0-1)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on provider name for faster lookups
CREATE INDEX IF NOT EXISTS idx_llm_providers_name ON llm_providers(name);
CREATE INDEX IF NOT EXISTS idx_llm_providers_tier ON llm_providers(tier);
CREATE INDEX IF NOT EXISTS idx_llm_providers_status ON llm_providers(status);

-- Create LLM responses table to track all responses
CREATE TABLE IF NOT EXISTS llm_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL,
  provider_id UUID NOT NULL REFERENCES llm_providers(id),
  model_id VARCHAR(255) NOT NULL, -- Model ID from the provider's models array
  prompt TEXT NOT NULL, -- The original prompt
  response TEXT NOT NULL, -- The LLM response
  input_tokens INTEGER NOT NULL, -- Number of input tokens
  output_tokens INTEGER NOT NULL, -- Number of output tokens
  cost FLOAT NOT NULL, -- Cost of the request
  duration INTEGER, -- Duration in milliseconds
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  success BOOLEAN NOT NULL DEFAULT TRUE, -- Whether the request was successful
  error TEXT, -- Error message if the request failed
  metadata JSONB, -- Additional metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_llm_responses_task_id ON llm_responses(task_id);
CREATE INDEX IF NOT EXISTS idx_llm_responses_provider_id ON llm_responses(provider_id);
CREATE INDEX IF NOT EXISTS idx_llm_responses_start_time ON llm_responses(start_time);
CREATE INDEX IF NOT EXISTS idx_llm_responses_success ON llm_responses(success);

-- Create quality analyses table to track response quality
CREATE TABLE IF NOT EXISTS quality_analyses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  response_id UUID NOT NULL REFERENCES llm_responses(id) ON DELETE CASCADE,
  provider_id UUID NOT NULL REFERENCES llm_providers(id),
  model_id VARCHAR(255) NOT NULL, -- Model ID from the provider's models array
  task_id UUID NOT NULL,
  relevance_score FLOAT NOT NULL, -- Relevance score (0-1)
  accuracy_score FLOAT NOT NULL, -- Accuracy score (0-1)
  completeness_score FLOAT NOT NULL, -- Completeness score (0-1)
  coherence_score FLOAT NOT NULL, -- Coherence score (0-1)
  helpfulness_score FLOAT NOT NULL, -- Helpfulness score (0-1)
  overall_score FLOAT NOT NULL, -- Overall quality score (0-1)
  analysis_method VARCHAR(50) NOT NULL CHECK (analysis_method IN ('heuristic', 'llm')), -- Method used for analysis
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_quality_analyses_response_id ON quality_analyses(response_id);
CREATE INDEX IF NOT EXISTS idx_quality_analyses_provider_id ON quality_analyses(provider_id);
CREATE INDEX IF NOT EXISTS idx_quality_analyses_overall_score ON quality_analyses(overall_score);

-- Create task classifications table to track task classifications
CREATE TABLE IF NOT EXISTS task_classifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL,
  complexity FLOAT NOT NULL, -- Complexity score (0-1)
  importance FLOAT NOT NULL, -- Importance score (0-1)
  creativity FLOAT NOT NULL, -- Creativity requirements score (0-1)
  factuality FLOAT NOT NULL, -- Factuality requirements score (0-1)
  sensitivity FLOAT NOT NULL, -- Sensitivity score (0-1)
  recommended_tier VARCHAR(50) NOT NULL CHECK (recommended_tier IN ('premium', 'standard', 'basic')), -- Recommended provider tier
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on task_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_task_classifications_task_id ON task_classifications(task_id);
CREATE INDEX IF NOT EXISTS idx_task_classifications_recommended_tier ON task_classifications(recommended_tier);

-- Create provider performance table to track provider performance over time
CREATE TABLE IF NOT EXISTS provider_performance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID NOT NULL REFERENCES llm_providers(id),
  date DATE NOT NULL,
  requests_count INTEGER NOT NULL DEFAULT 0, -- Number of requests
  success_count INTEGER NOT NULL DEFAULT 0, -- Number of successful requests
  error_count INTEGER NOT NULL DEFAULT 0, -- Number of failed requests
  total_tokens INTEGER NOT NULL DEFAULT 0, -- Total tokens used
  total_cost FLOAT NOT NULL DEFAULT 0, -- Total cost
  average_quality_score FLOAT, -- Average quality score
  average_response_time INTEGER, -- Average response time in milliseconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_provider_performance_provider_id ON provider_performance(provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_performance_date ON provider_performance(date);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_llm_rotation_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for tables with updated_at column
CREATE TRIGGER update_llm_providers_updated_at
BEFORE UPDATE ON llm_providers
FOR EACH ROW
EXECUTE FUNCTION update_llm_rotation_updated_at_column();

CREATE TRIGGER update_provider_performance_updated_at
BEFORE UPDATE ON provider_performance
FOR EACH ROW
EXECUTE FUNCTION update_llm_rotation_updated_at_column();

-- Insert default providers
INSERT INTO llm_providers (
  id, name, description, api_keys, models, base_url, rate_limits, current_usage, 
  tier, status, capabilities, cost_per_token, quality_score, reliability_score, speed_score
) VALUES (
  'openai', 'OpenAI', 'OpenAI API', 
  '[""]'::JSONB, -- Empty API key, will be set via environment variable
  '[
    {
      "id": "gpt-4",
      "name": "GPT-4",
      "maxTokens": 8192,
      "tier": "premium",
      "capabilities": ["text-generation", "text-completion", "function-calling", "code-generation"],
      "costPerToken": {
        "input": 0.00003,
        "output": 0.00006
      }
    },
    {
      "id": "gpt-4-turbo",
      "name": "GPT-4 Turbo",
      "maxTokens": 128000,
      "tier": "premium",
      "capabilities": ["text-generation", "text-completion", "function-calling", "code-generation"],
      "costPerToken": {
        "input": 0.00001,
        "output": 0.00003
      }
    },
    {
      "id": "gpt-3.5-turbo",
      "name": "GPT-3.5 Turbo",
      "maxTokens": 16385,
      "tier": "standard",
      "capabilities": ["text-generation", "text-completion", "function-calling", "code-generation"],
      "costPerToken": {
        "input": 0.0000015,
        "output": 0.000002
      }
    }
  ]'::JSONB,
  'https://api.openai.com/v1',
  '{"requestsPerMinute": 60, "requestsPerDay": 1000, "tokensPerMinute": 40000, "tokensPerDay": 1000000}'::JSONB,
  '{"requestsToday": 0, "tokensToday": 0, "requestsThisMinute": 0, "tokensThisMinute": 0, "lastMinuteTimestamp": 0, "lastDayTimestamp": 0}'::JSONB,
  'premium', 'active', 
  '["text-generation", "text-completion", "function-calling", "code-generation"]'::JSONB,
  '{"input": 0.00001, "output": 0.00002}'::JSONB,
  0.9, 0.95, 0.8
);

INSERT INTO llm_providers (
  id, name, description, api_keys, models, base_url, rate_limits, current_usage, 
  tier, status, capabilities, cost_per_token, quality_score, reliability_score, speed_score
) VALUES (
  'anthropic', 'Anthropic', 'Anthropic API', 
  '[""]'::JSONB, -- Empty API key, will be set via environment variable
  '[
    {
      "id": "claude-3-opus",
      "name": "Claude 3 Opus",
      "maxTokens": 200000,
      "tier": "premium",
      "capabilities": ["text-generation", "text-completion", "function-calling", "code-generation"],
      "costPerToken": {
        "input": 0.00001,
        "output": 0.00003
      }
    },
    {
      "id": "claude-3-sonnet",
      "name": "Claude 3 Sonnet",
      "maxTokens": 200000,
      "tier": "standard",
      "capabilities": ["text-generation", "text-completion", "function-calling", "code-generation"],
      "costPerToken": {
        "input": 0.000003,
        "output": 0.000015
      }
    },
    {
      "id": "claude-3-haiku",
      "name": "Claude 3 Haiku",
      "maxTokens": 200000,
      "tier": "basic",
      "capabilities": ["text-generation", "text-completion", "function-calling", "code-generation"],
      "costPerToken": {
        "input": 0.00000025,
        "output": 0.00000125
      }
    }
  ]'::JSONB,
  'https://api.anthropic.com/v1',
  '{"requestsPerMinute": 60, "requestsPerDay": 1000, "tokensPerMinute": 40000, "tokensPerDay": 1000000}'::JSONB,
  '{"requestsToday": 0, "tokensToday": 0, "requestsThisMinute": 0, "tokensThisMinute": 0, "lastMinuteTimestamp": 0, "lastDayTimestamp": 0}'::JSONB,
  'premium', 'active', 
  '["text-generation", "text-completion", "function-calling", "code-generation"]'::JSONB,
  '{"input": 0.000005, "output": 0.000015}'::JSONB,
  0.95, 0.9, 0.85
);

INSERT INTO llm_providers (
  id, name, description, api_keys, models, base_url, rate_limits, current_usage, 
  tier, status, capabilities, cost_per_token, quality_score, reliability_score, speed_score
) VALUES (
  'google', 'Google', 'Google AI API', 
  '[""]'::JSONB, -- Empty API key, will be set via environment variable
  '[
    {
      "id": "gemini-pro",
      "name": "Gemini Pro",
      "maxTokens": 32768,
      "tier": "standard",
      "capabilities": ["text-generation", "text-completion", "function-calling", "code-generation"],
      "costPerToken": {
        "input": 0.000001,
        "output": 0.000002
      }
    },
    {
      "id": "gemini-ultra",
      "name": "Gemini Ultra",
      "maxTokens": 32768,
      "tier": "premium",
      "capabilities": ["text-generation", "text-completion", "function-calling", "code-generation"],
      "costPerToken": {
        "input": 0.000005,
        "output": 0.00001
      }
    }
  ]'::JSONB,
  'https://generativelanguage.googleapis.com/v1',
  '{"requestsPerMinute": 60, "requestsPerDay": 1000, "tokensPerMinute": 40000, "tokensPerDay": 1000000}'::JSONB,
  '{"requestsToday": 0, "tokensToday": 0, "requestsThisMinute": 0, "tokensThisMinute": 0, "lastMinuteTimestamp": 0, "lastDayTimestamp": 0}'::JSONB,
  'standard', 'active', 
  '["text-generation", "text-completion", "function-calling", "code-generation"]'::JSONB,
  '{"input": 0.000001, "output": 0.000002}'::JSONB,
  0.85, 0.9, 0.9
);

INSERT INTO llm_providers (
  id, name, description, api_keys, models, base_url, rate_limits, current_usage, 
  tier, status, capabilities, cost_per_token, quality_score, reliability_score, speed_score
) VALUES (
  'sambanova', 'SambaNova', 'SambaNova API', 
  '[""]'::JSONB, -- Empty API key, will be set via environment variable
  '[
    {
      "id": "sambanova-1",
      "name": "SambaNova 1",
      "maxTokens": 16000,
      "tier": "basic",
      "capabilities": ["text-generation", "text-completion"],
      "costPerToken": {
        "input": 0.0000005,
        "output": 0.000001
      }
    }
  ]'::JSONB,
  'https://api.sambanova.ai',
  '{"requestsPerMinute": 60, "requestsPerDay": 1000, "tokensPerMinute": 40000, "tokensPerDay": 1000000}'::JSONB,
  '{"requestsToday": 0, "tokensToday": 0, "requestsThisMinute": 0, "tokensThisMinute": 0, "lastMinuteTimestamp": 0, "lastDayTimestamp": 0}'::JSONB,
  'basic', 'active', 
  '["text-generation", "text-completion"]'::JSONB,
  '{"input": 0.0000005, "output": 0.000001}'::JSONB,
  0.7, 0.8, 0.75
);

INSERT INTO llm_providers (
  id, name, description, api_keys, models, base_url, rate_limits, current_usage, 
  tier, status, capabilities, cost_per_token, quality_score, reliability_score, speed_score
) VALUES (
  'ollama', 'Ollama', 'Local Ollama instance', 
  '[""]'::JSONB, -- No API key needed for local Ollama
  '[
    {
      "id": "llama3",
      "name": "Llama 3",
      "maxTokens": 8192,
      "tier": "basic",
      "capabilities": ["text-generation", "text-completion", "code-generation"],
      "costPerToken": {
        "input": 0,
        "output": 0
      }
    },
    {
      "id": "mistral",
      "name": "Mistral",
      "maxTokens": 8192,
      "tier": "basic",
      "capabilities": ["text-generation", "text-completion", "code-generation"],
      "costPerToken": {
        "input": 0,
        "output": 0
      }
    }
  ]'::JSONB,
  'http://localhost:11434/api',
  '{"requestsPerMinute": 60, "requestsPerDay": 1000, "tokensPerMinute": 40000, "tokensPerDay": 1000000}'::JSONB,
  '{"requestsToday": 0, "tokensToday": 0, "requestsThisMinute": 0, "tokensThisMinute": 0, "lastMinuteTimestamp": 0, "lastDayTimestamp": 0}'::JSONB,
  'basic', 'active', 
  '["text-generation", "text-completion", "code-generation"]'::JSONB,
  '{"input": 0, "output": 0}'::JSONB,
  0.6, 0.7, 0.9
);

-- Add comments to tables
COMMENT ON TABLE llm_providers IS 'Stores information about LLM providers, their models, and capabilities';
COMMENT ON TABLE llm_responses IS 'Stores all LLM responses for analysis and tracking';
COMMENT ON TABLE quality_analyses IS 'Stores quality analyses of LLM responses';
COMMENT ON TABLE task_classifications IS 'Stores classifications of tasks for provider selection';
COMMENT ON TABLE provider_performance IS 'Stores provider performance metrics over time';
