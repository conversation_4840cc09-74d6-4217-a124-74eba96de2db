-- Entity Relationship Core Module Tables

-- Base Entity table (abstract base table for polymorphic relationships)
CREATE TABLE "BaseEntities" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "entityType" VARCHAR(50) NOT NULL,
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "metadata" JSONB DEFAULT '{}'::jsonb,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "createdById" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  "updatedById" UUID REFERENCES "Users"("id") ON DELETE SET NULL
);

-- Organizations table with hierarchical relationships
CREATE TABLE "Organizations" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "baseEntityId" UUID NOT NULL REFERENCES "BaseEntities"("id") ON DELETE CASCADE,
  "parentOrganizationId" UUID REFERENCES "Organizations"("id") ON DELETE SET NULL,
  "organizationType" VARCHAR(50) DEFAULT 'company',
  "industry" VARCHAR(100),
  "website" VARCHAR(255),
  "status" VARCHAR(50) DEFAULT 'active',
  "email" VARCHAR(255),
  "phone" VARCHAR(50),
  "address" TEXT,
  "taxId" VARCHAR(50),
  "employeeCount" INTEGER,
  "foundedYear" INTEGER,
  "annualRevenue" DECIMAL(15, 2),
  "customFields" JSONB DEFAULT '{}'::jsonb,
  
  CONSTRAINT "unique_organization_base_entity" UNIQUE ("baseEntityId")
);

-- Organization hierarchy history for tracking changes
CREATE TABLE "OrganizationHierarchyHistory" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "organizationId" UUID NOT NULL REFERENCES "Organizations"("id") ON DELETE CASCADE,
  "previousParentId" UUID REFERENCES "Organizations"("id") ON DELETE SET NULL,
  "newParentId" UUID REFERENCES "Organizations"("id") ON DELETE SET NULL,
  "changedById" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  "changedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "reason" TEXT
);

-- Departments within organizations
CREATE TABLE "Departments" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "baseEntityId" UUID NOT NULL REFERENCES "BaseEntities"("id") ON DELETE CASCADE,
  "organizationId" UUID NOT NULL REFERENCES "Organizations"("id") ON DELETE CASCADE,
  "parentDepartmentId" UUID REFERENCES "Departments"("id") ON DELETE SET NULL,
  "function" VARCHAR(100),
  "location" VARCHAR(255),
  "headCount" INTEGER DEFAULT 0,
  "budget" DECIMAL(15, 2),
  "customFields" JSONB DEFAULT '{}'::jsonb,
  
  CONSTRAINT "unique_department_base_entity" UNIQUE ("baseEntityId")
);

-- Contacts (replacing Customer model for more flexibility)
CREATE TABLE "Contacts" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "baseEntityId" UUID NOT NULL REFERENCES "BaseEntities"("id") ON DELETE CASCADE,
  "email" VARCHAR(255),
  "firstName" VARCHAR(100),
  "lastName" VARCHAR(100),
  "title" VARCHAR(100),
  "phone" VARCHAR(50),
  "mobilePhone" VARCHAR(50),
  "address" TEXT,
  "timezone" VARCHAR(50),
  "language" VARCHAR(50),
  "status" VARCHAR(50) DEFAULT 'active',
  "source" VARCHAR(100),
  "notes" TEXT,
  "customFields" JSONB DEFAULT '{}'::jsonb,
  "primaryOrganizationId" UUID REFERENCES "Organizations"("id") ON DELETE SET NULL,
  "aiInsights" JSONB DEFAULT '{}'::jsonb,
  "lastContact" TIMESTAMP WITH TIME ZONE,
  "assignedAgentId" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  
  CONSTRAINT "unique_contact_base_entity" UNIQUE ("baseEntityId")
);

-- Contact-Department relationship (many-to-many)
CREATE TABLE "ContactDepartments" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "contactId" UUID NOT NULL REFERENCES "Contacts"("id") ON DELETE CASCADE,
  "departmentId" UUID NOT NULL REFERENCES "Departments"("id") ON DELETE CASCADE,
  "organizationId" UUID NOT NULL REFERENCES "Organizations"("id") ON DELETE CASCADE,
  "role" VARCHAR(100),
  "isPrimary" BOOLEAN DEFAULT false,
  "startDate" DATE,
  "endDate" DATE,
  "customFields" JSONB DEFAULT '{}'::jsonb,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  CONSTRAINT "unique_contact_department" UNIQUE ("contactId", "departmentId")
);

-- Projects table (container for contracts and activities)
CREATE TABLE "Projects" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "baseEntityId" UUID NOT NULL REFERENCES "BaseEntities"("id") ON DELETE CASCADE,
  "primaryOrganizationId" UUID NOT NULL REFERENCES "Organizations"("id") ON DELETE CASCADE,
  "status" VARCHAR(50) DEFAULT 'active',
  "startDate" DATE,
  "targetEndDate" DATE,
  "actualEndDate" DATE,
  "budget" DECIMAL(15, 2),
  "costToDate" DECIMAL(15, 2) DEFAULT 0,
  "priority" VARCHAR(20) DEFAULT 'medium',
  "category" VARCHAR(100),
  "customFields" JSONB DEFAULT '{}'::jsonb,
  "primaryContactId" UUID REFERENCES "Contacts"("id") ON DELETE SET NULL,
  "managerId" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  
  CONSTRAINT "unique_project_base_entity" UNIQUE ("baseEntityId")
);

-- Contracts table (can be associated with projects)
CREATE TABLE "Contracts" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "baseEntityId" UUID NOT NULL REFERENCES "BaseEntities"("id") ON DELETE CASCADE,
  "projectId" UUID REFERENCES "Projects"("id") ON DELETE SET NULL,
  "organizationId" UUID NOT NULL REFERENCES "Organizations"("id") ON DELETE CASCADE,
  "contractNumber" VARCHAR(100) UNIQUE,
  "contractType" VARCHAR(50) DEFAULT 'standard',
  "status" VARCHAR(50) DEFAULT 'draft',
  "startDate" DATE,
  "endDate" DATE,
  "value" DECIMAL(15, 2),
  "currency" VARCHAR(3) DEFAULT 'USD',
  "renewalDate" DATE,
  "terminationDate" DATE,
  "terminationReason" TEXT,
  "primaryContactId" UUID REFERENCES "Contacts"("id") ON DELETE SET NULL,
  "secondaryContactId" UUID REFERENCES "Contacts"("id") ON DELETE SET NULL,
  "customFields" JSONB DEFAULT '{}'::jsonb,
  "documentPath" VARCHAR(255),
  "signedByContactId" UUID REFERENCES "Contacts"("id") ON DELETE SET NULL,
  "signedDate" DATE,
  "paymentTerms" TEXT,
  "specialConditions" TEXT,
  
  CONSTRAINT "unique_contract_base_entity" UNIQUE ("baseEntityId")
);

-- Contract Stages table (configurable workflow stages)
CREATE TABLE "ContractStages" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "contractId" UUID NOT NULL REFERENCES "Contracts"("id") ON DELETE CASCADE,
  "name" VARCHAR(100) NOT NULL,
  "status" VARCHAR(50) DEFAULT 'pending',
  "startDate" TIMESTAMP WITH TIME ZONE,
  "completedDate" TIMESTAMP WITH TIME ZONE,
  "dueDate" TIMESTAMP WITH TIME ZONE,
  "completedById" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  "notes" TEXT,
  "sequenceOrder" INTEGER NOT NULL,
  "metadata" JSONB DEFAULT '{}'::jsonb,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Relationships table (polymorphic connections between any entities)
CREATE TABLE "Relationships" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "sourceEntityId" UUID NOT NULL REFERENCES "BaseEntities"("id") ON DELETE CASCADE,
  "targetEntityId" UUID NOT NULL REFERENCES "BaseEntities"("id") ON DELETE CASCADE,
  "relationshipType" VARCHAR(100) NOT NULL,
  "startDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "endDate" TIMESTAMP WITH TIME ZONE,
  "metadata" JSONB DEFAULT '{}'::jsonb,
  "createdById" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  CONSTRAINT "unique_entity_relationship" UNIQUE ("sourceEntityId", "targetEntityId", "relationshipType")
);

-- Relationship History table (tracks changes to relationships)
CREATE TABLE "RelationshipHistory" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "relationshipId" UUID NOT NULL REFERENCES "Relationships"("id") ON DELETE CASCADE,
  "previousSourceEntityId" UUID REFERENCES "BaseEntities"("id") ON DELETE SET NULL,
  "previousTargetEntityId" UUID REFERENCES "BaseEntities"("id") ON DELETE SET NULL,
  "previousRelationshipType" VARCHAR(100),
  "previousMetadata" JSONB,
  "changedById" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  "changedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "reason" TEXT
);

-- Indexes for performance
CREATE INDEX "idx_base_entities_entity_type" ON "BaseEntities"("entityType");
CREATE INDEX "idx_organizations_parent_id" ON "Organizations"("parentOrganizationId");
CREATE INDEX "idx_contacts_primary_organization" ON "Contacts"("primaryOrganizationId");
CREATE INDEX "idx_projects_primary_organization" ON "Projects"("primaryOrganizationId");
CREATE INDEX "idx_contracts_project_id" ON "Contracts"("projectId");
CREATE INDEX "idx_contracts_organization_id" ON "Contracts"("organizationId");
CREATE INDEX "idx_departments_organization_id" ON "Departments"("organizationId");
CREATE INDEX "idx_contact_departments_contact_id" ON "ContactDepartments"("contactId");
CREATE INDEX "idx_contact_departments_department_id" ON "ContactDepartments"("departmentId");
CREATE INDEX "idx_relationships_source_entity" ON "Relationships"("sourceEntityId");
CREATE INDEX "idx_relationships_target_entity" ON "Relationships"("targetEntityId");
CREATE INDEX "idx_relationships_type" ON "Relationships"("relationshipType");
