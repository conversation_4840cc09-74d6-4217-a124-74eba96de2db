-- Create agent system tables for SPQR system

-- Agents table to store agent definitions
CREATE TABLE IF NOT EXISTS agents (
    id UUID PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL, -- e.g., 'chat', 'task', 'specialized'
    model VARCHAR(255) NOT NULL, -- e.g., 'claude-3-opus', 'gpt-4'
    capabilities JSONB DEFAULT '[]'::JSONB, -- Array of agent capabilities
    config JSONB DEFAULT '{}'::JSONB, -- Configuration settings
    system_prompt TEXT, -- Default system prompt
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- 'active', 'inactive', 'deprecated'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_agents_type ON agents(type);
CREATE INDEX IF NOT EXISTS idx_agents_status ON agents(status);

-- Agent executions to track agent invocations
CREATE TABLE IF NOT EXISTS agent_executions (
    id UUID PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    input TEXT NOT NULL, -- User input/request
    context JSONB DEFAULT '{}'::JSONB, -- Context provided for execution
    output TEXT, -- Agent response
    status VARCHAR(50) NOT NULL, -- 'running', 'completed', 'failed'
    error TEXT, -- Error message if failed
    metrics JSONB DEFAULT '{}'::JSONB, -- Performance metrics
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    time_taken_ms INTEGER -- Execution time in milliseconds
);

CREATE INDEX IF NOT EXISTS idx_agent_executions_agent_id ON agent_executions(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_executions_status ON agent_executions(status);
CREATE INDEX IF NOT EXISTS idx_agent_executions_started_at ON agent_executions(started_at);

-- Agent tools table to store available tools for agents
CREATE TABLE IF NOT EXISTS agent_tools (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    function_schema JSONB NOT NULL, -- JSON Schema for the function
    handler VARCHAR(255) NOT NULL, -- Path to the function handler
    category VARCHAR(100), -- Tool category
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_agent_tools_category ON agent_tools(category);
CREATE INDEX IF NOT EXISTS idx_agent_tools_enabled ON agent_tools(enabled);

-- Agent tool executions to track tool usage
CREATE TABLE IF NOT EXISTS agent_tool_executions (
    id UUID PRIMARY KEY,
    execution_id UUID NOT NULL REFERENCES agent_executions(id) ON DELETE CASCADE,
    tool_id UUID NOT NULL REFERENCES agent_tools(id) ON DELETE CASCADE,
    parameters JSONB NOT NULL, -- Tool input parameters
    result JSONB, -- Tool execution result
    error TEXT, -- Error message if failed
    status VARCHAR(50) NOT NULL, -- 'running', 'completed', 'failed'
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    time_taken_ms INTEGER -- Execution time in milliseconds
);

CREATE INDEX IF NOT EXISTS idx_agent_tool_executions_execution_id ON agent_tool_executions(execution_id);
CREATE INDEX IF NOT EXISTS idx_agent_tool_executions_tool_id ON agent_tool_executions(tool_id);
CREATE INDEX IF NOT EXISTS idx_agent_tool_executions_status ON agent_tool_executions(status);

-- Agent registry settings for global configuration
CREATE TABLE IF NOT EXISTS agent_registry_settings (
    id UUID PRIMARY KEY,
    key VARCHAR(255) NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to automatically update the updated_at column
CREATE TRIGGER update_agents_updated_at
BEFORE UPDATE ON agents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_tools_updated_at
BEFORE UPDATE ON agent_tools
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_registry_settings_updated_at
BEFORE UPDATE ON agent_registry_settings
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a function to calculate time_taken_ms on completion
CREATE OR REPLACE FUNCTION calculate_execution_time()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.completed_at IS NOT NULL AND OLD.completed_at IS NULL THEN
        NEW.time_taken_ms = EXTRACT(EPOCH FROM (NEW.completed_at - NEW.started_at)) * 1000;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to automatically calculate execution time
CREATE TRIGGER calculate_agent_execution_time
BEFORE UPDATE ON agent_executions
FOR EACH ROW
EXECUTE FUNCTION calculate_execution_time();

CREATE TRIGGER calculate_agent_tool_execution_time
BEFORE UPDATE ON agent_tool_executions
FOR EACH ROW
EXECUTE FUNCTION calculate_execution_time();

-- Insert default demo agents
INSERT INTO agents (id, name, description, type, model, capabilities, config, status)
VALUES 
(
    'f47ac10b-58cc-4372-a567-0e02b2c3d479', 
    'General Assistant', 
    'A general-purpose AI assistant that can answer questions and provide information.', 
    'chat', 
    'claude-3-sonnet-20240229', 
    '["answer_questions", "provide_information", "summarize_content"]'::JSONB, 
    '{
        "system_prompt": "You are a helpful, harmless, and honest AI assistant. You answer user questions accurately and concisely.",
        "max_tokens": 4000,
        "temperature": 0.7
    }'::JSONB, 
    'active'
),
(
    'ba0b8dbb-57c1-4c14-86d8-2ac3d6365ad1', 
    'Data Analyst', 
    'An AI agent specialized in analyzing and visualizing data.', 
    'specialized', 
    'claude-3-opus-20240229', 
    '["data_analysis", "generate_visualizations", "explain_insights"]'::JSONB, 
    '{
        "system_prompt": "You are a data analyst. Help users understand their data, provide statistical insights, and suggest visualizations.",
        "max_tokens": 4000,
        "temperature": 0.2
    }'::JSONB, 
    'active'
),
(
    '3f7b38c1-c291-4a5d-afef-22163c52926a', 
    'Code Assistant', 
    'An AI agent specialized in writing and debugging code.', 
    'specialized', 
    'gpt-4', 
    '["write_code", "debug_code", "explain_code", "refactor_code"]'::JSONB, 
    '{
        "system_prompt": "You are a helpful AI programming assistant. You help users write, debug, and understand code. Provide clear explanations and examples.",
        "max_tokens": 4000,
        "temperature": 0.3
    }'::JSONB, 
    'active'
);
