-- Create the metrics schema to organize all metrics-related tables
CREATE SCHEMA IF NOT EXISTS metrics;

-- Table for storing system component metrics
CREATE TABLE metrics.component_metrics (
    id SERIAL PRIMARY KEY,
    component_name VARCHAR(50) NOT NULL,
    metric_name VARCHAR(50) NOT NULL,
    metric_value DOUBLE PRECISION NOT NULL,
    unit VARCHAR(20),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Table for business KPIs
CREATE TABLE metrics.business_kpis (
    id SERIAL PRIMARY KEY,
    kpi_name VARCHAR(50) NOT NULL,
    kpi_value DOUBLE PRECISION NOT NULL,
    previous_value DOUBLE PRECISION,
    change_percentage DOUBLE PRECISION,
    trend VARCHAR(10), -- 'up', 'down', 'stable'
    target_value DOUBLE PRECISION,
    unit VARCHAR(20),
    time_period VARCHAR(20) NOT NULL, -- 'hourly', 'daily', 'weekly', 'monthly'
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Table for AI system metrics
CREATE TABLE metrics.ai_metrics (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER REFERENCES public.agent_registry(id) ON DELETE SET NULL,
    metric_name VARCHAR(50) NOT NULL, -- 'accuracy', 'latency', 'token_usage', etc.
    metric_value DOUBLE PRECISION NOT NULL,
    previous_value DOUBLE PRECISION,
    change_percentage DOUBLE PRECISION,
    trend VARCHAR(10), -- 'up', 'down', 'stable'
    unit VARCHAR(20),
    time_period VARCHAR(20) NOT NULL, -- 'hourly', 'daily', 'weekly', 'monthly'
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Table for telephony metrics
CREATE TABLE metrics.telephony_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(50) NOT NULL, -- 'calls_processed', 'avg_duration', 'automation_rate', etc.
    metric_value DOUBLE PRECISION NOT NULL,
    previous_value DOUBLE PRECISION,
    change_percentage DOUBLE PRECISION,
    trend VARCHAR(10), -- 'up', 'down', 'stable'
    unit VARCHAR(20),
    time_period VARCHAR(20) NOT NULL, -- 'hourly', 'daily', 'weekly', 'monthly'
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Table for system health metrics
CREATE TABLE metrics.system_health (
    id SERIAL PRIMARY KEY,
    system_component VARCHAR(50) NOT NULL, -- 'api', 'database', 'external_connection', etc.
    metric_name VARCHAR(50) NOT NULL, -- 'error_rate', 'response_time', 'cpu_usage', etc.
    metric_value DOUBLE PRECISION NOT NULL,
    previous_value DOUBLE PRECISION,
    change_percentage DOUBLE PRECISION,
    trend VARCHAR(10), -- 'up', 'down', 'stable'
    status VARCHAR(20), -- 'healthy', 'warning', 'critical'
    unit VARCHAR(20),
    time_period VARCHAR(20) NOT NULL, -- 'hourly', 'daily', 'weekly', 'monthly'
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Table for metrics aggregations
CREATE TABLE metrics.aggregated_metrics (
    id SERIAL PRIMARY KEY,
    metric_category VARCHAR(50) NOT NULL, -- 'business', 'ai', 'telephony', 'system'
    metric_name VARCHAR(50) NOT NULL,
    aggregation_type VARCHAR(20) NOT NULL, -- 'avg', 'sum', 'min', 'max', 'count'
    metric_value DOUBLE PRECISION NOT NULL,
    unit VARCHAR(20),
    time_period VARCHAR(20) NOT NULL, -- 'hourly', 'daily', 'weekly', 'monthly'
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- Table for real-time metrics (shorter retention)
CREATE TABLE metrics.realtime_metrics (
    id SERIAL PRIMARY KEY,
    metric_category VARCHAR(50) NOT NULL, -- 'business', 'ai', 'telephony', 'system'
    metric_name VARCHAR(50) NOT NULL,
    metric_value DOUBLE PRECISION NOT NULL,
    unit VARCHAR(20),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ttl TIMESTAMP WITH TIME ZONE -- Time to live - for automatic cleanup
);

-- Table for storing dashboard view permissions
CREATE TABLE metrics.dashboard_permissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES public.users(id) ON DELETE CASCADE,
    role_id INTEGER REFERENCES public.roles(id) ON DELETE CASCADE,
    dashboard_section VARCHAR(50) NOT NULL, -- 'business', 'ai', 'telephony', 'system', 'all'
    can_view BOOLEAN DEFAULT FALSE,
    can_export BOOLEAN DEFAULT FALSE,
    can_configure BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT user_or_role_required CHECK (user_id IS NOT NULL OR role_id IS NOT NULL)
);

-- Create indexes for faster querying
CREATE INDEX idx_component_metrics_component ON metrics.component_metrics(component_name);
CREATE INDEX idx_component_metrics_timestamp ON metrics.component_metrics(timestamp);
CREATE INDEX idx_business_kpis_time_period ON metrics.business_kpis(time_period, start_time, end_time);
CREATE INDEX idx_ai_metrics_agent_id ON metrics.ai_metrics(agent_id);
CREATE INDEX idx_ai_metrics_time_period ON metrics.ai_metrics(time_period, start_time, end_time);
CREATE INDEX idx_telephony_metrics_time_period ON metrics.telephony_metrics(time_period, start_time, end_time);
CREATE INDEX idx_system_health_component ON metrics.system_health(system_component);
CREATE INDEX idx_system_health_time_period ON metrics.system_health(time_period, start_time, end_time);
CREATE INDEX idx_aggregated_metrics_category ON metrics.aggregated_metrics(metric_category, time_period);
CREATE INDEX idx_realtime_metrics_category ON metrics.realtime_metrics(metric_category);
CREATE INDEX idx_realtime_metrics_timestamp ON metrics.realtime_metrics(timestamp);
CREATE INDEX idx_dashboard_permissions_user ON metrics.dashboard_permissions(user_id, dashboard_section);
CREATE INDEX idx_dashboard_permissions_role ON metrics.dashboard_permissions(role_id, dashboard_section);

-- Add TTL index for automatic cleanup of real-time metrics
CREATE INDEX idx_realtime_metrics_ttl ON metrics.realtime_metrics(ttl) WHERE ttl IS NOT NULL;
