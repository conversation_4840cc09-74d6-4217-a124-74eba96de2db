-- Create tables for advanced analytics functionality
-- Migration: 20250308_create_analytics_tables.sql

-- Table for storing customer journeys
CREATE TABLE IF NOT EXISTS "CustomerJourneys" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "customerId" UUID NOT NULL REFERENCES "Customers"("id") ON DELETE CASCADE,
  "touchpoints" JSONB NOT NULL DEFAULT '[]',
  "currentStage" VARCHAR(255) NOT NULL DEFAULT 'awareness',
  "firstTouchAt" TIMESTAMP WITH TIME ZONE,
  "lastTouchAt" TIMESTAMP WITH TIME ZONE,
  "totalTouchpoints" INTEGER NOT NULL DEFAULT 0,
  "avgTimeBetweenTouchpoints" FLOAT,
  "attributes" JSONB NOT NULL DEFAULT '{}',
  "metrics" JSONB NOT NULL DEFAULT '{}',
  "isActive" BOOLEAN NOT NULL DEFAULT TRUE,
  "timeToConversion" FLOAT,
  "conversionValue" DECIMAL(15, 2),
  "conversionType" VARCHAR(255),
  "initialSource" VARCHAR(255),
  "channelDistribution" JSONB NOT NULL DEFAULT '{}',
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS "customer_journey_customer_id_idx" ON "CustomerJourneys"("customerId");
CREATE INDEX IF NOT EXISTS "customer_journey_stage_idx" ON "CustomerJourneys"("currentStage");
CREATE INDEX IF NOT EXISTS "customer_journey_active_idx" ON "CustomerJourneys"("isActive");
CREATE INDEX IF NOT EXISTS "customer_journey_conversion_type_idx" ON "CustomerJourneys"("conversionType");
CREATE INDEX IF NOT EXISTS "customer_journey_first_touch_idx" ON "CustomerJourneys"("firstTouchAt");

-- Update existing models to add journey references
ALTER TABLE "Interactions" 
ADD COLUMN IF NOT EXISTS "journeyId" UUID REFERENCES "CustomerJourneys"("id") ON DELETE SET NULL;

ALTER TABLE "Deals" 
ADD COLUMN IF NOT EXISTS "journeyId" UUID REFERENCES "CustomerJourneys"("id") ON DELETE SET NULL;

-- Table for storing ML prediction models
CREATE TABLE IF NOT EXISTS "PredictionModels" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" VARCHAR(255) NOT NULL,
  "type" VARCHAR(255) NOT NULL,
  "version" VARCHAR(50) NOT NULL,
  "modelPath" VARCHAR(255) NOT NULL,
  "metadata" JSONB NOT NULL DEFAULT '{}',
  "metrics" JSONB NOT NULL DEFAULT '{}',
  "parameters" JSONB NOT NULL DEFAULT '{}',
  "isActive" BOOLEAN NOT NULL DEFAULT TRUE,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS "prediction_model_type_idx" ON "PredictionModels"("type");
CREATE INDEX IF NOT EXISTS "prediction_model_active_idx" ON "PredictionModels"("isActive");

-- Table for storing forecasts
CREATE TABLE IF NOT EXISTS "Forecasts" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "modelId" UUID NOT NULL REFERENCES "PredictionModels"("id") ON DELETE CASCADE,
  "targetType" VARCHAR(255) NOT NULL,
  "targetId" UUID,
  "startDate" TIMESTAMP WITH TIME ZONE NOT NULL,
  "endDate" TIMESTAMP WITH TIME ZONE NOT NULL,
  "forecastPeriods" INTEGER NOT NULL,
  "forecastData" JSONB NOT NULL,
  "confidence" JSONB,
  "accuracy" FLOAT,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS "forecast_model_id_idx" ON "Forecasts"("modelId");
CREATE INDEX IF NOT EXISTS "forecast_target_type_idx" ON "Forecasts"("targetType");
CREATE INDEX IF NOT EXISTS "forecast_date_range_idx" ON "Forecasts"("startDate", "endDate");

-- Table for storing detected anomalies
CREATE TABLE IF NOT EXISTS "Anomalies" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "sourceType" VARCHAR(255) NOT NULL,
  "sourceId" UUID,
  "detectionMethod" VARCHAR(255) NOT NULL,
  "anomalyType" VARCHAR(255) NOT NULL,
  "severity" FLOAT NOT NULL,
  "score" FLOAT NOT NULL,
  "threshold" FLOAT NOT NULL,
  "detectedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "message" TEXT,
  "context" JSONB NOT NULL DEFAULT '{}',
  "isResolved" BOOLEAN NOT NULL DEFAULT FALSE,
  "resolvedAt" TIMESTAMP WITH TIME ZONE,
  "resolution" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS "anomaly_source_idx" ON "Anomalies"("sourceType", "sourceId");
CREATE INDEX IF NOT EXISTS "anomaly_type_idx" ON "Anomalies"("anomalyType");
CREATE INDEX IF NOT EXISTS "anomaly_severity_idx" ON "Anomalies"("severity");
CREATE INDEX IF NOT EXISTS "anomaly_detection_idx" ON "Anomalies"("detectedAt");
CREATE INDEX IF NOT EXISTS "anomaly_resolution_idx" ON "Anomalies"("isResolved");

-- Table for storing OLAP/BI cube definitions
CREATE TABLE IF NOT EXISTS "DataCubes" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "dimensions" JSONB NOT NULL,
  "measures" JSONB NOT NULL,
  "refreshSchedule" VARCHAR(255),
  "lastRefreshed" TIMESTAMP WITH TIME ZONE,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Table for storing dashboard configurations
CREATE TABLE IF NOT EXISTS "AnalyticsDashboards" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "layout" JSONB NOT NULL DEFAULT '[]',
  "widgets" JSONB NOT NULL DEFAULT '[]',
  "userId" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  "isPublic" BOOLEAN NOT NULL DEFAULT FALSE,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS "analytics_dashboard_user_idx" ON "AnalyticsDashboards"("userId");
CREATE INDEX IF NOT EXISTS "analytics_dashboard_public_idx" ON "AnalyticsDashboards"("isPublic");

-- Table for exported reports
CREATE TABLE IF NOT EXISTS "AnalyticsReports" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "format" VARCHAR(50) NOT NULL,
  "parameters" JSONB NOT NULL DEFAULT '{}',
  "filePath" VARCHAR(255),
  "userId" UUID REFERENCES "Users"("id") ON DELETE SET NULL,
  "scheduleExpression" VARCHAR(255),
  "lastRun" TIMESTAMP WITH TIME ZONE,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS "analytics_report_user_idx" ON "AnalyticsReports"("userId");
CREATE INDEX IF NOT EXISTS "analytics_report_format_idx" ON "AnalyticsReports"("format");
