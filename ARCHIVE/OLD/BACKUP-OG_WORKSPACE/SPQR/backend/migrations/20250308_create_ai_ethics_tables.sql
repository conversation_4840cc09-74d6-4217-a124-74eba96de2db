-- AI Ethics and Compliance Module Tables
-- Migration script for AI decision auditing, compliance management, and bias detection

-- Table for storing AI decision logs with detailed metadata
CREATE TABLE IF NOT EXISTS "AIDecisionLogs" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" UUID,
  "actionType" VARCHAR(255) NOT NULL,
  "contextData" JSONB NOT NULL,
  "decision" TEXT NOT NULL,
  "explanation" TEXT,
  "confidenceScore" FLOAT,
  "modelId" VARCHAR(255) NOT NULL,
  "modelVersion" VARCHAR(255) NOT NULL,
  "executionTimeMs" INTEGER,
  "tokenCount" INTEGER,
  "inputHash" VARCHAR(255),
  "biasAssessment" JSONB,
  "complianceFlags" JSONB,
  "sensitiveDataAccessed" BOOLEAN NOT NULL DEFAULT FALSE,
  "relatedEntityType" VARCHAR(255),
  "relatedEntityId" UUID,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for AIDecisionLogs
CREATE INDEX IF NOT EXISTS "idx_ai_decision_logs_user_id" ON "AIDecisionLogs" ("userId");
CREATE INDEX IF NOT EXISTS "idx_ai_decision_logs_action_type" ON "AIDecisionLogs" ("actionType");
CREATE INDEX IF NOT EXISTS "idx_ai_decision_logs_model_id" ON "AIDecisionLogs" ("modelId");
CREATE INDEX IF NOT EXISTS "idx_ai_decision_logs_created_at" ON "AIDecisionLogs" ("createdAt");
CREATE INDEX IF NOT EXISTS "idx_ai_decision_logs_related_entity" ON "AIDecisionLogs" ("relatedEntityType", "relatedEntityId");

-- Table for storing compliance policies and regulatory requirements
CREATE TABLE IF NOT EXISTS "CompliancePolicies" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "policyType" VARCHAR(50) NOT NULL,
  "regulationReference" VARCHAR(255),
  "status" VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
  "version" VARCHAR(50) NOT NULL DEFAULT '1.0',
  "effectiveDate" TIMESTAMP WITH TIME ZONE,
  "reviewDate" TIMESTAMP WITH TIME ZONE,
  "scope" JSONB,
  "requirements" JSONB NOT NULL,
  "validationRules" JSONB,
  "remediationSteps" TEXT,
  "ownerUserId" UUID,
  "documentationUrl" VARCHAR(255),
  "isTemplated" BOOLEAN NOT NULL DEFAULT FALSE,
  "templateMetadata" JSONB,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for CompliancePolicies
CREATE INDEX IF NOT EXISTS "idx_compliance_policies_policy_type" ON "CompliancePolicies" ("policyType");
CREATE INDEX IF NOT EXISTS "idx_compliance_policies_status" ON "CompliancePolicies" ("status");
CREATE INDEX IF NOT EXISTS "idx_compliance_policies_effective_date" ON "CompliancePolicies" ("effectiveDate");
CREATE INDEX IF NOT EXISTS "idx_compliance_policies_owner" ON "CompliancePolicies" ("ownerUserId");

-- Table for tracking user opt-in/opt-out preferences for AI features
CREATE TABLE IF NOT EXISTS "UserAIPreferences" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" UUID NOT NULL,
  "aiFeatureEnabled" BOOLEAN NOT NULL DEFAULT TRUE,
  "dataSharingEnabled" BOOLEAN NOT NULL DEFAULT TRUE,
  "modelPersonalizationEnabled" BOOLEAN NOT NULL DEFAULT TRUE,
  "sensitivityLevel" VARCHAR(50) NOT NULL DEFAULT 'MEDIUM',
  "explainabilityLevel" VARCHAR(50) NOT NULL DEFAULT 'STANDARD',
  "categories" JSONB,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  CONSTRAINT "fk_user_ai_preferences_user" FOREIGN KEY ("userId") REFERENCES "Users" ("id") ON DELETE CASCADE
);

CREATE UNIQUE INDEX IF NOT EXISTS "idx_user_ai_preferences_user_id" ON "UserAIPreferences" ("userId");

-- Table for tracking data usage and access for transparency
CREATE TABLE IF NOT EXISTS "DataUsageRecords" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "userId" UUID,
  "actorId" UUID NOT NULL,
  "actorType" VARCHAR(50) NOT NULL,
  "dataCategory" VARCHAR(100) NOT NULL,
  "purpose" VARCHAR(255) NOT NULL,
  "accessType" VARCHAR(50) NOT NULL,
  "sensitivityLevel" VARCHAR(50) NOT NULL DEFAULT 'MEDIUM',
  "dataReference" JSONB NOT NULL,
  "retentionPeriod" INTEGER,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "expiresAt" TIMESTAMP WITH TIME ZONE
);

CREATE INDEX IF NOT EXISTS "idx_data_usage_records_user_id" ON "DataUsageRecords" ("userId");
CREATE INDEX IF NOT EXISTS "idx_data_usage_records_data_category" ON "DataUsageRecords" ("dataCategory");
CREATE INDEX IF NOT EXISTS "idx_data_usage_records_created_at" ON "DataUsageRecords" ("createdAt");
CREATE INDEX IF NOT EXISTS "idx_data_usage_records_actor" ON "DataUsageRecords" ("actorId", "actorType");

-- Function to automatically update the updatedAt timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to update the updatedAt column automatically
CREATE TRIGGER update_ai_decision_logs_updated_at
BEFORE UPDATE ON "AIDecisionLogs"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_compliance_policies_updated_at
BEFORE UPDATE ON "CompliancePolicies"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_ai_preferences_updated_at
BEFORE UPDATE ON "UserAIPreferences"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
