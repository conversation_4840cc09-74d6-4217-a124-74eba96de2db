-- Telephony Integration Tables Migration
-- This migration creates the necessary tables for the VAPI.ai telephony integration

-- Phone Numbers Table
-- Stores registered phone numbers for the system
CREATE TABLE IF NOT EXISTS phone_numbers (
  id SERIAL PRIMARY KEY,
  number VARCHAR(20) NOT NULL UNIQUE,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  provider VARCHAR(50) DEFAULT 'vapi',
  capabilities JSONB DEFAULT '{"voice": true, "sms": false}'::JSONB,
  configuration JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Calls Table
-- Records all call interactions through the system
CREATE TABLE IF NOT EXISTS calls (
  id SERIAL PRIMARY KEY,
  call_id VARCHAR(255) NOT NULL UNIQUE, -- External call ID from VAPI
  direction VARCHAR(10) NOT NULL, -- 'inbound' or 'outbound'
  phone_number_id INTEGER REFERENCES phone_numbers(id),
  from_number VARCHAR(20) NOT NULL,
  to_number VA<PERSON>HAR(20) NOT NULL,
  status VARCHAR(20) NOT NULL, -- 'initiated', 'in-progress', 'completed', 'failed', etc.
  duration_seconds INTEGER DEFAULT 0,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  recording_url TEXT,
  transcription_available BOOLEAN DEFAULT FALSE,
  metadata JSONB,
  customer_id INTEGER REFERENCES customers(id),
  user_id INTEGER REFERENCES users(id), -- Staff member involved
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Call Transcripts Table
-- Stores detailed transcription of calls
CREATE TABLE IF NOT EXISTS call_transcripts (
  id SERIAL PRIMARY KEY,
  call_id INTEGER REFERENCES calls(id) ON DELETE CASCADE,
  full_transcript TEXT,
  segments JSONB, -- Array of transcript segments with timestamps
  speaker_labels BOOLEAN DEFAULT FALSE, -- Whether speaker diarization was applied
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Call Events Table
-- Detailed event log for each call
CREATE TABLE IF NOT EXISTS call_events (
  id SERIAL PRIMARY KEY,
  call_id INTEGER REFERENCES calls(id) ON DELETE CASCADE,
  event_type VARCHAR(50) NOT NULL, -- 'started', 'answered', 'transcription', 'end', etc.
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  payload JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Call Insights Table
-- Extracted insights and analytics from calls
CREATE TABLE IF NOT EXISTS call_insights (
  id SERIAL PRIMARY KEY,
  call_id INTEGER REFERENCES calls(id) ON DELETE CASCADE,
  insight_type VARCHAR(50) NOT NULL, -- 'sentiment', 'action_item', 'question', etc.
  content TEXT NOT NULL,
  confidence FLOAT,
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE, -- Whether this insight has been acted upon
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Call Transfers Table
-- Tracks call transfers between AI and human agents
CREATE TABLE IF NOT EXISTS call_transfers (
  id SERIAL PRIMARY KEY,
  call_id INTEGER REFERENCES calls(id) ON DELETE CASCADE,
  from_type VARCHAR(20) NOT NULL, -- 'ai' or 'human'
  to_type VARCHAR(20) NOT NULL, -- 'ai' or 'human'
  user_id INTEGER REFERENCES users(id), -- Human agent if applicable
  status VARCHAR(20) NOT NULL, -- 'requested', 'accepted', 'rejected', 'completed', 'failed'
  reason TEXT,
  transfer_start TIMESTAMP WITH TIME ZONE,
  transfer_end TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Two-Stage Call Connections Table
-- Manages the two-stage call connection process
CREATE TABLE IF NOT EXISTS two_stage_connections (
  id SERIAL PRIMARY KEY,
  original_call_id INTEGER REFERENCES calls(id),
  target_user_id INTEGER REFERENCES users(id),
  target_number VARCHAR(20) NOT NULL,
  status VARCHAR(20) NOT NULL, -- 'pending', 'connected', 'failed', 'rejected', etc.
  connection_call_id VARCHAR(255), -- VAPI call ID for second leg
  connection_time TIMESTAMP WITH TIME ZONE,
  disconnect_time TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Telephony Settings Table
-- System-wide and user-specific telephony settings
CREATE TABLE IF NOT EXISTS telephony_settings (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id), -- NULL for system-wide settings
  setting_type VARCHAR(50) NOT NULL, -- 'greeting', 'voicemail', 'transfer_rules', etc.
  settings JSONB NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE (user_id, setting_type)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_calls_customer_id ON calls(customer_id);
CREATE INDEX IF NOT EXISTS idx_calls_user_id ON calls(user_id);
CREATE INDEX IF NOT EXISTS idx_calls_status ON calls(status);
CREATE INDEX IF NOT EXISTS idx_call_events_call_id ON call_events(call_id);
CREATE INDEX IF NOT EXISTS idx_call_events_event_type ON call_events(event_type);
CREATE INDEX IF NOT EXISTS idx_call_insights_call_id ON call_insights(call_id);
CREATE INDEX IF NOT EXISTS idx_call_insights_type ON call_insights(insight_type);
CREATE INDEX IF NOT EXISTS idx_call_transfers_call_id ON call_transfers(call_id);
CREATE INDEX IF NOT EXISTS idx_two_stage_connections_call_id ON two_stage_connections(original_call_id);
CREATE INDEX IF NOT EXISTS idx_two_stage_connections_user_id ON two_stage_connections(target_user_id);
CREATE INDEX IF NOT EXISTS idx_telephony_settings_user_id ON telephony_settings(user_id);
