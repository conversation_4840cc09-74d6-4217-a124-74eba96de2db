-- Migration: Create Connection System Tables
-- This migration adds tables for the Connection Agent system

-- Connection Registry Table
CREATE TABLE IF NOT EXISTS "Connections" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "type" VARCHAR(50) NOT NULL CHECK ("type" IN ('API', 'UI_AUTOMATION', 'DATABASE', 'WEBHOOK', 'CUSTOM')),
  "status" VARCHAR(50) NOT NULL DEFAULT 'active' CHECK ("status" IN ('active', 'inactive', 'testing', 'error')),
  "systemUrl" VARCHAR(255),
  "documentationUrl" VARCHAR(255),
  "apiBaseUrl" VARCHAR(255),
  "apiVersion" VARCHAR(50),
  "authType" VARCHAR(50) CHECK ("authType" IN ('none', 'api_key', 'oauth2', 'basic', 'bearer', 'custom')),
  "credentialsId" UUID,
  "icon" TEXT,
  "metadata" JSONB DEFAULT '{}',
  "config" JSONB NOT NULL DEFAULT '{}',
  "schema" JSONB,
  "createdBy" UUID REFERENCES "Users"("id"),
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "lastTestedAt" TIMESTAMP WITH TIME ZONE,
  CONSTRAINT "unique_connection_name" UNIQUE ("name")
);

-- Connection Credentials (encrypted)
CREATE TABLE IF NOT EXISTS "ConnectionCredentials" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "connectionId" UUID NOT NULL REFERENCES "Connections"("id") ON DELETE CASCADE,
  "name" VARCHAR(255) NOT NULL,
  "type" VARCHAR(50) NOT NULL CHECK ("type" IN ('api_key', 'oauth2', 'basic', 'bearer', 'certificate', 'custom')),
  "encryptedData" TEXT NOT NULL,
  "isDefault" BOOLEAN DEFAULT FALSE,
  "expiresAt" TIMESTAMP WITH TIME ZONE,
  "metadata" JSONB DEFAULT '{}',
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "unique_default_credential" UNIQUE ("connectionId", "isDefault") WHERE ("isDefault" = TRUE)
);

-- Connection Endpoints
CREATE TABLE IF NOT EXISTS "ConnectionEndpoints" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "connectionId" UUID NOT NULL REFERENCES "Connections"("id") ON DELETE CASCADE,
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "path" VARCHAR(255) NOT NULL,
  "method" VARCHAR(20) NOT NULL CHECK ("method" IN ('GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD')),
  "requestSchema" JSONB,
  "responseSchema" JSONB,
  "parameters" JSONB DEFAULT '{}',
  "headers" JSONB DEFAULT '{}',
  "requiresAuth" BOOLEAN DEFAULT TRUE,
  "rateLimit" INTEGER,
  "cacheTtl" INTEGER,
  "status" VARCHAR(50) DEFAULT 'active' CHECK ("status" IN ('active', 'inactive', 'deprecated', 'testing')),
  "metadata" JSONB DEFAULT '{}',
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "unique_endpoint_path_method" UNIQUE ("connectionId", "path", "method")
);

-- UI Automation Steps
CREATE TABLE IF NOT EXISTS "UiAutomationWorkflows" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "connectionId" UUID NOT NULL REFERENCES "Connections"("id") ON DELETE CASCADE,
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "entryUrl" VARCHAR(255) NOT NULL,
  "steps" JSONB NOT NULL DEFAULT '[]',
  "parameters" JSONB DEFAULT '{}',
  "expectedOutput" JSONB,
  "status" VARCHAR(50) DEFAULT 'active' CHECK ("status" IN ('active', 'inactive', 'testing')),
  "metadata" JSONB DEFAULT '{}',
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Connection Tools (Generated from connections)
CREATE TABLE IF NOT EXISTS "ConnectionTools" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "connectionId" UUID NOT NULL REFERENCES "Connections"("id") ON DELETE CASCADE,
  "endpointId" UUID REFERENCES "ConnectionEndpoints"("id") ON DELETE CASCADE,
  "workflowId" UUID REFERENCES "UiAutomationWorkflows"("id") ON DELETE CASCADE,
  "toolId" UUID REFERENCES "Tools"("id") ON DELETE CASCADE,
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "action" VARCHAR(255) NOT NULL,
  "parameters" JSONB DEFAULT '{}',
  "outputSchema" JSONB,
  "status" VARCHAR(50) DEFAULT 'active' CHECK ("status" IN ('active', 'inactive', 'testing')),
  "metadata" JSONB DEFAULT '{}',
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "unique_tool_name" UNIQUE ("name"),
  -- Either endpointId or workflowId must be set, but not both
  CONSTRAINT "endpoint_or_workflow" CHECK (
    (("endpointId" IS NOT NULL)::INTEGER + ("workflowId" IS NOT NULL)::INTEGER) = 1
  )
);

-- Connection Executions
CREATE TABLE IF NOT EXISTS "ConnectionExecutions" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "connectionId" UUID NOT NULL REFERENCES "Connections"("id"),
  "endpointId" UUID REFERENCES "ConnectionEndpoints"("id"),
  "workflowId" UUID REFERENCES "UiAutomationWorkflows"("id"),
  "toolId" UUID REFERENCES "ConnectionTools"("id"),
  "requestPayload" JSONB,
  "responsePayload" JSONB,
  "startTime" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "endTime" TIMESTAMP WITH TIME ZONE,
  "duration" INTEGER,
  "status" VARCHAR(50) NOT NULL CHECK ("status" IN ('pending', 'success', 'error', 'timeout')),
  "errorMessage" TEXT,
  "userId" UUID REFERENCES "Users"("id"),
  "agentId" UUID REFERENCES "SpecializedAgent"("id"),
  "conversationId" UUID,
  "metadata" JSONB DEFAULT '{}',
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Connection Agent Registry
INSERT INTO "SpecializedAgent" (
  "id", "name", "agentType", "description", "version", "systemPrompt", "capabilities", "parameters", "status", "createdAt", "updatedAt"
) VALUES (
  uuid_generate_v4(), 
  'Connection Agent', 
  'connection_agent', 
  'Specialized agent for researching, creating, and managing external system connections',
  '1.0.0',
  'You are a Connection Agent specialized in integrating external systems with the CRM platform. Your expertise includes researching API documentation, creating reliable connections to external systems, designing UI automation workflows, and generating tools that other agents can use. Approach each task methodically, with security and reliability as your top priorities.',
  ARRAY['api_integration', 'ui_automation', 'system_connection', 'tool_generation'],
  '{
    "enableRag": true,
    "ragMaxResults": 5,
    "enableWebBrowsing": true,
    "claude": {
      "model": "claude-3-opus-20240229",
      "temperature": 0.2,
      "max_tokens": 4000
    }
  }',
  'active',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS "idx_connections_type" ON "Connections"("type");
CREATE INDEX IF NOT EXISTS "idx_connections_status" ON "Connections"("status");
CREATE INDEX IF NOT EXISTS "idx_connection_credentials_connection_id" ON "ConnectionCredentials"("connectionId");
CREATE INDEX IF NOT EXISTS "idx_connection_endpoints_connection_id" ON "ConnectionEndpoints"("connectionId");
CREATE INDEX IF NOT EXISTS "idx_ui_workflows_connection_id" ON "UiAutomationWorkflows"("connectionId");
CREATE INDEX IF NOT EXISTS "idx_connection_tools_connection_id" ON "ConnectionTools"("connectionId");
CREATE INDEX IF NOT EXISTS "idx_connection_tools_tool_id" ON "ConnectionTools"("toolId");
CREATE INDEX IF NOT EXISTS "idx_connection_executions_connection_id" ON "ConnectionExecutions"("connectionId");
CREATE INDEX IF NOT EXISTS "idx_connection_executions_status" ON "ConnectionExecutions"("status");
