-- Create webhook tables for direct webhook integration

-- Webhooks table
CREATE TABLE IF NOT EXISTS webhooks (
    id UUID PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    url TEXT NOT NULL,
    event VARCHAR(255) NOT NULL,
    headers J<PERSON><PERSON><PERSON> DEFAULT '{}'::<PERSON><PERSON><PERSON><PERSON>,
    config J<PERSON><PERSON><PERSON> DEFAULT '{}'::<PERSON><PERSON><PERSON><PERSON>,
    secret VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_webhooks_event ON webhooks(event);

-- Webhook executions table
CREATE TABLE IF NOT EXISTS webhook_executions (
    id UUID PRIMARY KEY,
    webhook_id UUID NOT NULL REFERENCES webhooks(id) ON DELETE CASCADE,
    request JSONB NOT NULL,
    response JSON<PERSON>,
    success BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_webhook_executions_webhook_id ON webhook_executions(webhook_id);
CREATE INDEX IF NOT EXISTS idx_webhook_executions_created_at ON webhook_executions(created_at);
CREATE INDEX IF NOT EXISTS idx_webhook_executions_success ON webhook_executions(success);

-- Webhook delivery table
CREATE TABLE IF NOT EXISTS webhook_deliveries (
    id UUID PRIMARY KEY,
    webhook_id UUID NOT NULL REFERENCES webhooks(id) ON DELETE CASCADE,
    execution_id UUID REFERENCES webhook_executions(id) ON DELETE SET NULL,
    status VARCHAR(50) NOT NULL, -- 'pending', 'delivered', 'failed'
    attempts INTEGER DEFAULT 0,
    next_attempt_at TIMESTAMP WITH TIME ZONE,
    last_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_webhook_id ON webhook_deliveries(webhook_id);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_status ON webhook_deliveries(status);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_next_attempt_at ON webhook_deliveries(next_attempt_at);

-- VAPI webhook tools table
CREATE TABLE IF NOT EXISTS vapi_webhook_tools (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    schema JSONB NOT NULL,
    handler VARCHAR(255) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_vapi_webhook_tools_name ON vapi_webhook_tools(name);
CREATE INDEX IF NOT EXISTS idx_vapi_webhook_tools_enabled ON vapi_webhook_tools(enabled);

-- VAPI webhook executions table
CREATE TABLE IF NOT EXISTS vapi_webhook_executions (
    id UUID PRIMARY KEY,
    tool_id UUID NOT NULL REFERENCES vapi_webhook_tools(id) ON DELETE CASCADE,
    request JSONB NOT NULL,
    response JSONB,
    success BOOLEAN NOT NULL,
    error TEXT,
    execution_time INTEGER, -- in milliseconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_vapi_webhook_executions_tool_id ON vapi_webhook_executions(tool_id);
CREATE INDEX IF NOT EXISTS idx_vapi_webhook_executions_created_at ON vapi_webhook_executions(created_at);
CREATE INDEX IF NOT EXISTS idx_vapi_webhook_executions_success ON vapi_webhook_executions(success);

-- Function to update updated_at timestamp (if not already created in previous migrations)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
    CREATE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  END IF;
END
$$;

-- Triggers to update updated_at timestamp
CREATE TRIGGER update_webhooks_updated_at
BEFORE UPDATE ON webhooks
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_webhook_deliveries_updated_at
BEFORE UPDATE ON webhook_deliveries
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vapi_webhook_tools_updated_at
BEFORE UPDATE ON vapi_webhook_tools
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Insert demo webhook for testing
INSERT INTO webhooks (id, name, url, event, headers, config, secret)
VALUES (
    'e47ac10b-58cc-4372-a567-0e02b2c3d478',
    'Demo Event Webhook',
    'http://localhost:3200/api/events/demo',
    'demo_event',
    '{"Content-Type": "application/json"}'::JSONB,
    '{"retry_count": 3, "retry_delay": 1000}'::JSONB,
    '1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef'
);

-- Insert demo VAPI webhook tool
INSERT INTO vapi_webhook_tools (id, name, description, schema, handler, enabled)
VALUES (
    'd47ac10b-58cc-4372-a567-0e02b2c3d477',
    'get_weather',
    'Get weather for a specified location',
    '{
        "type": "object",
        "properties": {
            "location": {
                "type": "string",
                "description": "City name or geographic location"
            },
            "units": {
                "type": "string",
                "enum": ["metric", "imperial"],
                "default": "metric",
                "description": "Temperature units (metric or imperial)"
            }
        },
        "required": ["location"]
    }'::JSONB,
    'weather/getWeather',
    TRUE
);
