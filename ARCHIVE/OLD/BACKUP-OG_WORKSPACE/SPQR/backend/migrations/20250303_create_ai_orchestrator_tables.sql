-- Migration for AI Orchestrator System Tables

-- Create Tool table
CREATE TABLE IF NOT EXISTS "Tools" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" VARCHAR(255) NOT NULL UNIQUE,
  "description" TEXT NOT NULL,
  "category" VARCHAR(50) NOT NULL CHECK (category IN ('data_retrieval', 'communication', 'integration', 'analysis', 'action', 'custom')),
  "endpoint" VARCHAR(512),
  "method" VARCHAR(10) NOT NULL CHECK (method IN ('GET', 'POST', 'PUT', 'DELETE', 'FUNCTION')),
  "parameters" JSONB DEFAULT '{}',
  "requestTemplate" TEXT,
  "responseTemplate" TEXT,
  "function" VARCHAR(255),
  "isActive" BOOLEAN DEFAULT TRUE,
  "securityLevel" VARCHAR(20) NOT NULL CHECK (securityLevel IN ('public', 'protected', 'admin')),
  "throttlingLimit" INTEGER DEFAULT 100,
  "timeoutMs" INTEGER DEFAULT 30000,
  "requiresConfirmation" BOOLEAN DEFAULT FALSE,
  "allowedAgents" UUID[] DEFAULT '{}',
  "executionCount" INTEGER DEFAULT 0,
  "successRate" FLOAT DEFAULT 0,
  "createdBy" UUID REFERENCES "Users"(id),
  "lastUpdated" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ToolExecution table
CREATE TABLE IF NOT EXISTS "ToolExecutions" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "toolId" UUID NOT NULL REFERENCES "Tools"(id) ON DELETE CASCADE,
  "agentId" UUID REFERENCES "AgentConfigs"(id) ON DELETE SET NULL,
  "userId" UUID REFERENCES "Users"(id) ON DELETE SET NULL,
  "conversationId" VARCHAR(255),
  "parameters" JSONB DEFAULT '{}',
  "result" JSONB,
  "status" VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'success', 'failed', 'timeout', 'unauthorized')),
  "errorMessage" TEXT,
  "startTime" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "endTime" TIMESTAMP WITH TIME ZONE,
  "executionTimeMs" INTEGER,
  "contextBefore" JSONB,
  "contextAfter" JSONB,
  "userFeedback" VARCHAR(20) DEFAULT 'none' CHECK (userFeedback IN ('helpful', 'not_helpful', 'incorrect', 'harmful', 'none')),
  "metadata" JSONB DEFAULT '{}',
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on tool execution status
CREATE INDEX IF NOT EXISTS "idx_tool_execution_status" ON "ToolExecutions"("status");

-- Create index on tool execution tool ID
CREATE INDEX IF NOT EXISTS "idx_tool_execution_tool_id" ON "ToolExecutions"("toolId");

-- Create index on conversationId
CREATE INDEX IF NOT EXISTS "idx_tool_execution_conversation_id" ON "ToolExecutions"("conversationId");

-- Create join table for AgentConfig and Tool (many-to-many)
CREATE TABLE IF NOT EXISTS "AgentTools" (
  "agentId" UUID NOT NULL REFERENCES "AgentConfigs"(id) ON DELETE CASCADE,
  "toolId" UUID NOT NULL REFERENCES "Tools"(id) ON DELETE CASCADE,
  "isDefault" BOOLEAN DEFAULT FALSE,
  "priority" INTEGER DEFAULT 0,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY ("agentId", "toolId")
);

-- Update Event model to add a new source type for tool executions
ALTER TABLE "Events"
ADD CONSTRAINT "check_source_type" 
CHECK (
  "sourceType" IN (
    'user', 
    'system', 
    'customer', 
    'deal', 
    'interaction', 
    'task', 
    'notification', 
    'tool_execution', 
    'agent', 
    'webhook'
  )
);

-- Add Claude model option to AgentConfig
ALTER TABLE "AgentConfigs" 
ALTER COLUMN "aiModel" TYPE VARCHAR(50);

-- Update existing agents to set proper values
UPDATE "AgentConfigs" 
SET "aiModel" = 'gpt-4' 
WHERE "aiModel" IS NULL;

COMMENT ON TABLE "Tools" IS 'Defines tools that can be executed by AI agents';
COMMENT ON TABLE "ToolExecutions" IS 'Records of tool executions and their results';
COMMENT ON TABLE "AgentTools" IS 'Many-to-many relationship between agents and tools';
