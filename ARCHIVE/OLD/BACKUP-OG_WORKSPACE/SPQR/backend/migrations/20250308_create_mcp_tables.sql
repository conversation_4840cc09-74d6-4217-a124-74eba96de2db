-- Migration: 20250308_create_mcp_tables.sql
-- Erstellt Mission Control Panel (MCP) Tabellen

-- MCP Konfigurationen
CREATE TABLE IF NOT EXISTS mcp_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  config_data JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MCP System Status Metriken
CREATE TABLE IF NOT EXISTS mcp_system_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  component_name VARCHAR(100) NOT NULL,
  status VARCHAR(50) NOT NULL, -- online, offline, degraded, etc.
  response_time FLOAT,
  error_rate FLOAT,
  cpu_usage FLOAT,
  memory_usage FLOAT,
  disk_usage FLOAT,
  additional_metrics JSONB,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MCP Agent Metriken
CREATE TABLE IF NOT EXISTS mcp_agent_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID REFERENCES agent_registry(id) ON DELETE CASCADE,
  execution_count INTEGER DEFAULT 0,
  success_rate FLOAT,
  average_response_time FLOAT,
  token_usage INTEGER,
  error_count INTEGER DEFAULT 0,
  last_execution_timestamp TIMESTAMP WITH TIME ZONE,
  status VARCHAR(50) NOT NULL, -- active, inactive, error
  additional_metrics JSONB,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MCP Event Queue Status
CREATE TABLE IF NOT EXISTS mcp_event_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  queue_name VARCHAR(100) NOT NULL,
  queue_size INTEGER DEFAULT 0,
  processing_rate FLOAT,
  oldest_event_timestamp TIMESTAMP WITH TIME ZONE,
  newest_event_timestamp TIMESTAMP WITH TIME ZONE,
  error_count INTEGER DEFAULT 0,
  status VARCHAR(50) NOT NULL, -- active, paused, error
  additional_metrics JSONB,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MCP Dashboard Berechtigungen
CREATE TABLE IF NOT EXISTS mcp_dashboard_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  can_view BOOLEAN DEFAULT false,
  can_edit BOOLEAN DEFAULT false,
  can_restart_services BOOLEAN DEFAULT false,
  can_configure_agents BOOLEAN DEFAULT false,
  can_manage_events BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- MCP Audit Log
CREATE TABLE IF NOT EXISTS mcp_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  action VARCHAR(255) NOT NULL,
  entity_type VARCHAR(100),
  entity_id UUID,
  details JSONB,
  ip_address VARCHAR(50),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MCP Kommandoverlauf
CREATE TABLE IF NOT EXISTS mcp_command_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  command_type VARCHAR(100) NOT NULL,
  command_data JSONB NOT NULL,
  result_status VARCHAR(50),
  result_data JSONB,
  execution_time FLOAT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indizes für schnelle Abfragen
CREATE INDEX IF NOT EXISTS idx_mcp_system_status_component ON mcp_system_status(component_name);
CREATE INDEX IF NOT EXISTS idx_mcp_system_status_timestamp ON mcp_system_status(timestamp);
CREATE INDEX IF NOT EXISTS idx_mcp_agent_metrics_agent_id ON mcp_agent_metrics(agent_id);
CREATE INDEX IF NOT EXISTS idx_mcp_agent_metrics_timestamp ON mcp_agent_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_mcp_event_queue_name ON mcp_event_queue(queue_name);
CREATE INDEX IF NOT EXISTS idx_mcp_event_queue_timestamp ON mcp_event_queue(timestamp);
CREATE INDEX IF NOT EXISTS idx_mcp_audit_log_user ON mcp_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_mcp_audit_log_timestamp ON mcp_audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_mcp_command_history_user ON mcp_command_history(user_id);
CREATE INDEX IF NOT EXISTS idx_mcp_command_history_timestamp ON mcp_command_history(timestamp);
