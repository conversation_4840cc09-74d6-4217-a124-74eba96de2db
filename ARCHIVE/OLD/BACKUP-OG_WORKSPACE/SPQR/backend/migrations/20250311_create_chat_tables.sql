-- Chat System Tables Migration
-- Version: 1.0
-- Date: 2025-03-11

-- Create chat_sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL,
  context JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_updated_at ON chat_sessions(updated_at);

-- Create chat_messages table
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID PRIMARY KEY,
  session_id UUID NOT NULL,
  user_id UUID NOT NULL,
  content TEXT NOT NULL,
  role VA<PERSON>HA<PERSON>(50) NOT NULL, -- 'user', 'assistant', 'system'
  context JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL,
  FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);

-- Create chat_entities table for entity references in chat
CREATE TABLE IF NOT EXISTS chat_entities (
  id UUID PRIMARY KEY,
  message_id UUID NOT NULL,
  entity_type VARCHAR(100) NOT NULL,
  entity_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL,
  FOREIGN KEY (message_id) REFERENCES chat_messages(id) ON DELETE CASCADE
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_chat_entities_message_id ON chat_entities(message_id);
CREATE INDEX IF NOT EXISTS idx_chat_entities_entity_type_entity_id ON chat_entities(entity_type, entity_id);

-- Create chat_actions table for tracking actions taken in chat
CREATE TABLE IF NOT EXISTS chat_actions (
  id UUID PRIMARY KEY,
  message_id UUID NOT NULL,
  action_type VARCHAR(100) NOT NULL,
  action_data JSONB DEFAULT '{}',
  status VARCHAR(50) NOT NULL DEFAULT 'pending', -- 'pending', 'completed', 'failed'
  result JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL,
  FOREIGN KEY (message_id) REFERENCES chat_messages(id) ON DELETE CASCADE
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_chat_actions_message_id ON chat_actions(message_id);
CREATE INDEX IF NOT EXISTS idx_chat_actions_action_type ON chat_actions(action_type);
CREATE INDEX IF NOT EXISTS idx_chat_actions_status ON chat_actions(status);

-- Add permissions for chat functionality
INSERT INTO permissions (id, name, description, created_at, updated_at)
VALUES 
  (gen_random_uuid(), 'chat:create', 'Create new chat sessions', NOW(), NOW()),
  (gen_random_uuid(), 'chat:read', 'Read chat sessions and messages', NOW(), NOW()),
  (gen_random_uuid(), 'chat:write', 'Send messages in chat sessions', NOW(), NOW()),
  (gen_random_uuid(), 'chat:delete', 'Delete chat sessions and messages', NOW(), NOW()),
  (gen_random_uuid(), 'chat:admin', 'Administer chat system', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- Add default role permissions for chat
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
  r.id, 
  p.id
FROM 
  roles r, 
  permissions p
WHERE 
  r.name = 'admin' AND 
  p.name IN ('chat:create', 'chat:read', 'chat:write', 'chat:delete', 'chat:admin')
ON CONFLICT (role_id, permission_id) DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id)
SELECT 
  r.id, 
  p.id
FROM 
  roles r, 
  permissions p
WHERE 
  r.name = 'user' AND 
  p.name IN ('chat:create', 'chat:read', 'chat:write')
ON CONFLICT (role_id, permission_id) DO NOTHING;
