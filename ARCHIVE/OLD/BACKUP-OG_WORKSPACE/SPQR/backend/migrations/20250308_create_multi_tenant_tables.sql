-- Migration for Multi-Tenant functionality
-- Create Tenant and TenantBranding tables and add tenantId to all relevant tables

-- Create Tenants table
CREATE TABLE IF NOT EXISTS "Tenants" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "slug" VARCHAR(255) NOT NULL UNIQUE,
  "domain" VARCHAR(255) UNIQUE,
  "isActive" BOOLEAN DEFAULT TRUE,
  "settings" JSONB DEFAULT '{}'::JSONB,
  "plan" VARCHAR(255) DEFAULT 'free',
  "maxUsers" INTEGER DEFAULT 5,
  "maxStorage" INTEGER DEFAULT 1000,
  "metadata" JSONB DEFAULT '{}'::J<PERSON><PERSON><PERSON>,
  "createdBy" UUID,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS "idx_tenant_slug" ON "Tenants"("slug");
CREATE INDEX IF NOT EXISTS "idx_tenant_domain" ON "Tenants"("domain");

-- Create TenantBranding table
CREATE TABLE IF NOT EXISTS "TenantBrandings" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "tenantId" UUID NOT NULL REFERENCES "Tenants"("id") ON DELETE CASCADE,
  "logoUrl" VARCHAR(255),
  "faviconUrl" VARCHAR(255),
  "primaryColor" VARCHAR(50) DEFAULT '#3498db',
  "secondaryColor" VARCHAR(50) DEFAULT '#2ecc71',
  "accentColor" VARCHAR(50) DEFAULT '#e74c3c',
  "fontPrimary" VARCHAR(100) DEFAULT 'Inter, sans-serif',
  "fontSecondary" VARCHAR(100) DEFAULT 'Roboto, sans-serif',
  "customCSS" TEXT,
  "loginBackgroundUrl" VARCHAR(255),
  "emailTemplate" JSONB DEFAULT '{}'::JSONB,
  "documentTemplate" JSONB DEFAULT '{}'::JSONB,
  "isActive" BOOLEAN DEFAULT TRUE,
  "createdBy" UUID,
  "updatedBy" UUID,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS "idx_tenant_branding_tenant" ON "TenantBrandings"("tenantId");

-- Create a default tenant for existing data
INSERT INTO "Tenants" (
  "name", 
  "slug", 
  "isActive", 
  "plan", 
  "maxUsers", 
  "maxStorage"
) VALUES (
  'Default Tenant', 
  'default', 
  TRUE, 
  'enterprise', 
  500, 
  100000
) ON CONFLICT DO NOTHING;

-- Get the default tenant ID for referencing
DO $$
DECLARE
  default_tenant_id UUID;
BEGIN
  SELECT id INTO default_tenant_id FROM "Tenants" WHERE slug = 'default' LIMIT 1;

  -- Create default branding for the default tenant
  INSERT INTO "TenantBrandings" ("tenantId") 
  VALUES (default_tenant_id)
  ON CONFLICT DO NOTHING;

  -- Add tenantId column to Users table
  BEGIN
    ALTER TABLE "Users" ADD COLUMN "tenantId" UUID REFERENCES "Tenants"("id");
  EXCEPTION
    WHEN duplicate_column THEN
      RAISE NOTICE 'tenantId column already exists in Users table';
  END;
  
  -- Add role 'superadmin' to User roles enum if it doesn't exist
  BEGIN
    ALTER TYPE "enum_Users_role" ADD VALUE 'superadmin' IF NOT EXISTS;
  EXCEPTION
    WHEN invalid_parameter_value THEN
      RAISE NOTICE 'Role superadmin already exists in enum_Users_role';
  END;

  -- Update Users to use default tenant
  UPDATE "Users" SET "tenantId" = default_tenant_id WHERE "tenantId" IS NULL;

  -- Add tenantId column to Customers table
  BEGIN
    ALTER TABLE "Customers" ADD COLUMN "tenantId" UUID REFERENCES "Tenants"("id");
  EXCEPTION
    WHEN duplicate_column THEN
      RAISE NOTICE 'tenantId column already exists in Customers table';
  END;

  -- Update Customers to use default tenant
  UPDATE "Customers" SET "tenantId" = default_tenant_id WHERE "tenantId" IS NULL;
  
  -- Add tenantId column to Deals table
  BEGIN
    ALTER TABLE "Deals" ADD COLUMN "tenantId" UUID REFERENCES "Tenants"("id");
  EXCEPTION
    WHEN duplicate_column THEN
      RAISE NOTICE 'tenantId column already exists in Deals table';
  END;

  -- Update Deals to use default tenant
  UPDATE "Deals" SET "tenantId" = default_tenant_id WHERE "tenantId" IS NULL;
  
  -- Add tenantId column to Interactions table
  BEGIN
    ALTER TABLE "Interactions" ADD COLUMN "tenantId" UUID REFERENCES "Tenants"("id");
  EXCEPTION
    WHEN duplicate_column THEN
      RAISE NOTICE 'tenantId column already exists in Interactions table';
  END;

  -- Update Interactions to use default tenant
  UPDATE "Interactions" SET "tenantId" = default_tenant_id WHERE "tenantId" IS NULL;
  
  -- Make tenantId column NOT NULL after setting default values
  BEGIN
    ALTER TABLE "Users" ALTER COLUMN "tenantId" SET NOT NULL;
  EXCEPTION
    WHEN others THEN
      RAISE NOTICE 'Could not set tenantId NOT NULL in Users. Some rows may still have NULL values.';
  END;

  BEGIN
    ALTER TABLE "Customers" ALTER COLUMN "tenantId" SET NOT NULL;
  EXCEPTION
    WHEN others THEN
      RAISE NOTICE 'Could not set tenantId NOT NULL in Customers. Some rows may still have NULL values.';
  END;

  BEGIN
    ALTER TABLE "Deals" ALTER COLUMN "tenantId" SET NOT NULL;
  EXCEPTION
    WHEN others THEN
      RAISE NOTICE 'Could not set tenantId NOT NULL in Deals. Some rows may still have NULL values.';
  END;

  BEGIN
    ALTER TABLE "Interactions" ALTER COLUMN "tenantId" SET NOT NULL;
  EXCEPTION
    WHEN others THEN
      RAISE NOTICE 'Could not set tenantId NOT NULL in Interactions. Some rows may still have NULL values.';
  END;

  -- Add indexes for tenant isolation
  CREATE INDEX IF NOT EXISTS "idx_users_tenant" ON "Users"("tenantId");
  CREATE INDEX IF NOT EXISTS "idx_customers_tenant" ON "Customers"("tenantId");
  CREATE INDEX IF NOT EXISTS "idx_deals_tenant" ON "Deals"("tenantId");
  CREATE INDEX IF NOT EXISTS "idx_interactions_tenant" ON "Interactions"("tenantId");

  -- Add tenantId column to other tables as needed
  -- Events, Notifications, etc.
  BEGIN
    ALTER TABLE "Events" ADD COLUMN "tenantId" UUID REFERENCES "Tenants"("id");
    UPDATE "Events" SET "tenantId" = default_tenant_id WHERE "tenantId" IS NULL;
    CREATE INDEX IF NOT EXISTS "idx_events_tenant" ON "Events"("tenantId");
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Events table does not exist, skipping';
    WHEN duplicate_column THEN
      RAISE NOTICE 'tenantId column already exists in Events table';
  END;

  BEGIN
    ALTER TABLE "Notifications" ADD COLUMN "tenantId" UUID REFERENCES "Tenants"("id");
    UPDATE "Notifications" SET "tenantId" = default_tenant_id WHERE "tenantId" IS NULL;
    CREATE INDEX IF NOT EXISTS "idx_notifications_tenant" ON "Notifications"("tenantId");
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Notifications table does not exist, skipping';
    WHEN duplicate_column THEN
      RAISE NOTICE 'tenantId column already exists in Notifications table';
  END;

  BEGIN
    ALTER TABLE "Webhooks" ADD COLUMN "tenantId" UUID REFERENCES "Tenants"("id");
    UPDATE "Webhooks" SET "tenantId" = default_tenant_id WHERE "tenantId" IS NULL;
    CREATE INDEX IF NOT EXISTS "idx_webhooks_tenant" ON "Webhooks"("tenantId");
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Webhooks table does not exist, skipping';
    WHEN duplicate_column THEN
      RAISE NOTICE 'tenantId column already exists in Webhooks table';
  END;
  
  BEGIN
    ALTER TABLE "AgentRegistry" ADD COLUMN "tenantId" UUID REFERENCES "Tenants"("id");
    UPDATE "AgentRegistry" SET "tenantId" = default_tenant_id WHERE "tenantId" IS NULL;
    CREATE INDEX IF NOT EXISTS "idx_agent_registry_tenant" ON "AgentRegistry"("tenantId");
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'AgentRegistry table does not exist, skipping';
    WHEN duplicate_column THEN
      RAISE NOTICE 'tenantId column already exists in AgentRegistry table';
  END;
  
  -- Add row-level security policies for tenant isolation
  -- This ensures that even if application code doesn't filter by tenant,
  -- database-level security prevents cross-tenant data access
  
  -- Enable Row-Level Security (RLS)
  ALTER TABLE "Users" ENABLE ROW LEVEL SECURITY;
  ALTER TABLE "Customers" ENABLE ROW LEVEL SECURITY;
  ALTER TABLE "Deals" ENABLE ROW LEVEL SECURITY;
  ALTER TABLE "Interactions" ENABLE ROW LEVEL SECURITY;
  
  -- Create policies for tenant isolation
  CREATE POLICY tenant_isolation_users ON "Users"
    USING ("tenantId" = current_setting('app.current_tenant_id', TRUE)::UUID);
    
  CREATE POLICY tenant_isolation_customers ON "Customers"
    USING ("tenantId" = current_setting('app.current_tenant_id', TRUE)::UUID);
    
  CREATE POLICY tenant_isolation_deals ON "Deals"
    USING ("tenantId" = current_setting('app.current_tenant_id', TRUE)::UUID);
    
  CREATE POLICY tenant_isolation_interactions ON "Interactions"
    USING ("tenantId" = current_setting('app.current_tenant_id', TRUE)::UUID);
    
  -- Create function to set tenant context
  CREATE OR REPLACE FUNCTION set_tenant_context(tenant_id UUID)
  RETURNS VOID AS $$
  BEGIN
    PERFORM set_config('app.current_tenant_id', tenant_id::TEXT, FALSE);
  END;
  $$ LANGUAGE plpgsql;
  
END $$;
