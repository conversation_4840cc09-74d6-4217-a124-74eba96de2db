/**
 * Push Notification Controller
 * 
 * Handles API endpoints related to push notifications management, 
 * including subscription management and notification delivery.
 */

const pushService = require('../services/pushService');
const { validateRequest } = require('../utils/validator');
const { asyncHandler } = require('../middleware/async');
const { ForbiddenError, NotFoundError } = require('../utils/errors');

/**
 * Get VAPID public key for push subscriptions
 * @route GET /api/push/vapid-public-key
 * @access Public
 */
const getVapidPublicKey = asyncHandler(async (req, res) => {
  const publicKey = pushService.getPublicVapidKey();
  
  if (!publicKey) {
    return res.status(503).json({
      success: false,
      message: 'Push notifications are not configured on this server'
    });
  }
  
  res.json({
    success: true,
    publicKey
  });
});

/**
 * Subscribe to push notifications
 * @route POST /api/push/subscriptions
 * @access Private
 */
const subscribe = asyncHandler(async (req, res) => {
  // Validate request
  const validationRules = {
    subscription: 'required|object',
    'subscription.endpoint': 'required|string',
    'subscription.keys': 'required|object',
    'subscription.keys.p256dh': 'required|string',
    'subscription.keys.auth': 'required|string'
  };
  
  validateRequest(req.body, validationRules);
  
  // Extract data
  const { subscription, deviceInfo } = req.body;
  const userId = req.user.id;
  
  // Save subscription
  const savedSubscription = await pushService.saveSubscription(userId, subscription, {
    deviceInfo,
    userAgent: req.headers['user-agent']
  });
  
  res.status(201).json({
    success: true,
    data: savedSubscription
  });
});

/**
 * Unsubscribe from push notifications
 * @route DELETE /api/push/subscriptions/:endpoint
 * @access Private
 */
const unsubscribe = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const endpoint = req.params.endpoint;
  
  // URL-encode the endpoint
  const decodedEndpoint = decodeURIComponent(endpoint);
  
  // Remove subscription
  const removed = await pushService.removeSubscription(userId, decodedEndpoint);
  
  if (!removed) {
    return res.status(404).json({
      success: false,
      message: 'Subscription not found'
    });
  }
  
  res.json({
    success: true,
    message: 'Subscription successfully removed'
  });
});

/**
 * Update notification preferences for a subscription
 * @route PATCH /api/push/subscriptions/:id/preferences
 * @access Private
 */
const updatePreferences = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const subscriptionId = req.params.id;
  
  // Validate request
  const validationRules = {
    preferences: 'required|object'
  };
  
  validateRequest(req.body, validationRules);
  
  // Update preferences
  const updatedSubscription = await pushService.updateSubscriptionPreferences(
    userId,
    subscriptionId,
    req.body.preferences
  );
  
  res.json({
    success: true,
    data: updatedSubscription
  });
});

/**
 * Test send a notification to the current user
 * @route POST /api/push/test-notification
 * @access Private
 */
const sendTestNotification = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // Validate request
  const validationRules = {
    title: 'required|string',
    body: 'string',
    data: 'object'
  };
  
  validateRequest(req.body, validationRules);
  
  // Send notification
  const result = await pushService.sendNotification(userId, {
    title: req.body.title,
    body: req.body.body || 'This is a test notification',
    data: req.body.data || { url: '/dashboard' }
  });
  
  res.json({
    success: true,
    data: result
  });
});

/**
 * Send a notification using a template
 * @route POST /api/push/template-notification
 * @access Private (Admin/Manager)
 */
const sendTemplateNotification = asyncHandler(async (req, res) => {
  // Validate request
  const validationRules = {
    userId: 'required|string',
    type: 'required|string',
    data: 'required|object'
  };
  
  validateRequest(req.body, validationRules);
  
  // Check permissions (only admin/manager can send to other users)
  const targetUserId = req.body.userId;
  const currentUserId = req.user.id;
  
  if (targetUserId !== currentUserId && !['admin', 'manager'].includes(req.user.role)) {
    throw new ForbiddenError('You do not have permission to send notifications to other users');
  }
  
  // Check if template exists
  if (!Object.values(pushService.NOTIFICATION_TYPES).includes(req.body.type)) {
    return res.status(400).json({
      success: false,
      message: `Invalid notification type: ${req.body.type}`
    });
  }
  
  // Send notification
  const result = await pushService.sendTemplateNotification(
    targetUserId,
    req.body.type,
    req.body.data,
    req.body.options || {}
  );
  
  res.json({
    success: true,
    data: result
  });
});

/**
 * Send a notification to multiple users
 * @route POST /api/push/bulk-notification
 * @access Private (Admin only)
 */
const sendBulkNotification = asyncHandler(async (req, res) => {
  // Validate request
  const validationRules = {
    userIds: 'required|array',
    'userIds.*': 'string',
    notification: 'required|object',
    'notification.title': 'required|string'
  };
  
  validateRequest(req.body, validationRules);
  
  // Check permissions (only admins can send bulk notifications)
  if (req.user.role !== 'admin') {
    throw new ForbiddenError('Only administrators can send bulk notifications');
  }
  
  // Send notification
  const results = await pushService.sendNotificationToMultiple(
    req.body.userIds,
    req.body.notification,
    req.body.options || {}
  );
  
  res.json({
    success: true,
    data: {
      results,
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    }
  });
});

/**
 * Get unread notifications for current user
 * @route GET /api/push/notifications
 * @access Private
 */
const getNotifications = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // Parse query params
  const options = {
    limit: parseInt(req.query.limit) || 50,
    skip: parseInt(req.query.skip) || 0,
    type: req.query.type || null
  };
  
  // Get notifications
  const notifications = await pushService.getUnreadNotifications(userId, options);
  
  res.json({
    success: true,
    count: notifications.length,
    data: notifications
  });
});

/**
 * Mark notifications as read
 * @route POST /api/push/notifications/mark-read
 * @access Private
 */
const markNotificationsAsRead = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // Validate request
  const validationRules = {
    notificationIds: 'required|array',
    'notificationIds.*': 'string'
  };
  
  validateRequest(req.body, validationRules);
  
  // Mark as read
  const count = await pushService.markNotificationsAsRead(userId, req.body.notificationIds);
  
  res.json({
    success: true,
    message: `${count} notifications marked as read`
  });
});

/**
 * Delete notifications
 * @route DELETE /api/push/notifications
 * @access Private
 */
const deleteNotifications = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // Validate request
  const validationRules = {
    notificationIds: 'required|array',
    'notificationIds.*': 'string'
  };
  
  validateRequest(req.body, validationRules);
  
  // Delete notifications
  const count = await pushService.deleteNotifications(userId, req.body.notificationIds);
  
  res.json({
    success: true,
    message: `${count} notifications deleted`
  });
});

module.exports = {
  getVapidPublicKey,
  subscribe,
  unsubscribe,
  updatePreferences,
  sendTestNotification,
  sendTemplateNotification,
  sendBulkNotification,
  getNotifications,
  markNotificationsAsRead,
  deleteNotifications
};
