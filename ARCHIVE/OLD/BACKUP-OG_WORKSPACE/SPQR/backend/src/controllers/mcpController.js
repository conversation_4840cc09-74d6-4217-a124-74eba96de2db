const mcpService = require('../services/mcpService');
const { catchAsync } = require('../utils/errors');

/**
 * Controller für das Mission Control Panel (MCP)
 * Stellt API-Endpunkte für das MCP bereit
 */
class MCPController {
  /**
   * System-Status-Informationen abrufen
   * @route GET /api/mcp/system-status
   */
  getSystemStatus = catchAsync(async (req, res) => {
    const { component, startTime, endTime, limit } = req.query;
    
    const startDate = startTime ? new Date(startTime) : null;
    const endDate = endTime ? new Date(endTime) : null;
    const maxResults = limit ? parseInt(limit, 10) : 100;
    
    const statusData = await mcpService.getSystemStatusData(
      component, 
      startDate, 
      endDate, 
      maxResults
    );
    
    res.status(200).json({
      success: true,
      count: statusData.length,
      data: statusData
    });
  });

  /**
   * Agent-Metriken abrufen
   * @route GET /api/mcp/agent-metrics
   */
  getAgentMetrics = catchAsync(async (req, res) => {
    const { agentId, limit } = req.query;
    const maxResults = limit ? parseInt(limit, 10) : 100;
    
    const metricsData = await mcpService.getAgentMetricsData(agentId, maxResults);
    
    res.status(200).json({
      success: true,
      count: metricsData.length,
      data: metricsData
    });
  });

  /**
   * Event-Queue-Daten abrufen
   * @route GET /api/mcp/event-queues
   */
  getEventQueueData = catchAsync(async (req, res) => {
    const { queueName, limit } = req.query;
    const maxResults = limit ? parseInt(limit, 10) : 100;
    
    const queueData = await mcpService.getEventQueueData(queueName, maxResults);
    
    res.status(200).json({
      success: true,
      count: queueData.length,
      data: queueData
    });
  });

  /**
   * Audit-Logs abrufen
   * @route GET /api/mcp/audit-logs
   */
  getAuditLogs = catchAsync(async (req, res) => {
    const { userId, action, startTime, endTime, limit } = req.query;
    
    const startDate = startTime ? new Date(startTime) : null;
    const endDate = endTime ? new Date(endTime) : null;
    const maxResults = limit ? parseInt(limit, 10) : 100;
    
    const logs = await mcpService.getAuditLogs(
      userId, 
      action, 
      startDate, 
      endDate, 
      maxResults
    );
    
    res.status(200).json({
      success: true,
      count: logs.length,
      data: logs
    });
  });

  /**
   * Kommando-Verlauf abrufen
   * @route GET /api/mcp/command-history
   */
  getCommandHistory = catchAsync(async (req, res) => {
    const { userId, commandType, startTime, endTime, limit } = req.query;
    
    const startDate = startTime ? new Date(startTime) : null;
    const endDate = endTime ? new Date(endTime) : null;
    const maxResults = limit ? parseInt(limit, 10) : 100;
    
    const history = await mcpService.getCommandHistory(
      userId, 
      commandType, 
      startDate, 
      endDate, 
      maxResults
    );
    
    res.status(200).json({
      success: true,
      count: history.length,
      data: history
    });
  });

  /**
   * Systemweiten Health-Check durchführen
   * @route POST /api/mcp/perform-health-check
   */
  performHealthCheck = catchAsync(async (req, res) => {
    const result = await mcpService.performSystemHealthCheck();
    
    // Audit-Log erstellen
    await mcpService.createAuditLog(
      req.user.id,
      'perform_health_check',
      'system',
      null,
      { result },
      req.ip
    );
    
    res.status(200).json({
      success: true,
      data: result
    });
  });

  /**
   * Status einer spezifischen Komponente prüfen
   * @route POST /api/mcp/check-component/:component
   */
  checkComponentStatus = catchAsync(async (req, res) => {
    const { component } = req.params;
    
    if (!mcpService.systemComponents.includes(component)) {
      return res.status(400).json({
        success: false,
        message: `Ungültige Komponente: ${component}`
      });
    }
    
    const status = await mcpService.checkComponentStatus(component);
    
    // Audit-Log erstellen
    await mcpService.createAuditLog(
      req.user.id,
      'check_component_status',
      'component',
      null,
      { component, status },
      req.ip
    );
    
    res.status(200).json({
      success: true,
      data: status
    });
  });

  /**
   * MCP-Konfiguration abrufen
   * @route GET /api/mcp/config
   */
  getConfiguration = catchAsync(async (req, res) => {
    const { name } = req.query;
    
    const config = await mcpService.getConfiguration(name);
    
    res.status(200).json({
      success: true,
      data: config
    });
  });

  /**
   * MCP-Konfiguration erstellen oder aktualisieren
   * @route POST /api/mcp/config
   */
  upsertConfiguration = catchAsync(async (req, res) => {
    const { name, configData, description } = req.body;
    
    if (!name || !configData) {
      return res.status(400).json({
        success: false,
        message: 'Name und configData sind erforderlich'
      });
    }
    
    const config = await mcpService.upsertConfiguration(name, configData, description);
    
    // Audit-Log erstellen
    await mcpService.createAuditLog(
      req.user.id,
      'update_mcp_config',
      'config',
      config.id,
      { name, description },
      req.ip
    );
    
    res.status(200).json({
      success: true,
      data: config
    });
  });

  /**
   * Benutzerberechtigungen für das MCP abrufen
   * @route GET /api/mcp/permissions/:userId
   */
  getUserPermissions = catchAsync(async (req, res) => {
    const { userId } = req.params;
    
    const permissions = await mcpService.getUserPermissions(userId);
    
    res.status(200).json({
      success: true,
      data: permissions
    });
  });

  /**
   * Benutzerberechtigungen für das MCP aktualisieren
   * @route PUT /api/mcp/permissions/:userId
   */
  updateUserPermissions = catchAsync(async (req, res) => {
    const { userId } = req.params;
    const permissionData = req.body;
    
    const permissions = await mcpService.updateUserPermissions(userId, permissionData);
    
    // Audit-Log erstellen
    await mcpService.createAuditLog(
      req.user.id,
      'update_mcp_permissions',
      'user',
      userId,
      permissionData,
      req.ip
    );
    
    res.status(200).json({
      success: true,
      data: permissions
    });
  });

  /**
   * Kommando ausführen
   * @route POST /api/mcp/execute-command
   */
  executeCommand = catchAsync(async (req, res) => {
    const { commandType, commandData } = req.body;
    
    if (!commandType || !commandData) {
      return res.status(400).json({
        success: false,
        message: 'commandType und commandData sind erforderlich'
      });
    }
    
    let resultStatus = 'success';
    let resultData = {};
    let executionTime = 0;
    
    const startTime = Date.now();
    
    try {
      // Basierend auf dem Kommando-Typ die entsprechende Aktion ausführen
      switch (commandType) {
        case 'restart_service':
          // Dienst neu starten (Beispiel)
          resultData = { message: 'Dienst erfolgreich neu gestartet' };
          break;
          
        case 'reconfigure_agent':
          // Agent neu konfigurieren (Beispiel)
          resultData = { message: 'Agent erfolgreich neu konfiguriert' };
          break;
          
        case 'clear_event_queue':
          // Event-Queue leeren (Beispiel)
          resultData = { message: 'Event-Queue erfolgreich geleert' };
          break;
          
        default:
          return res.status(400).json({
            success: false,
            message: `Unbekannter Kommando-Typ: ${commandType}`
          });
      }
    } catch (error) {
      resultStatus = 'error';
      resultData = { error: error.message };
    }
    
    executionTime = Date.now() - startTime;
    
    // Kommando-Verlauf erstellen
    await mcpService.createCommandHistory(
      req.user.id,
      commandType,
      commandData,
      resultStatus,
      resultData,
      executionTime
    );
    
    // Audit-Log erstellen
    await mcpService.createAuditLog(
      req.user.id,
      `execute_command_${commandType}`,
      'system',
      null,
      { commandData, resultStatus, resultData },
      req.ip
    );
    
    res.status(200).json({
      success: resultStatus === 'success',
      executionTime,
      data: resultData
    });
  });

  /**
   * Dashboard-Zusammenfassung abrufen
   * @route GET /api/mcp/dashboard-summary
   */
  getDashboardSummary = catchAsync(async (req, res) => {
    // System-Status der letzten Komponenten abrufen
    const latestSystemStatus = await Promise.all(
      mcpService.systemComponents.map(async (component) => {
        const status = await mcpService.getSystemStatusData(component, null, null, 1);
        return status.length > 0 ? status[0] : { componentName: component, status: 'unknown' };
      })
    );
    
    // Aktive Agenten-Metriken abrufen
    const agentMetrics = await mcpService.getAgentMetricsData(null, 10);
    
    // Event-Queue-Daten abrufen
    const eventQueues = await mcpService.getEventQueueData(null, 5);
    
    res.status(200).json({
      success: true,
      data: {
        systemStatus: latestSystemStatus,
        agentMetrics,
        eventQueues,
        serverTime: new Date().toISOString(),
        uptime: process.uptime()
      }
    });
  });
}

module.exports = new MCPController();
