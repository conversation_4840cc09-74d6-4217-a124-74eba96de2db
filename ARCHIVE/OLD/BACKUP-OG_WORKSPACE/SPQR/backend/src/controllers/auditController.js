/**
 * Audit Controller
 * 
 * Controller for managing AI decision logs, retrieving explanations,
 * and generating audit reports.
 */
const aiAuditService = require('../services/aiAuditService');
const { handleError } = require('../utils/errors');

const auditController = {
  /**
   * Get a specific decision log by ID
   */
  async getDecision(req, res) {
    try {
      const { id } = req.params;
      const decision = await aiAuditService.getDecisionById(id);
      
      if (!decision) {
        return res.status(404).json({ message: 'Decision log not found' });
      }
      
      return res.json(decision);
    } catch (error) {
      return handleError(error, res, 'Error retrieving decision log');
    }
  },
  
  /**
   * Get explanation data for a decision
   */
  async getDecisionExplanation(req, res) {
    try {
      const { id } = req.params;
      const decision = await aiAuditService.getDecisionById(id);
      
      if (!decision) {
        return res.status(404).json({ message: 'Decision log not found' });
      }
      
      // Create an explanation object from the decision log
      const explanation = {
        decisionId: decision.id,
        timestamp: decision.createdAt,
        summary: decision.explanation || 'No detailed explanation available',
        confidenceScore: decision.confidenceScore,
        model: {
          id: decision.modelId,
          version: decision.modelVersion
        }
      };
      
      // Add additional fields if available
      if (decision.contextData) {
        // Extract potential factors from context data
        const contextData = typeof decision.contextData === 'string' 
          ? JSON.parse(decision.contextData) 
          : decision.contextData;
        
        // Example of extracting factors from context
        if (contextData.factors) {
          explanation.factors = contextData.factors;
        } else {
          // Create simple factors from available context keys
          explanation.factors = Object.entries(contextData)
            .filter(([key, value]) => typeof value !== 'object' && key !== 'userId')
            .map(([key, value]) => ({
              name: key,
              value: typeof value === 'number' ? value : (value ? 1 : 0),
              description: `Input parameter: ${key}`
            }));
        }
        
        // Extract data sources if available
        if (contextData.dataSources) {
          explanation.dataSources = contextData.dataSources;
        }
      }
      
      // Include potential bias assessment
      if (decision.biasAssessment) {
        explanation.biasAssessment = decision.biasAssessment;
      }
      
      return res.json(explanation);
    } catch (error) {
      return handleError(error, res, 'Error retrieving decision explanation');
    }
  },
  
  /**
   * Search decision logs with filters and pagination
   */
  async searchDecisions(req, res) {
    try {
      const { filters, pagination } = req.body;
      const result = await aiAuditService.searchDecisions(filters, pagination);
      return res.json(result);
    } catch (error) {
      return handleError(error, res, 'Error searching decision logs');
    }
  },
  
  /**
   * Generate statistical report on AI decisions
   */
  async generateStatisticalReport(req, res) {
    try {
      const { filters } = req.body;
      const report = await aiAuditService.generateStatisticalReport(filters);
      return res.json(report);
    } catch (error) {
      return handleError(error, res, 'Error generating statistical report');
    }
  },
  
  /**
   * Find similar decisions to a reference decision
   */
  async findSimilarDecisions(req, res) {
    try {
      const { id } = req.params;
      const { limit } = req.query;
      
      const similarDecisions = await aiAuditService.findSimilarDecisions(
        id, 
        limit ? parseInt(limit, 10) : 5
      );
      
      return res.json(similarDecisions);
    } catch (error) {
      return handleError(error, res, 'Error finding similar decisions');
    }
  },
  
  /**
   * Log a new AI decision
   */
  async logDecision(req, res) {
    try {
      const decisionData = req.body;
      
      // Add user ID from authenticated request if available
      if (req.user && req.user.id) {
        decisionData.userId = req.user.id;
      }
      
      const logEntry = await aiAuditService.logDecision(decisionData);
      return res.status(201).json(logEntry);
    } catch (error) {
      return handleError(error, res, 'Error logging AI decision');
    }
  },
  
  /**
   * Process feedback on an AI decision
   */
  async submitDecisionFeedback(req, res) {
    try {
      const { id } = req.params;
      const feedback = req.body;
      
      // Get the decision log
      const decision = await aiAuditService.getDecisionById(id);
      if (!decision) {
        return res.status(404).json({ message: 'Decision log not found' });
      }
      
      // Store feedback in context data
      let contextData = typeof decision.contextData === 'string' 
        ? JSON.parse(decision.contextData) 
        : { ...(decision.contextData || {}) };
      
      contextData.feedback = {
        ...(contextData.feedback || {}),
        userId: req.user?.id,
        timestamp: new Date().toISOString(),
        ...feedback
      };
      
      // Update the decision log
      await decision.update({ 
        contextData 
      });
      
      return res.json({ 
        message: 'Feedback submitted successfully',
        decisionId: id
      });
    } catch (error) {
      return handleError(error, res, 'Error submitting decision feedback');
    }
  },
  
  /**
   * Request human review of an AI decision
   */
  async requestHumanReview(req, res) {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      
      // Get the decision log
      const decision = await aiAuditService.getDecisionById(id);
      if (!decision) {
        return res.status(404).json({ message: 'Decision log not found' });
      }
      
      // Store review request in context data
      let contextData = typeof decision.contextData === 'string' 
        ? JSON.parse(decision.contextData) 
        : { ...(decision.contextData || {}) };
      
      contextData.reviewRequest = {
        userId: req.user?.id,
        timestamp: new Date().toISOString(),
        reason,
        status: 'PENDING'
      };
      
      // Update the decision log
      await decision.update({ 
        contextData 
      });
      
      // Here you would also trigger any notifications or workflows
      // to notify human reviewers about the request
      
      return res.json({ 
        message: 'Review request submitted successfully',
        decisionId: id,
        status: 'PENDING'
      });
    } catch (error) {
      return handleError(error, res, 'Error requesting human review');
    }
  }
};

module.exports = auditController;
