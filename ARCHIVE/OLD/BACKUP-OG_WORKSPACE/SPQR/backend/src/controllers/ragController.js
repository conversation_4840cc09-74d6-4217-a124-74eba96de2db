const ragQueryService = require('../services/ragQueryService');

/**
 * Controller für RAG-Funktionalitäten (Retrieval-Augmented Generation)
 */
const ragController = {
  /**
   * Führt eine RAG-Abfrage durch
   * @param {Object} req - Express-Request-Objekt
   * @param {Object} res - Express-Response-Objekt
   * @returns {Promise<void>}
   */
  async query(req, res) {
    try {
      const { query, useHybridSearch, similarityThreshold, maxResults, temperature } = req.body;
      
      if (!query) {
        return res.status(400).json({
          success: false,
          message: 'Die Abfrage darf nicht leer sein'
        });
      }

      const userId = req.user.id;
      const conversationId = req.body.conversationId;
      
      let previousMessages = [];
      
      // Wenn conversationId vorhanden, lade vorherige Nachrichten
      if (conversationId) {
        previousMessages = await ragQueryService.getConversationHistory(conversationId);
      }
      
      // RAG-Abfrage durchführen
      const result = await ragQueryService.query(
        query,
        userId,
        {
          useHybridSearch: useHybridSearch !== false, // Standard: true
          similarityThreshold: similarityThreshold || 0.7,
          maxResults: maxResults || 5,
          temperature: temperature || 0.7,
          conversationId,
          previousMessages
        }
      );
      
      res.json({
        success: true,
        answer: result.answer,
        conversationId: result.conversationId,
        context: result.context
      });
    } catch (error) {
      console.error('Fehler bei der RAG-Abfrage:', error);
      res.status(500).json({
        success: false,
        message: `Fehler bei der RAG-Abfrage: ${error.message}`
      });
    }
  },

  /**
   * Ruft den Gesprächsverlauf ab
   * @param {Object} req - Express-Request-Objekt
   * @param {Object} res - Express-Response-Objekt
   * @returns {Promise<void>}
   */
  async getConversationHistory(req, res) {
    try {
      const { conversationId } = req.params;
      
      if (!conversationId) {
        return res.status(400).json({
          success: false,
          message: 'Konversations-ID ist erforderlich'
        });
      }
      
      const history = await ragQueryService.getConversationHistory(conversationId);
      
      res.json({
        success: true,
        conversationId,
        history
      });
    } catch (error) {
      console.error('Fehler beim Abrufen des Gesprächsverlaufs:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen des Gesprächsverlaufs: ${error.message}`
      });
    }
  },

  /**
   * Listet alle Konversationen eines Benutzers auf
   * @param {Object} req - Express-Request-Objekt
   * @param {Object} res - Express-Response-Objekt
   * @returns {Promise<void>}
   */
  async getUserConversations(req, res) {
    try {
      const userId = req.user.id;
      const limit = parseInt(req.query.limit) || 20;
      
      const conversations = await ragQueryService.getUserConversations(userId, limit);
      
      res.json({
        success: true,
        userId,
        conversations
      });
    } catch (error) {
      console.error('Fehler beim Abrufen der Konversationen:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Abrufen der Konversationen: ${error.message}`
      });
    }
  },

  /**
   * Führt eine einfache Ähnlichkeitssuche durch, ohne Antwortgenerierung
   * @param {Object} req - Express-Request-Objekt
   * @param {Object} res - Express-Response-Objekt
   * @returns {Promise<void>}
   */
  async similaritySearch(req, res) {
    try {
      const { query, threshold, limit } = req.body;
      
      if (!query) {
        return res.status(400).json({
          success: false,
          message: 'Die Abfrage darf nicht leer sein'
        });
      }
      
      const results = await ragQueryService.embeddingService.similaritySearch(
        query,
        threshold || 0.7,
        limit || 5
      );
      
      res.json({
        success: true,
        query,
        results
      });
    } catch (error) {
      console.error('Fehler bei der Ähnlichkeitssuche:', error);
      res.status(500).json({
        success: false,
        message: `Fehler bei der Ähnlichkeitssuche: ${error.message}`
      });
    }
  },

  /**
   * Führt eine Hybridsuche durch, ohne Antwortgenerierung
   * @param {Object} req - Express-Request-Objekt
   * @param {Object} res - Express-Response-Objekt
   * @returns {Promise<void>}
   */
  async hybridSearch(req, res) {
    try {
      const { query, threshold, limit } = req.body;
      
      if (!query) {
        return res.status(400).json({
          success: false,
          message: 'Die Abfrage darf nicht leer sein'
        });
      }
      
      const results = await ragQueryService.embeddingService.hybridSearch(
        query,
        threshold || 0.7,
        limit || 5
      );
      
      res.json({
        success: true,
        query,
        results
      });
    } catch (error) {
      console.error('Fehler bei der Hybridsuche:', error);
      res.status(500).json({
        success: false,
        message: `Fehler bei der Hybridsuche: ${error.message}`
      });
    }
  }
};

module.exports = ragController;
