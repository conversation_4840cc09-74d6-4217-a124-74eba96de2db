/**
 * Agent Controller
 * 
 * This controller handles API requests related to the specialized agent system,
 * including agent registration, discovery, execution, and management.
 */

const agentRegistryService = require('../services/agentRegistryService');
const agentExecutionService = require('../services/agentExecutionService');
const { SpecializedAgent, AgentRegistry, AgentExecution } = require('../models');
const { Op } = require('sequelize');
const logger = require('winston');

/**
 * Get all registered agents
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAgents = async (req, res) => {
  try {
    const { 
      type, 
      isActive = true, 
      limit = 10, 
      offset = 0, 
      includeTools = false,
      includeKnowledgeSources = false
    } = req.query;

    // Build query options
    const queryOptions = {
      where: { isActive: isActive === 'false' ? false : true },
      limit: parseInt(limit) || 10,
      offset: parseInt(offset) || 0,
      order: [['createdAt', 'DESC']]
    };

    // Add type filter if provided
    if (type) {
      queryOptions.where.agentType = type;
    }

    // Add include options if requested
    if (includeTools === 'true' || includeKnowledgeSources === 'true') {
      queryOptions.include = [];
      
      if (includeTools === 'true') {
        queryOptions.include.push({
          association: 'tools',
          include: [{ association: 'tool', attributes: ['id', 'name', 'description'] }]
        });
      }
      
      if (includeKnowledgeSources === 'true') {
        queryOptions.include.push({ association: 'knowledgeSources' });
      }
    }

    // Get agents
    const agents = await SpecializedAgent.findAll(queryOptions);
    
    const totalCount = await SpecializedAgent.count({
      where: queryOptions.where
    });

    res.json({
      data: agents,
      meta: {
        total: totalCount,
        limit: parseInt(limit) || 10,
        offset: parseInt(offset) || 0
      }
    });
  } catch (error) {
    logger.error(`Error in getAgents: ${error.message}`);
    res.status(500).json({ error: 'Failed to retrieve agents' });
  }
};

/**
 * Get a specific agent by ID
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAgentById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const agent = await SpecializedAgent.findByPk(id, {
      include: [
        { association: 'registry' },
        { 
          association: 'tools',
          include: [{ association: 'tool' }]
        },
        { association: 'knowledgeSources' },
        {
          association: 'teams',
          through: { attributes: ['role'] }
        }
      ]
    });
    
    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }
    
    res.json(agent);
  } catch (error) {
    logger.error(`Error in getAgentById: ${error.message}`);
    res.status(500).json({ error: 'Failed to retrieve agent' });
  }
};

/**
 * Create a new specialized agent
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createAgent = async (req, res) => {
  try {
    const {
      name,
      description,
      agentType,
      systemPrompt,
      capabilities = [],
      parameters = {},
      examples,
      registryId,
      isActive = true
    } = req.body;
    
    // Validate required fields
    if (!name || !agentType || !systemPrompt) {
      return res.status(400).json({ 
        error: 'Missing required fields: name, agentType, and systemPrompt are required' 
      });
    }
    
    // Get the user ID from authentication
    const createdBy = req.user ? req.user.id : null;
    
    // Create agent
    const agent = await agentRegistryService.registerAgent({
      name,
      description,
      agentType,
      systemPrompt,
      capabilities,
      parameters,
      examples,
      isActive,
      createdBy
    }, registryId);
    
    res.status(201).json(agent);
  } catch (error) {
    logger.error(`Error in createAgent: ${error.message}`);
    res.status(500).json({ error: 'Failed to create agent' });
  }
};

/**
 * Update an existing agent
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateAgent = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      agentType,
      systemPrompt,
      capabilities,
      parameters,
      examples,
      isActive,
      version,
      changeReason
    } = req.body;
    
    // Get the user ID from authentication
    const userId = req.user ? req.user.id : null;
    
    // Update with history tracking
    const updatedAgent = await agentRegistryService.updateAgentWithHistory(
      id,
      {
        name,
        description,
        agentType,
        systemPrompt,
        capabilities,
        parameters,
        examples,
        isActive,
        version
      },
      userId,
      changeReason || 'Updated via API'
    );
    
    res.json(updatedAgent);
  } catch (error) {
    logger.error(`Error in updateAgent: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to update agent' });
  }
};

/**
 * Delete an agent (soft delete by setting isActive to false)
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteAgent = async (req, res) => {
  try {
    const { id } = req.params;
    
    const agent = await SpecializedAgent.findByPk(id);
    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }
    
    // Soft delete by setting isActive to false
    await agent.update({ isActive: false });
    
    // Refresh cache
    await agentRegistryService.refreshAgentById(id);
    
    res.json({ message: 'Agent deactivated successfully' });
  } catch (error) {
    logger.error(`Error in deleteAgent: ${error.message}`);
    res.status(500).json({ error: 'Failed to delete agent' });
  }
};

/**
 * Execute an agent
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.executeAgent = async (req, res) => {
  try {
    const { id } = req.params;
    const { input, context, conversationId } = req.body;
    
    if (!input) {
      return res.status(400).json({ error: 'Input is required' });
    }
    
    const result = await agentExecutionService.executeAgent({
      agentId: id,
      input,
      context: context || {},
      conversationId
    });
    
    res.json(result);
  } catch (error) {
    logger.error(`Error in executeAgent: ${error.message}`);
    
    if (error.message.includes('not found') || error.message.includes('not active')) {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to execute agent' });
  }
};

/**
 * Get agent execution status
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getExecutionStatus = async (req, res) => {
  try {
    const { executionId } = req.params;
    
    const status = await agentExecutionService.getExecutionStatus(executionId);
    
    res.json(status);
  } catch (error) {
    logger.error(`Error in getExecutionStatus: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to get execution status' });
  }
};

/**
 * Get agent executions
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAgentExecutions = async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 10, offset = 0 } = req.query;
    
    // Verify agent exists
    const agent = await SpecializedAgent.findByPk(id);
    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }
    
    // Get executions
    const executions = await AgentExecution.findAll({
      where: { agentId: id },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['startTime', 'DESC']]
    });
    
    const totalCount = await AgentExecution.count({
      where: { agentId: id }
    });
    
    res.json({
      data: executions,
      meta: {
        total: totalCount,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    logger.error(`Error in getAgentExecutions: ${error.message}`);
    res.status(500).json({ error: 'Failed to retrieve agent executions' });
  }
};

/**
 * Get agent registries
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getRegistries = async (req, res) => {
  try {
    const { limit = 10, offset = 0 } = req.query;
    
    const registries = await AgentRegistry.findAll({
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });
    
    const totalCount = await AgentRegistry.count();
    
    res.json({
      data: registries,
      meta: {
        total: totalCount,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    logger.error(`Error in getRegistries: ${error.message}`);
    res.status(500).json({ error: 'Failed to retrieve registries' });
  }
};

/**
 * Create a new registry
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createRegistry = async (req, res) => {
  try {
    const { name, description, isActive = true } = req.body;
    
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }
    
    const registry = await AgentRegistry.create({
      name,
      description,
      isActive
    });
    
    res.status(201).json(registry);
  } catch (error) {
    logger.error(`Error in createRegistry: ${error.message}`);
    res.status(500).json({ error: 'Failed to create registry' });
  }
};

/**
 * Add a tool to an agent
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addToolToAgent = async (req, res) => {
  try {
    const { id } = req.params;
    const { toolId, parameters = {}, allowAutoTrigger = false } = req.body;
    
    if (!toolId) {
      return res.status(400).json({ error: 'Tool ID is required' });
    }
    
    const agentTool = await agentRegistryService.assignToolToAgent(
      id,
      toolId,
      { parameters, allowAutoTrigger }
    );
    
    res.status(201).json(agentTool);
  } catch (error) {
    logger.error(`Error in addToolToAgent: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to add tool to agent' });
  }
};

/**
 * Add a knowledge source to an agent
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addKnowledgeSource = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      sourceType, 
      sourceId,
      sourceName,
      description,
      parameters = {}
    } = req.body;
    
    if (!sourceType || !sourceName) {
      return res.status(400).json({ 
        error: 'Source type and source name are required' 
      });
    }
    
    const knowledgeSource = await agentRegistryService.addKnowledgeSource(
      id,
      {
        sourceType,
        sourceId,
        sourceName,
        description,
        parameters
      }
    );
    
    res.status(201).json(knowledgeSource);
  } catch (error) {
    logger.error(`Error in addKnowledgeSource: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to add knowledge source' });
  }
};

/**
 * Get agent types
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAgentTypes = async (req, res) => {
  try {
    const types = await agentRegistryService.getAgentTypes();
    
    res.json(types);
  } catch (error) {
    logger.error(`Error in getAgentTypes: ${error.message}`);
    res.status(500).json({ error: 'Failed to retrieve agent types' });
  }
};

/**
 * Create a team of agents
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createAgentTeam = async (req, res) => {
  try {
    const { name, description, agentIds = [], parameters = {} } = req.body;
    
    if (!name) {
      return res.status(400).json({ error: 'Team name is required' });
    }
    
    // Get user ID from authentication
    const createdBy = req.user ? req.user.id : null;
    
    const team = await agentRegistryService.createAgentTeam(
      {
        name,
        description,
        parameters,
        createdBy
      },
      agentIds
    );
    
    res.status(201).json(team);
  } catch (error) {
    logger.error(`Error in createAgentTeam: ${error.message}`);
    res.status(500).json({ error: 'Failed to create agent team' });
  }
};

/**
 * Get agent metrics
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAgentMetrics = async (req, res) => {
  try {
    const { id } = req.params;
    const { period = 'daily', limit = 30 } = req.query;
    
    // Verify agent exists
    const agent = await SpecializedAgent.findByPk(id);
    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }
    
    // Get metrics
    const metrics = await agent.sequelize.model('AgentMetrics').findAll({
      where: { 
        agentId: id,
        period
      },
      limit: parseInt(limit),
      order: [['startDate', 'DESC']]
    });
    
    res.json(metrics);
  } catch (error) {
    logger.error(`Error in getAgentMetrics: ${error.message}`);
    res.status(500).json({ error: 'Failed to retrieve agent metrics' });
  }
};
