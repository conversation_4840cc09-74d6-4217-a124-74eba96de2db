/**
 * AI Controller
 * 
 * This controller handles all AI-related routes including conversations with the
 * AI orchestrator, tool execution, agent management, and context handling.
 */

const orchestrator = require('../services/orchestratorService');
const toolManager = require('../services/toolManagerService');
const claudeService = require('../services/claudeService');
const { AgentConfig, Tool, ToolExecution } = require('../models');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');

/**
 * Process a message to the AI orchestrator
 * 
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
async function processMessage(req, res, next) {
  try {
    const { message, conversationId, contextData } = req.body;
    
    if (!message) {
      throw new ValidationError('Message is required');
    }
    
    // Initialize orchestrator if needed
    if (!orchestrator.initialized) {
      await orchestrator.initialize();
    }
    
    // Process the message
    const response = await orchestrator.processMessage({
      conversationId,
      message,
      userId: req.user?.id,
      contextData
    });
    
    res.json({
      status: 'success',
      data: response
    });
  } catch (error) {
    next(error);
  }
}

/**
 * Get conversation history and context
 * 
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
async function getConversation(req, res, next) {
  try {
    const { conversationId } = req.params;
    
    if (!conversationId) {
      throw new ValidationError('Conversation ID is required');
    }
    
    // Check if conversation exists
    const conversation = orchestrator.conversations.get(conversationId);
    if (!conversation) {
      throw new NotFoundError(`Conversation with ID ${conversationId} not found`);
    }
    
    // Ensure the user has access to this conversation
    if (req.user && conversation.context.lastUserId && req.user.id !== conversation.context.lastUserId) {
      throw new AuthorizationError('You do not have access to this conversation');
    }
    
    res.json({
      status: 'success',
      data: {
        conversationId,
        messages: conversation.messages,
        context: conversation.context
      }
    });
  } catch (error) {
    next(error);
  }
}

/**
 * Execute a tool directly
 * 
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
async function executeTool(req, res, next) {
  try {
    const { toolName, parameters, conversationId, contextData } = req.body;
    
    if (!toolName) {
      throw new ValidationError('Tool name is required');
    }
    
    // Get conversation context if a conversation ID is provided
    let context = contextData || {};
    if (conversationId) {
      const conversation = orchestrator.conversations.get(conversationId);
      if (conversation) {
        context = { ...conversation.context, ...context };
      }
    }
    
    // Execute the tool
    const result = await toolManager.executeTool({
      toolName,
      parameters,
      userId: req.user?.id,
      conversationId,
      context
    });
    
    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    next(error);
  }
}

/**
 * Get a list of available tools
 * 
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
async function getAvailableTools(req, res, next) {
  try {
    const { category, agentId } = req.query;
    
    // Get appropriate security level based on user role
    let securityLevel = 'public';
    if (req.user) {
      securityLevel = req.user.isAdmin ? 'admin' : 'protected';
    }
    
    // Get tools with filters
    const tools = await toolManager.getTools({
      category,
      securityLevel,
      agentId
    });
    
    // Format for API response (remove sensitive data)
    const formattedTools = tools.map(tool => ({
      id: tool.id,
      name: tool.name,
      description: tool.description,
      category: tool.category,
      parameters: tool.parameters,
      requiresConfirmation: tool.requiresConfirmation,
      method: tool.method === 'FUNCTION' ? 'FUNCTION' : 'API'
    }));
    
    res.json({
      status: 'success',
      data: formattedTools
    });
  } catch (error) {
    next(error);
  }
}

/**
 * Get a list of available agents
 * 
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
async function getAvailableAgents(req, res, next) {
  try {
    // Determine which agents the user can access based on visibility
    let query = { isActive: true };
    
    if (req.user) {
      if (!req.user.isAdmin) {
        query.visibility = ['public', 'team'];
        // Add team check here if needed
      }
    } else {
      query.visibility = 'public';
    }
    
    const agents = await AgentConfig.findAll({ where: query });
    
    // Format for API response (remove sensitive data)
    const formattedAgents = agents.map(agent => ({
      id: agent.id,
      name: agent.name,
      description: agent.description,
      type: agent.type,
      aiModel: agent.aiModel,
      version: agent.version
    }));
    
    res.json({
      status: 'success',
      data: formattedAgents
    });
  } catch (error) {
    next(error);
  }
}

/**
 * Process an event using the AI orchestrator
 * 
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
async function processEvent(req, res, next) {
  try {
    const { eventId } = req.params;
    
    if (!eventId) {
      throw new ValidationError('Event ID is required');
    }
    
    // Load the event from the database
    const Event = require('../models').Event;
    const event = await Event.findByPk(eventId);
    
    if (!event) {
      throw new NotFoundError(`Event with ID ${eventId} not found`);
    }
    
    // Process the event
    const result = await orchestrator.processEvent(event);
    
    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    next(error);
  }
}

/**
 * Get a direct completion from Claude
 * 
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
async function getDirectCompletion(req, res, next) {
  try {
    const { prompt, systemPrompt, options } = req.body;
    
    if (!prompt) {
      throw new ValidationError('Prompt is required');
    }
    
    // Initialize Claude service if needed
    const claude = new claudeService();
    
    // Get completion
    const completion = await claude.getCompletion(prompt, systemPrompt, options);
    
    res.json({
      status: 'success',
      data: {
        completion
      }
    });
  } catch (error) {
    next(error);
  }
}

/**
 * Delete a conversation
 * 
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
async function deleteConversation(req, res, next) {
  try {
    const { conversationId } = req.params;
    
    if (!conversationId) {
      throw new ValidationError('Conversation ID is required');
    }
    
    // Check if conversation exists
    const conversation = orchestrator.conversations.get(conversationId);
    if (!conversation) {
      throw new NotFoundError(`Conversation with ID ${conversationId} not found`);
    }
    
    // Ensure the user has access to this conversation
    if (req.user && conversation.context.lastUserId && req.user.id !== conversation.context.lastUserId) {
      throw new AuthorizationError('You do not have access to this conversation');
    }
    
    // Delete the conversation
    orchestrator.conversations.delete(conversationId);
    
    res.json({
      status: 'success',
      message: `Conversation ${conversationId} deleted successfully`
    });
  } catch (error) {
    next(error);
  }
}

/**
 * Update context data for a conversation
 * 
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
async function updateConversationContext(req, res, next) {
  try {
    const { conversationId } = req.params;
    const { contextData } = req.body;
    
    if (!conversationId) {
      throw new ValidationError('Conversation ID is required');
    }
    
    if (!contextData || typeof contextData !== 'object') {
      throw new ValidationError('Context data must be an object');
    }
    
    // Get or create conversation
    const conversation = await orchestrator.getOrCreateConversation(conversationId);
    
    // Update context
    conversation.context = {
      ...conversation.context,
      ...contextData,
      updatedAt: new Date().toISOString()
    };
    
    res.json({
      status: 'success',
      data: {
        conversationId,
        context: conversation.context
      }
    });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  processMessage,
  getConversation,
  executeTool,
  getAvailableTools,
  getAvailableAgents,
  processEvent,
  getDirectCompletion,
  deleteConversation,
  updateConversationContext
};
