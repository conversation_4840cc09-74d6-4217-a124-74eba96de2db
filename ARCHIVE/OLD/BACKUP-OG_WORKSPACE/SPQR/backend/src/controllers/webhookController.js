/**
 * Webhook Controller
 * Handles incoming webhooks and webhook management operations
 */

const webhookService = require('../services/webhookService');
const eventService = require('../services/eventService');
const { validateWebhookSignature } = require('../utils/securityUtils');

/**
 * Process an incoming webhook request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.processWebhook = async (req, res) => {
  try {
    const { endpoint } = req.params;
    const payload = req.body;
    const signature = req.headers['x-webhook-signature'];
    
    // Process the webhook
    const result = await webhookService.processWebhook(endpoint, payload, signature);
    
    res.status(200).json({
      success: true,
      message: 'Webhook processed successfully',
      eventId: result.eventId
    });
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to process webhook'
    });
  }
};

/**
 * Create a new webhook
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createWebhook = async (req, res) => {
  try {
    const { name, description, endpoint, targetService } = req.body;
    
    if (!name || !endpoint) {
      return res.status(400).json({
        success: false,
        message: 'Name and endpoint are required'
      });
    }
    
    // Create webhook with auto-generated secret if not provided
    const webhook = await webhookService.createWebhook(
      {
        name,
        description,
        endpoint,
        targetService,
        secret: req.body.secret || webhookService.generateSecret()
      },
      req.user.id
    );
    
    res.status(201).json({
      success: true,
      message: 'Webhook created successfully',
      webhook: {
        id: webhook.id,
        name: webhook.name,
        description: webhook.description,
        endpoint: webhook.endpoint,
        targetService: webhook.targetService,
        active: webhook.active,
        // Important: Only show secret on creation
        secret: webhook.secret,
        createdAt: webhook.createdAt
      }
    });
  } catch (error) {
    console.error('Error creating webhook:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create webhook'
    });
  }
};

/**
 * Update a webhook
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateWebhook = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, active, targetService, secret } = req.body;
    
    // Check if user has access to this webhook
    const webhook = await webhookService.getWebhook(id);
    
    if (!webhook) {
      return res.status(404).json({
        success: false,
        message: 'Webhook not found'
      });
    }
    
    // Only admin, manager, or webhook creator can update it
    if (
      req.user.role !== 'admin' &&
      req.user.role !== 'manager' &&
      webhook.userId !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this webhook'
      });
    }
    
    // Update webhook
    const updatedWebhook = await webhookService.updateWebhook(id, {
      name,
      description,
      active,
      targetService,
      secret
    });
    
    res.status(200).json({
      success: true,
      message: 'Webhook updated successfully',
      webhook: {
        id: updatedWebhook.id,
        name: updatedWebhook.name,
        description: updatedWebhook.description,
        endpoint: updatedWebhook.endpoint,
        targetService: updatedWebhook.targetService,
        active: updatedWebhook.active,
        createdAt: updatedWebhook.createdAt,
        updatedAt: updatedWebhook.updatedAt
      }
    });
  } catch (error) {
    console.error('Error updating webhook:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update webhook'
    });
  }
};

/**
 * Delete a webhook
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteWebhook = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if user has access to this webhook
    const webhook = await webhookService.getWebhook(id);
    
    if (!webhook) {
      return res.status(404).json({
        success: false,
        message: 'Webhook not found'
      });
    }
    
    // Only admin, manager, or webhook creator can delete it
    if (
      req.user.role !== 'admin' &&
      req.user.role !== 'manager' &&
      webhook.userId !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this webhook'
      });
    }
    
    // Delete webhook
    await webhookService.deleteWebhook(id);
    
    res.status(200).json({
      success: true,
      message: 'Webhook deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting webhook:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete webhook'
    });
  }
};

/**
 * Get a webhook by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getWebhook = async (req, res) => {
  try {
    const { id } = req.params;
    const webhook = await webhookService.getWebhook(id);
    
    if (!webhook) {
      return res.status(404).json({
        success: false,
        message: 'Webhook not found'
      });
    }
    
    // Check if user has access to view this webhook
    if (
      req.user.role !== 'admin' &&
      req.user.role !== 'manager' &&
      webhook.userId !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view this webhook'
      });
    }
    
    res.status(200).json({
      success: true,
      webhook: {
        id: webhook.id,
        name: webhook.name,
        description: webhook.description,
        endpoint: webhook.endpoint,
        targetService: webhook.targetService,
        active: webhook.active,
        createdAt: webhook.createdAt,
        updatedAt: webhook.updatedAt,
        creator: webhook.creator ? {
          id: webhook.creator.id,
          email: webhook.creator.email,
          name: `${webhook.creator.firstName} ${webhook.creator.lastName}`
        } : null
      }
    });
  } catch (error) {
    console.error('Error getting webhook:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get webhook'
    });
  }
};

/**
 * List webhooks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listWebhooks = async (req, res) => {
  try {
    const { active, targetService } = req.query;
    
    // Filters based on user role
    const filters = {};
    
    if (active !== undefined) {
      filters.active = active === 'true';
    }
    
    if (targetService) {
      filters.targetService = targetService;
    }
    
    // Regular users can only see their own webhooks
    if (req.user.role === 'agent') {
      filters.userId = req.user.id;
    }
    
    const webhooks = await webhookService.listWebhooks(filters);
    
    res.status(200).json({
      success: true,
      count: webhooks.length,
      webhooks: webhooks.map(webhook => ({
        id: webhook.id,
        name: webhook.name,
        description: webhook.description,
        endpoint: webhook.endpoint,
        targetService: webhook.targetService,
        active: webhook.active,
        createdAt: webhook.createdAt,
        updatedAt: webhook.updatedAt,
        creator: webhook.creator ? {
          id: webhook.creator.id,
          email: webhook.creator.email,
          name: `${webhook.creator.firstName} ${webhook.creator.lastName}`
        } : null
      }))
    });
  } catch (error) {
    console.error('Error listing webhooks:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to list webhooks'
    });
  }
};

/**
 * Regenerate webhook secret
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.regenerateSecret = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if user has access to this webhook
    const webhook = await webhookService.getWebhook(id);
    
    if (!webhook) {
      return res.status(404).json({
        success: false,
        message: 'Webhook not found'
      });
    }
    
    // Only admin, manager, or webhook creator can regenerate secret
    if (
      req.user.role !== 'admin' &&
      req.user.role !== 'manager' &&
      webhook.userId !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to regenerate webhook secret'
      });
    }
    
    // Generate new secret
    const secret = webhookService.generateSecret();
    
    // Update webhook
    await webhookService.updateWebhook(id, { secret });
    
    res.status(200).json({
      success: true,
      message: 'Webhook secret regenerated successfully',
      secret
    });
  } catch (error) {
    console.error('Error regenerating webhook secret:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to regenerate webhook secret'
    });
  }
};
