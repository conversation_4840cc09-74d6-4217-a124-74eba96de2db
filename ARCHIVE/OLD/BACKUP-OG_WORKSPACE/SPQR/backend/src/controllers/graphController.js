/**
 * Graph Controller
 * 
 * This controller provides the API endpoints for managing and interacting with
 * LangGraph agent workflows.
 */

const langGraphService = require('../services/graphs/langGraphService');
const logger = require('winston');

/**
 * Create a new graph
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.createGraph = async (req, res) => {
  try {
    const { definition } = req.body;
    const userId = req.user.id; // Assuming authentication middleware sets req.user
    
    if (!definition || !definition.nodes || !definition.startNode) {
      return res.status(400).json({ error: 'Invalid graph definition' });
    }
    
    const graphId = langGraphService.createGraph(definition, userId);
    
    res.status(201).json({
      success: true,
      graphId,
      message: 'Graph created successfully'
    });
  } catch (error) {
    logger.error(`Error creating graph: ${error.message}`);
    res.status(500).json({ error: 'Failed to create graph' });
  }
};

/**
 * Get a graph by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.getGraph = async (req, res) => {
  try {
    const { graphId } = req.params;
    const userId = req.user.id;
    
    const graph = langGraphService.getGraph(graphId);
    
    // Check if user owns the graph
    if (graph.userId !== userId) {
      return res.status(403).json({ error: 'You do not have permission to access this graph' });
    }
    
    res.status(200).json({
      success: true,
      graph: {
        id: graph.id,
        definition: graph.definition,
        createdAt: graph.createdAt,
        status: graph.status
      }
    });
  } catch (error) {
    logger.error(`Error getting graph: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Graph not found' });
    }
    
    res.status(500).json({ error: 'Failed to get graph' });
  }
};

/**
 * List all graphs for a user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.listGraphs = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const graphs = langGraphService.listGraphs(userId);
    
    res.status(200).json({
      success: true,
      graphs
    });
  } catch (error) {
    logger.error(`Error listing graphs: ${error.message}`);
    res.status(500).json({ error: 'Failed to list graphs' });
  }
};

/**
 * Start a graph's execution
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.startGraph = async (req, res) => {
  try {
    const { graphId } = req.params;
    const { initialContext } = req.body;
    const userId = req.user.id;
    
    // Get the graph to check ownership
    const graph = langGraphService.getGraph(graphId);
    
    if (graph.userId !== userId) {
      return res.status(403).json({ error: 'You do not have permission to start this graph' });
    }
    
    // Start the graph
    const result = await langGraphService.startGraph(graphId, initialContext || {});
    
    res.status(200).json({
      success: true,
      state: result.state
    });
  } catch (error) {
    logger.error(`Error starting graph: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Graph not found' });
    }
    
    res.status(500).json({ error: 'Failed to start graph' });
  }
};

/**
 * Continue a graph's execution
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.continueGraph = async (req, res) => {
  try {
    const { graphId } = req.params;
    const { userInput } = req.body;
    const userId = req.user.id;
    
    // Get the graph to check ownership
    const graph = langGraphService.getGraph(graphId);
    
    if (graph.userId !== userId) {
      return res.status(403).json({ error: 'You do not have permission to continue this graph' });
    }
    
    // Continue the graph
    const result = await langGraphService.continueGraph(graphId, userInput);
    
    res.status(200).json({
      success: true,
      state: result.state,
      error: result.error
    });
  } catch (error) {
    logger.error(`Error continuing graph: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Graph not found' });
    }
    
    res.status(500).json({ error: 'Failed to continue graph' });
  }
};

/**
 * Pause a graph's execution
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.pauseGraph = async (req, res) => {
  try {
    const { graphId } = req.params;
    const userId = req.user.id;
    
    // Get the graph to check ownership
    const graph = langGraphService.getGraph(graphId);
    
    if (graph.userId !== userId) {
      return res.status(403).json({ error: 'You do not have permission to pause this graph' });
    }
    
    // Pause the graph
    const result = langGraphService.pauseGraph(graphId);
    
    res.status(200).json({
      success: true,
      state: result.state
    });
  } catch (error) {
    logger.error(`Error pausing graph: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Graph not found' });
    }
    
    res.status(500).json({ error: 'Failed to pause graph' });
  }
};

/**
 * Resume a paused graph
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.resumeGraph = async (req, res) => {
  try {
    const { graphId } = req.params;
    const userId = req.user.id;
    
    // Get the graph to check ownership
    const graph = langGraphService.getGraph(graphId);
    
    if (graph.userId !== userId) {
      return res.status(403).json({ error: 'You do not have permission to resume this graph' });
    }
    
    // Resume the graph
    const result = await langGraphService.resumeGraph(graphId);
    
    res.status(200).json({
      success: true,
      state: result.state,
      error: result.error
    });
  } catch (error) {
    logger.error(`Error resuming graph: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Graph not found' });
    }
    
    res.status(500).json({ error: 'Failed to resume graph' });
  }
};

/**
 * Terminate a graph
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.terminateGraph = async (req, res) => {
  try {
    const { graphId } = req.params;
    const userId = req.user.id;
    
    // Get the graph to check ownership
    const graph = langGraphService.getGraph(graphId);
    
    if (graph.userId !== userId) {
      return res.status(403).json({ error: 'You do not have permission to terminate this graph' });
    }
    
    // Terminate the graph
    const result = langGraphService.terminateGraph(graphId);
    
    res.status(200).json({
      success: true,
      state: result.state
    });
  } catch (error) {
    logger.error(`Error terminating graph: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Graph not found' });
    }
    
    res.status(500).json({ error: 'Failed to terminate graph' });
  }
};

/**
 * Get a graph's current state
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.getGraphState = async (req, res) => {
  try {
    const { graphId } = req.params;
    const userId = req.user.id;
    
    // Get the graph to check ownership
    const graph = langGraphService.getGraph(graphId);
    
    if (graph.userId !== userId) {
      return res.status(403).json({ error: 'You do not have permission to access this graph' });
    }
    
    // Get the graph state
    const state = langGraphService.getGraphState(graphId);
    
    res.status(200).json({
      success: true,
      state
    });
  } catch (error) {
    logger.error(`Error getting graph state: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Graph or state not found' });
    }
    
    res.status(500).json({ error: 'Failed to get graph state' });
  }
};

/**
 * Get a graph's visualization data
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.getGraphVisualization = async (req, res) => {
  try {
    const { graphId } = req.params;
    const userId = req.user.id;
    
    // Get the graph to check ownership
    const graph = langGraphService.getGraph(graphId);
    
    if (graph.userId !== userId) {
      return res.status(403).json({ error: 'You do not have permission to access this graph' });
    }
    
    // Get the graph visualization data
    const visualization = langGraphService.getGraphVisualization(graphId);
    
    res.status(200).json({
      success: true,
      visualization
    });
  } catch (error) {
    logger.error(`Error getting graph visualization: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Graph not found' });
    }
    
    res.status(500).json({ error: 'Failed to get graph visualization' });
  }
};

/**
 * Get a graph's execution history
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.getGraphHistory = async (req, res) => {
  try {
    const { graphId } = req.params;
    const userId = req.user.id;
    
    // Get the graph to check ownership
    const graph = langGraphService.getGraph(graphId);
    
    if (graph.userId !== userId) {
      return res.status(403).json({ error: 'You do not have permission to access this graph' });
    }
    
    // Get the graph history
    const history = langGraphService.getGraphHistory(graphId);
    
    res.status(200).json({
      success: true,
      history
    });
  } catch (error) {
    logger.error(`Error getting graph history: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Graph not found' });
    }
    
    res.status(500).json({ error: 'Failed to get graph history' });
  }
};
