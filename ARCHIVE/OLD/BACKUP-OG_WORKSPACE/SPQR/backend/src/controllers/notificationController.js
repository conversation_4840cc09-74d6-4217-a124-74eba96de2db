/**
 * Notification Controller
 * Handles user notification operations
 */

const notificationService = require('../services/notificationService');
const { isUUID } = require('validator');

/**
 * Get notifications for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUserNotifications = async (req, res) => {
  try {
    const {
      status,
      type,
      limit = 50,
      offset = 0,
      includeExpired = false
    } = req.query;
    
    const result = await notificationService.getUserNotifications(
      req.user.id,
      {
        status,
        type,
        limit,
        offset,
        includeExpired: includeExpired === 'true'
      }
    );
    
    res.status(200).json({
      success: true,
      count: result.count,
      notifications: result.notifications
    });
  } catch (error) {
    console.error('Error getting user notifications:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get notifications'
    });
  }
};

/**
 * Get notification by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getNotification = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!isUUID(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid notification ID'
      });
    }
    
    const notification = await notificationService.getNotificationById(id);
    
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }
    
    // Authorization check - users can only access their own notifications
    if (notification.userId !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to access this notification'
      });
    }
    
    res.status(200).json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('Error getting notification:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get notification'
    });
  }
};

/**
 * Mark notifications as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.markAsRead = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids)) {
      return res.status(400).json({
        success: false,
        message: 'Notification IDs must be provided as an array'
      });
    }
    
    const updatedCount = await notificationService.markAsRead(ids, req.user.id);
    
    res.status(200).json({
      success: true,
      message: `${updatedCount} notifications marked as read`,
      updatedCount
    });
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to mark notifications as read'
    });
  }
};

/**
 * Mark all notifications as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.markAllAsRead = async (req, res) => {
  try {
    const { type } = req.query;
    
    const updatedCount = await notificationService.markAllAsRead(req.user.id, type);
    
    res.status(200).json({
      success: true,
      message: `${updatedCount} notifications marked as read`,
      updatedCount
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to mark all notifications as read'
    });
  }
};

/**
 * Delete notifications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteNotifications = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids)) {
      return res.status(400).json({
        success: false,
        message: 'Notification IDs must be provided as an array'
      });
    }
    
    const deletedCount = await notificationService.deleteNotifications(ids, req.user.id);
    
    res.status(200).json({
      success: true,
      message: `${deletedCount} notifications deleted`,
      deletedCount
    });
  } catch (error) {
    console.error('Error deleting notifications:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete notifications'
    });
  }
};

/**
 * Archive notifications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.archiveNotifications = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids)) {
      return res.status(400).json({
        success: false,
        message: 'Notification IDs must be provided as an array'
      });
    }
    
    const updatedCount = await notificationService.archiveNotifications(ids, req.user.id);
    
    res.status(200).json({
      success: true,
      message: `${updatedCount} notifications archived`,
      updatedCount
    });
  } catch (error) {
    console.error('Error archiving notifications:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to archive notifications'
    });
  }
};

/**
 * Get unread notification count
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUnreadCount = async (req, res) => {
  try {
    const count = await notificationService.getUnreadCount(req.user.id);
    
    res.status(200).json({
      success: true,
      count
    });
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get unread notification count'
    });
  }
};

/**
 * Create a notification (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createNotification = async (req, res) => {
  try {
    const { title, message, type, userId, eventId, data, expiresAt } = req.body;
    
    if (!title || !message || !type || !userId) {
      return res.status(400).json({
        success: false,
        message: 'Title, message, type, and userId are required'
      });
    }
    
    const notification = await notificationService.createNotification({
      title,
      message,
      type,
      userId,
      eventId,
      data,
      expiresAt: expiresAt ? new Date(expiresAt) : null
    });
    
    res.status(201).json({
      success: true,
      message: 'Notification created successfully',
      notification
    });
  } catch (error) {
    console.error('Error creating notification:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create notification'
    });
  }
};

/**
 * Create notifications for multiple users (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createMultipleNotifications = async (req, res) => {
  try {
    const { title, message, type, userIds, eventId, data, expiresAt } = req.body;
    
    if (!title || !message || !type || !userIds || !Array.isArray(userIds)) {
      return res.status(400).json({
        success: false,
        message: 'Title, message, type, and userIds array are required'
      });
    }
    
    const notifications = await notificationService.createMultipleNotifications(
      userIds,
      {
        title,
        message,
        type,
        eventId,
        data,
        expiresAt: expiresAt ? new Date(expiresAt) : null
      }
    );
    
    res.status(201).json({
      success: true,
      message: `${notifications.length} notifications created successfully`,
      count: notifications.length
    });
  } catch (error) {
    console.error('Error creating multiple notifications:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create notifications'
    });
  }
};
