const tenantService = require('../services/tenantService');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');

/**
 * Tenant controller for handling tenant-related operations
 */
const tenantController = {
  /**
   * Get all tenants (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getAllTenants: async (req, res, next) => {
    try {
      // Only superadmin or admin can list all tenants
      if (req.user.role !== 'superadmin' && req.user.role !== 'admin') {
        throw new AuthorizationError('Insufficient permissions to access tenant list');
      }
      
      const { page = 1, limit = 10, search, status, plan } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
        status,
        plan
      };
      
      const result = await tenantService.getAllTenants(options);
      res.json(result);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get tenant by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getTenantById: async (req, res, next) => {
    try {
      const { id } = req.params;
      
      // Check if user has access to this tenant
      if (req.user.role !== 'superadmin' && req.tenantId !== id) {
        throw new AuthorizationError('Not authorized to access this tenant');
      }
      
      const tenant = await tenantService.getTenantById(id);
      res.json(tenant);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Create tenant (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  createTenant: async (req, res, next) => {
    try {
      // Only superadmin can create tenants
      if (req.user.role !== 'superadmin') {
        throw new AuthorizationError('Insufficient permissions to create tenants');
      }
      
      const { name, slug, domain, plan, settings } = req.body;
      
      if (!name || !slug) {
        throw new ValidationError('Name and slug are required');
      }
      
      const tenantData = {
        name,
        slug,
        domain,
        plan,
        settings
      };
      
      const tenant = await tenantService.createTenant(tenantData, req.user.id);
      res.status(201).json(tenant);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update tenant
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateTenant: async (req, res, next) => {
    try {
      const { id } = req.params;
      
      // Check permissions
      if (req.user.role !== 'superadmin' && req.user.role !== 'admin') {
        throw new AuthorizationError('Insufficient permissions to update tenants');
      }
      
      // Regular admins can only update their own tenant
      if (req.user.role === 'admin' && req.tenantId !== id) {
        throw new AuthorizationError('Not authorized to update this tenant');
      }
      
      const updateData = req.body;
      
      // Prevent non-superadmins from upgrading plans
      if (req.user.role !== 'superadmin' && updateData.plan) {
        const currentTenant = await tenantService.getTenantById(id);
        if (updateData.plan !== currentTenant.plan) {
          throw new AuthorizationError('Insufficient permissions to change subscription plan');
        }
      }
      
      const tenant = await tenantService.updateTenant(id, updateData);
      res.json(tenant);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete tenant (superadmin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  deleteTenant: async (req, res, next) => {
    try {
      const { id } = req.params;
      
      // Only superadmin can delete tenants
      if (req.user.role !== 'superadmin') {
        throw new AuthorizationError('Insufficient permissions to delete tenants');
      }
      
      // Check if it's the default tenant
      const tenant = await tenantService.getTenantById(id);
      if (tenant.slug === 'default') {
        throw new ValidationError('The default tenant cannot be deleted');
      }
      
      await tenantService.deleteTenant(id);
      res.status(204).end();
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get tenant statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getTenantStatistics: async (req, res, next) => {
    try {
      const { id } = req.params;
      
      // Check permissions
      if (req.user.role !== 'superadmin' && req.user.role !== 'admin') {
        throw new AuthorizationError('Insufficient permissions to view tenant statistics');
      }
      
      // Regular admins can only view their own tenant stats
      if (req.user.role === 'admin' && req.tenantId !== id) {
        throw new AuthorizationError('Not authorized to view statistics for this tenant');
      }
      
      const statistics = await tenantService.getTenantStatistics(id);
      res.json(statistics);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get tenant branding
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getTenantBranding: async (req, res, next) => {
    try {
      const { id } = req.params;
      
      // All users belonging to the tenant can get branding
      if (req.user.role !== 'superadmin' && req.tenantId !== id) {
        throw new AuthorizationError('Not authorized to access branding for this tenant');
      }
      
      const branding = await tenantService.getTenantBranding(id);
      res.json(branding);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update tenant branding
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateTenantBranding: async (req, res, next) => {
    try {
      const { id } = req.params;
      
      // Check permissions
      if (req.user.role !== 'superadmin' && req.user.role !== 'admin') {
        throw new AuthorizationError('Insufficient permissions to update branding');
      }
      
      // Regular admins can only update their own tenant branding
      if (req.user.role === 'admin' && req.tenantId !== id) {
        throw new AuthorizationError('Not authorized to update branding for this tenant');
      }
      
      const brandingData = req.body;
      const branding = await tenantService.updateTenantBranding(id, brandingData, req.user.id);
      res.json(branding);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Upload tenant logo
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  uploadTenantLogo: async (req, res, next) => {
    try {
      const { id } = req.params;
      
      // Check permissions
      if (req.user.role !== 'superadmin' && req.user.role !== 'admin') {
        throw new AuthorizationError('Insufficient permissions to upload logo');
      }
      
      // Regular admins can only update their own tenant logo
      if (req.user.role === 'admin' && req.tenantId !== id) {
        throw new AuthorizationError('Not authorized to upload logo for this tenant');
      }
      
      if (!req.file) {
        throw new ValidationError('No logo file uploaded');
      }
      
      // Implementation depends on file upload middleware (multer, formidable, etc.)
      // This is just a stub, actual implementation will vary
      const logoUrl = `/uploads/tenant-logos/${id}/${req.file.filename}`;
      
      // Update tenant branding with the new logo URL
      const brandingData = { logoUrl };
      const branding = await tenantService.updateTenantBranding(id, brandingData, req.user.id);
      
      res.json({
        logoUrl,
        message: 'Logo uploaded successfully'
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get tenant users
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getTenantUsers: async (req, res, next) => {
    try {
      const { id } = req.params;
      const { page = 1, limit = 10 } = req.query;
      
      // Check permissions
      if (req.user.role !== 'superadmin' && req.user.role !== 'admin') {
        throw new AuthorizationError('Insufficient permissions to view tenant users');
      }
      
      // Regular admins can only view their own tenant users
      if (req.user.role === 'admin' && req.tenantId !== id) {
        throw new AuthorizationError('Not authorized to view users for this tenant');
      }
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10)
      };
      
      const users = await tenantService.getTenantUsers(id, options);
      res.json(users);
    } catch (error) {
      next(error);
    }
  }
};

module.exports = tenantController;
