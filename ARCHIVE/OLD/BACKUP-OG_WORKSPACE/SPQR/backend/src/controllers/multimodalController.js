/**
 * Multimodal Controller
 * 
 * Controller for handling multimodal AI processing requests,
 * including image, audio, and video analysis using various LLM providers.
 */

const multimodalToolService = require('../services/ai/multimodalToolService');
const logger = require('../utils/logger');
const auditService = require('../services/auditService');

/**
 * Process a multimodal query with text and media
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.processMultimodalQuery = async (req, res) => {
  try {
    const { text, media, provider, modelOptions } = req.body;
    const userId = req.user?.id;
    
    // Log the request
    logger.info('Processing multimodal query', {
      userId,
      provider,
      mediaCount: media?.length || 0,
      textLength: text?.length || 0
    });
    
    // Process the multimodal query
    const result = await multimodalToolService.processMultimodalQuery({
      text,
      media,
      provider: provider || 'claude',
      modelOptions: modelOptions || {}
    });
    
    // Audit the AI decision if audit service is available
    try {
      if (auditService && auditService.logDecision) {
        await auditService.logDecision({
          userId,
          type: 'multimodal-query',
          input: {
            text,
            mediaTypes: media?.map(m => m.type) || []
          },
          output: result.content,
          model: result.model,
          provider: result.provider,
          metadata: {
            usage: result.usage
          }
        });
      }
    } catch (auditError) {
      logger.warn('Failed to audit multimodal query', { error: auditError.message });
    }
    
    res.json({
      success: true,
      result
    });
  } catch (error) {
    logger.error(`Error processing multimodal query: ${error.message}`, { error });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Process a multimodal query with uploaded files
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.processMultimodalQueryWithFiles = async (req, res) => {
  try {
    const { text, provider, modelOptions } = req.body;
    const files = req.files || [];
    const userId = req.user?.id;
    
    // Convert files to media objects
    const media = files.map(file => {
      const type = file.mimetype.startsWith('image/') ? 'image' :
                  file.mimetype.startsWith('audio/') ? 'audio' :
                  file.mimetype.startsWith('video/') ? 'video' : 'unknown';
      
      return {
        type,
        path: file.path,
        mimeType: file.mimetype,
        filename: file.originalname
      };
    });
    
    // Add any media URLs from the request body
    if (req.body.mediaUrls && Array.isArray(JSON.parse(req.body.mediaUrls))) {
      const mediaUrls = JSON.parse(req.body.mediaUrls);
      
      for (const mediaUrl of mediaUrls) {
        media.push({
          type: mediaUrl.type || 'image',
          url: mediaUrl.url,
          mimeType: mediaUrl.mimeType
        });
      }
    }
    
    // Log the request
    logger.info('Processing multimodal query with files', {
      userId,
      provider,
      fileCount: files.length,
      mediaCount: media.length,
      textLength: text?.length || 0
    });
    
    // Process the multimodal query
    const parsedModelOptions = modelOptions ? JSON.parse(modelOptions) : {};
    const result = await multimodalToolService.processMultimodalQuery({
      text,
      media,
      provider: provider || 'claude',
      modelOptions: parsedModelOptions
    });
    
    // Audit the AI decision if audit service is available
    try {
      if (auditService && auditService.logDecision) {
        await auditService.logDecision({
          userId,
          type: 'multimodal-query-files',
          input: {
            text,
            mediaTypes: media.map(m => m.type),
            fileNames: files.map(f => f.originalname)
          },
          output: result.content,
          model: result.model,
          provider: result.provider,
          metadata: {
            usage: result.usage
          }
        });
      }
    } catch (auditError) {
      logger.warn('Failed to audit multimodal query with files', { error: auditError.message });
    }
    
    // Clean up temporary files
    const filePaths = files.map(file => file.path);
    multimodalToolService.cleanupTempFiles(filePaths).catch(error => {
      logger.warn(`Error cleaning up temp files: ${error.message}`);
    });
    
    res.json({
      success: true,
      result
    });
  } catch (error) {
    logger.error(`Error processing multimodal query with files: ${error.message}`, { error });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Get supported multimodal providers and capabilities
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getMultimodalCapabilities = async (req, res) => {
  try {
    // This could be expanded to dynamically check provider capabilities
    const capabilities = {
      providers: [
        {
          id: 'claude',
          name: 'Claude',
          models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
          supportedMediaTypes: ['image'],
          maxMediaItems: 5
        },
        {
          id: 'openai',
          name: 'OpenAI',
          models: ['gpt-4-vision-preview'],
          supportedMediaTypes: ['image'],
          maxMediaItems: 10
        },
        {
          id: 'gemini',
          name: 'Google Gemini',
          models: ['gemini-1.5-pro', 'gemini-1.0-pro'],
          supportedMediaTypes: ['image'],
          maxMediaItems: 16
        }
      ],
      supportedMediaTypes: ['image', 'audio', 'video'],
      maxFileSize: '10MB',
      maxFilesPerRequest: 5
    };
    
    res.json({
      success: true,
      capabilities
    });
  } catch (error) {
    logger.error(`Error getting multimodal capabilities: ${error.message}`, { error });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};
