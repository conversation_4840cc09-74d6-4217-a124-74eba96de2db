/**
 * Chat Controller
 * Manages communication between users and AI via WebSockets and REST API
 */

const { StatusCodes } = require('http-status-codes');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const chatService = require('../services/chatService');

/**
 * Initializes a new chat session
 * @param {Object} req - Express Request object
 * @param {Object} res - Express Response object
 */
async function initializeChat(req, res) {
  try {
    const { userId, roles } = req.user;
    const { context } = req.body || {};

    // Create new chat session
    const sessionId = uuidv4();
    const session = await chatService.createSession(sessionId, userId, context);

    return res.status(StatusCodes.CREATED).json({
      success: true,
      data: {
        sessionId: session.id,
        createdAt: session.createdAt
      }
    });
  } catch (error) {
    logger.error(`Error initializing chat: ${error.message}`, { error });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error initializing chat',
      error: error.message
    });
  }
}

/**
 * Processes a chat message
 * @param {Object} req - Express Request object
 * @param {Object} res - Express Response object
 */
async function processMessage(req, res) {
  try {
    const { userId, roles } = req.user;
    const { sessionId, message, context } = req.body;

    if (!sessionId || !message) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'SessionId and message are required'
      });
    }

    // Process message
    const response = await chatService.processMessage(sessionId, userId, message, context, roles);

    return res.status(StatusCodes.OK).json({
      success: true,
      data: response
    });
  } catch (error) {
    logger.error(`Error processing chat message: ${error.message}`, { error });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error processing chat message',
      error: error.message
    });
  }
}

/**
 * Retrieves chat history for a session
 * @param {Object} req - Express Request object
 * @param {Object} res - Express Response object
 */
async function getChatHistory(req, res) {
  try {
    const { userId } = req.user;
    const { sessionId } = req.params;

    const history = await chatService.getChatHistory(sessionId, userId);

    return res.status(StatusCodes.OK).json({
      success: true,
      data: history
    });
  } catch (error) {
    logger.error(`Error retrieving chat history: ${error.message}`, { error });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving chat history',
      error: error.message
    });
  }
}

/**
 * Retrieves all chat sessions for a user
 * @param {Object} req - Express Request object
 * @param {Object} res - Express Response object
 */
async function getUserSessions(req, res) {
  try {
    const { userId } = req.user;
    const sessions = await chatService.getUserSessions(userId);

    return res.status(StatusCodes.OK).json({
      success: true,
      data: sessions
    });
  } catch (error) {
    logger.error(`Error retrieving user sessions: ${error.message}`, { error });
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving user sessions',
      error: error.message
    });
  }
}

/**
 * WebSocket handler for chat messages
 * @param {Object} ws - WebSocket connection
 * @param {Object} req - Express Request object
 */
function handleWebSocketConnection(ws, req) {
  const { userId, roles } = req.user;
  
  ws.on('message', async (message) => {
    try {
      const data = JSON.parse(message);
      const { type, sessionId, content, context } = data;

      if (type === 'message') {
        const response = await chatService.processMessage(sessionId, userId, content, context, roles);
        
        ws.send(JSON.stringify({
          type: 'response',
          sessionId,
          content: response.content,
          actions: response.actions || []
        }));
      } else if (type === 'init') {
        const session = await chatService.getOrCreateSession(sessionId || uuidv4(), userId, context);
        
        ws.send(JSON.stringify({
          type: 'init_response',
          sessionId: session.id,
          createdAt: session.createdAt
        }));
      }
    } catch (error) {
      logger.error(`WebSocket error: ${error.message}`, { error });
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Error processing message',
        error: error.message
      }));
    }
  });

  ws.on('close', () => {
    logger.info(`WebSocket connection closed for user ${userId}`);
  });
}

module.exports = {
  initializeChat,
  processMessage,
  getChatHistory,
  getUserSessions,
  handleWebSocketConnection
};
