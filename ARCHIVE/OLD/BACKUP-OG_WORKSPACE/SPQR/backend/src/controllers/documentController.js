const documentService = require('../services/documentService');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Temporäres Verzeichnis für hochgeladene Dateien
const uploadDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Multer-Konfiguration für den Datei-Upload
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueFilename = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueFilename);
  }
});

// Filter für unterstützte Dateiformate
const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'application/pdf', 
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/html',
    'text/markdown',
    'application/json',
    'text/csv',
    'application/javascript',
    'application/x-javascript',
    'text/javascript'
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Nicht unterstützter Dateityp: ${file.mimetype}`), false);
  }
};

// Multer-Upload-Konfiguration
const upload = multer({ 
  storage: storage,
  limits: { 
    fileSize: 20 * 1024 * 1024 // 20MB maximale Dateigröße
  },
  fileFilter: fileFilter
});

/**
 * Controller für die Dokumentenverarbeitung
 */
const documentController = {
  /**
   * Verarbeitet hochgeladene Dokumente und erstellt Einbettungen
   * @param {Object} req - Express-Request-Objekt
   * @param {Object} res - Express-Response-Objekt
   * @returns {Promise<void>}
   */
  async processDocument(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({ 
          success: false, 
          message: 'Keine Datei hochgeladen' 
        });
      }

      const filePath = req.file.path;
      const metadata = {
        originalName: req.file.originalname,
        mimeType: req.file.mimetype,
        size: req.file.size,
        uploadedBy: req.user.id,
        tags: req.body.tags ? JSON.parse(req.body.tags) : [],
        customMetadata: req.body.metadata ? JSON.parse(req.body.metadata) : {}
      };

      // Dokument verarbeiten und Chunks mit Einbettungen erstellen
      const documentId = await documentService.processDocument(filePath, metadata);

      // Temporäre Datei löschen
      fs.unlinkSync(filePath);

      res.json({
        success: true,
        message: 'Dokument erfolgreich verarbeitet',
        documentId
      });
    } catch (error) {
      console.error('Fehler beim Verarbeiten des Dokuments:', error);
      
      // Temporäre Datei bereinigen, falls vorhanden
      if (req.file && req.file.path && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      
      res.status(500).json({
        success: false,
        message: `Fehler beim Verarbeiten des Dokuments: ${error.message}`
      });
    }
  },

  /**
   * Löscht ein Dokument und seine Einbettungen
   * @param {Object} req - Express-Request-Objekt
   * @param {Object} res - Express-Response-Objekt
   * @returns {Promise<void>}
   */
  async deleteDocument(req, res) {
    try {
      const { documentId } = req.params;

      if (!documentId) {
        return res.status(400).json({
          success: false,
          message: 'Dokument-ID ist erforderlich'
        });
      }

      await documentService.deleteDocumentEmbeddings(documentId);

      res.json({
        success: true,
        message: 'Dokument und alle zugehörigen Einbettungen wurden gelöscht'
      });
    } catch (error) {
      console.error('Fehler beim Löschen des Dokuments:', error);
      res.status(500).json({
        success: false,
        message: `Fehler beim Löschen des Dokuments: ${error.message}`
      });
    }
  },

  /**
   * Batch-Upload-Controller
   * @param {Object} req - Express-Request-Objekt
   * @param {Object} res - Express-Response-Objekt
   * @returns {Promise<void>}
   */
  async processBatchDocuments(req, res) {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Keine Dateien hochgeladen'
        });
      }

      const processingResults = [];
      const errors = [];

      // Verarbeite jede Datei
      for (const file of req.files) {
        try {
          const filePath = file.path;
          const metadata = {
            originalName: file.originalname,
            mimeType: file.mimetype,
            size: file.size,
            uploadedBy: req.user.id,
            batchUpload: true,
            tags: req.body.tags ? JSON.parse(req.body.tags) : [],
            customMetadata: req.body.metadata ? JSON.parse(req.body.metadata) : {}
          };

          const documentId = await documentService.processDocument(filePath, metadata);
          
          // Temporäre Datei löschen
          fs.unlinkSync(filePath);
          
          processingResults.push({
            filename: file.originalname,
            documentId,
            status: 'success'
          });
        } catch (error) {
          console.error(`Fehler beim Verarbeiten von ${file.originalname}:`, error);
          
          // Temporäre Datei bereinigen
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
          
          errors.push({
            filename: file.originalname,
            error: error.message,
            status: 'error'
          });
        }
      }

      res.json({
        success: true,
        message: `${processingResults.length} Dokumente erfolgreich verarbeitet, ${errors.length} fehlgeschlagen`,
        results: processingResults,
        errors
      });
    } catch (error) {
      console.error('Fehler beim Batch-Verarbeiten von Dokumenten:', error);
      
      // Bereinige alle temporären Dateien
      if (req.files && req.files.length > 0) {
        for (const file of req.files) {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        }
      }
      
      res.status(500).json({
        success: false,
        message: `Fehler beim Batch-Verarbeiten: ${error.message}`
      });
    }
  }
};

module.exports = {
  documentController,
  upload // Multer-Upload-Middleware exportieren
};
