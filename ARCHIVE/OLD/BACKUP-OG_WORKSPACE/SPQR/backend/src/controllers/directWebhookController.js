/**
 * Direct Webhook Controller
 * 
 * This controller handles the direct webhook integration endpoints, including
 * webhook registration, triggering, and VAPI webhook handling.
 */

const directWebhookService = require('../services/webhooks/directWebhookService');
const { v4: uuidv4 } = require('uuid');

/**
 * Register a new webhook
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.registerWebhook = async (req, res) => {
  try {
    const { name, url, event, headers, config } = req.body;
    
    // Validate required fields
    if (!name || !url || !event) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: name, url, and event are required'
      });
    }
    
    // Register webhook
    const result = await directWebhookService.registerWebhook({
      name,
      url,
      event,
      headers,
      config
    });
    
    res.status(201).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error registering webhook:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Get a webhook by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getWebhook = async (req, res) => {
  try {
    const { id } = req.params;
    
    const webhook = await directWebhookService.getWebhook(id);
    
    res.json({
      success: true,
      data: webhook
    });
  } catch (error) {
    console.error(`Error retrieving webhook ${req.params.id}:`, error);
    res.status(404).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Update a webhook
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateWebhook = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    
    // Prevent updating certain fields
    delete updates.id;
    delete updates.secret;
    delete updates.createdAt;
    delete updates.updatedAt;
    
    const success = await directWebhookService.updateWebhook(id, updates);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        error: `Webhook not found: ${id}`
      });
    }
    
    res.json({
      success: true,
      message: `Webhook ${id} updated successfully`
    });
  } catch (error) {
    console.error(`Error updating webhook ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Delete a webhook
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteWebhook = async (req, res) => {
  try {
    const { id } = req.params;
    
    const success = await directWebhookService.deleteWebhook(id);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        error: `Webhook not found: ${id}`
      });
    }
    
    res.json({
      success: true,
      message: `Webhook ${id} deleted successfully`
    });
  } catch (error) {
    console.error(`Error deleting webhook ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * List all webhooks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listWebhooks = async (req, res) => {
  try {
    const { event } = req.query;
    
    const webhooks = await directWebhookService.listWebhooks(event);
    
    res.json({
      success: true,
      data: webhooks
    });
  } catch (error) {
    console.error('Error listing webhooks:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Trigger webhooks for an event
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.triggerWebhooks = async (req, res) => {
  try {
    const { event } = req.params;
    const payload = req.body;
    
    if (!event) {
      return res.status(400).json({
        success: false,
        error: 'Event parameter is required'
      });
    }
    
    const results = await directWebhookService.triggerWebhooks(event, payload);
    
    res.json({
      success: true,
      data: {
        event,
        results
      }
    });
  } catch (error) {
    console.error(`Error triggering webhooks for event ${req.params.event}:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Handle VAPI webhook
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleVapiWebhook = async (req, res) => {
  try {
    const signature = req.headers['x-vapi-signature'];
    const payload = req.body;
    
    // Validate signature
    if (!signature || !directWebhookService.validateVapiSignature(signature, payload)) {
      return res.status(401).json({
        success: false,
        error: 'Invalid signature'
      });
    }
    
    // Extract tool name and parameters
    const { tool, parameters } = payload;
    
    if (!tool) {
      return res.status(400).json({
        success: false,
        error: 'Tool name is required'
      });
    }
    
    // Find tool handler
    const toolHandler = await getToolHandler(tool);
    
    if (!toolHandler) {
      return res.status(404).json({
        success: false,
        error: `Tool not found: ${tool}`
      });
    }
    
    // Execute tool
    const startTime = Date.now();
    let result;
    let success = true;
    let error = null;
    
    try {
      result = await toolHandler(parameters || {});
    } catch (err) {
      success = false;
      error = err.message;
      console.error(`Error executing VAPI tool ${tool}:`, err);
    }
    
    const executionTime = Date.now() - startTime;
    
    // Log execution
    await logVapiExecution(tool, payload, result, success, error, executionTime);
    
    if (!success) {
      return res.status(500).json({
        success: false,
        error: error || 'Unknown error'
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error handling VAPI webhook:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Confirm webhook delivery
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.confirmDelivery = async (req, res) => {
  try {
    const { deliveryId } = req.params;
    
    // Update delivery status
    const result = await updateDeliveryStatus(deliveryId, 'delivered');
    
    if (!result) {
      return res.status(404).json({
        success: false,
        error: `Delivery not found: ${deliveryId}`
      });
    }
    
    res.json({
      success: true,
      message: `Delivery ${deliveryId} confirmed`
    });
  } catch (error) {
    console.error(`Error confirming delivery ${req.params.deliveryId}:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

/**
 * Get tool handler by name
 * @param {string} toolName - Tool name
 * @returns {Function|null} - Tool handler function
 */
async function getToolHandler(toolName) {
  try {
    // Query database for tool
    const pool = require('../db');
    const result = await pool.query(
      'SELECT * FROM vapi_webhook_tools WHERE name = $1 AND enabled = TRUE',
      [toolName]
    );
    
    if (result.rows.length === 0) {
      return null;
    }
    
    const tool = result.rows[0];
    
    // Get handler function
    const handlerPath = tool.handler;
    
    try {
      // Dynamic import of handler
      const handler = require(`../tools/${handlerPath}`);
      return handler;
    } catch (error) {
      console.error(`Error loading tool handler ${handlerPath}:`, error);
      return null;
    }
  } catch (error) {
    console.error(`Error getting tool handler for ${toolName}:`, error);
    return null;
  }
}

/**
 * Log VAPI webhook execution
 * @param {string} toolName - Tool name
 * @param {Object} request - Request data
 * @param {Object} response - Response data
 * @param {boolean} success - Success indicator
 * @param {string} error - Error message
 * @param {number} executionTime - Execution time in milliseconds
 */
async function logVapiExecution(toolName, request, response, success, error, executionTime) {
  try {
    const pool = require('../db');
    
    // Get tool ID
    const toolResult = await pool.query(
      'SELECT id FROM vapi_webhook_tools WHERE name = $1',
      [toolName]
    );
    
    if (toolResult.rows.length === 0) {
      console.error(`Tool not found for logging: ${toolName}`);
      return;
    }
    
    const toolId = toolResult.rows[0].id;
    
    // Log execution
    await pool.query(
      'INSERT INTO vapi_webhook_executions (id, tool_id, request, response, success, error, execution_time) ' +
      'VALUES ($1, $2, $3, $4, $5, $6, $7)',
      [uuidv4(), toolId, request, response, success, error, executionTime]
    );
  } catch (error) {
    console.error('Error logging VAPI execution:', error);
  }
}

/**
 * Update delivery status
 * @param {string} deliveryId - Delivery ID
 * @param {string} status - New status
 * @returns {boolean} - Success indicator
 */
async function updateDeliveryStatus(deliveryId, status) {
  try {
    const pool = require('../db');
    
    const result = await pool.query(
      'UPDATE webhook_deliveries SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [status, deliveryId]
    );
    
    return result.rowCount > 0;
  } catch (error) {
    console.error(`Error updating delivery status for ${deliveryId}:`, error);
    return false;
  }
}
