/**
 * <PERSON><PERSON>er Controller
 * 
 * This controller provides API endpoints for browser automation tasks
 * using the browser-use integration.
 */

const browserAutomationService = require('../services/browser/browserAutomationService');
const logger = require('winston');

/**
 * Create a new browser automation task
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.createTask = async (req, res) => {
  try {
    const { instruction, sessionId, context } = req.body;
    const userId = req.user.id; // Assuming authentication middleware sets req.user
    
    if (!instruction) {
      return res.status(400).json({ error: 'Instruction is required' });
    }
    
    const task = await browserAutomationService.createTask({
      instruction,
      userId,
      sessionId,
      context: context || {}
    });
    
    res.status(201).json({
      success: true,
      taskId: task.taskId,
      sessionId: task.sessionId,
      status: task.status
    });
  } catch (error) {
    logger.error(`Error creating browser task: ${error.message}`);
    res.status(500).json({ error: `Failed to create browser task: ${error.message}` });
  }
};

/**
 * Get a task's status and result
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.getTaskStatus = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;
    
    // Get the task status
    const taskStatus = await browserAutomationService.getTaskStatus(taskId);
    
    // Check if the task belongs to the user (in a full implementation)
    // For now, we'll assume the service handles this check
    
    res.status(200).json({
      success: true,
      task: taskStatus
    });
  } catch (error) {
    logger.error(`Error getting task status: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Task not found' });
    }
    
    res.status(500).json({ error: 'Failed to get task status: ${error.message}' });
  }
};

/**
 * Get a screenshot from a task
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.getTaskScreenshot = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;
    
    // Get the screenshot
    const screenshot = await browserAutomationService.getTaskScreenshot(taskId);
    
    // Check if screenshot is a base64 string
    if (screenshot.startsWith('data:image')) {
      // Send as JSON
      return res.status(200).json({
        success: true,
        screenshot
      });
    } else {
      // Set content type and send raw image
      res.setHeader('Content-Type', 'image/png');
      return res.status(200).send(Buffer.from(screenshot, 'base64'));
    }
  } catch (error) {
    logger.error(`Error getting task screenshot: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Task or screenshot not found' });
    }
    
    res.status(500).json({ error: 'Failed to get task screenshot: ${error.message}' });
  }
};

/**
 * Continue a browser session with new instructions
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.continueSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { instruction } = req.body;
    const userId = req.user.id;
    
    if (!instruction) {
      return res.status(400).json({ error: 'Instruction is required' });
    }
    
    // Continue the session
    const task = await browserAutomationService.continueSession(sessionId, instruction, userId);
    
    res.status(200).json({
      success: true,
      taskId: task.taskId,
      sessionId: task.sessionId,
      status: task.status
    });
  } catch (error) {
    logger.error(`Error continuing session: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Session not found' });
    }
    
    res.status(500).json({ error: 'Failed to continue session: ${error.message}' });
  }
};

/**
 * List all tasks for a user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.listUserTasks = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get all tasks for the user
    const tasks = await browserAutomationService.listUserTasks(userId);
    
    res.status(200).json({
      success: true,
      tasks
    });
  } catch (error) {
    logger.error(`Error listing user tasks: ${error.message}`);
    res.status(500).json({ error: 'Failed to list user tasks: ${error.message}' });
  }
};

/**
 * List all sessions for a user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.listUserSessions = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get all sessions for the user
    const sessions = await browserAutomationService.listUserSessions(userId);
    
    res.status(200).json({
      success: true,
      sessions
    });
  } catch (error) {
    logger.error(`Error listing user sessions: ${error.message}`);
    res.status(500).json({ error: 'Failed to list user sessions: ${error.message}' });
  }
};

/**
 * Cancel a running task
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.cancelTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;
    
    // Cancel the task
    const result = await browserAutomationService.cancelTask(taskId, userId);
    
    res.status(200).json({
      success: true,
      cancelled: result
    });
  } catch (error) {
    logger.error(`Error cancelling task: ${error.message}`);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: 'Task not found' });
    }
    
    if (error.message.includes('does not belong')) {
      return res.status(403).json({ error: 'You do not have permission to cancel this task' });
    }
    
    res.status(500).json({ error: 'Failed to cancel task: ${error.message}' });
  }
};
