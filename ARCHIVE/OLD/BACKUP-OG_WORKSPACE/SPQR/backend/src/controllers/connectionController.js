/**
 * Connection Controller
 * 
 * Handles HTTP requests for connections, endpoints, workflows, and tools.
 * Provides interface for the connection agent functionality.
 */

const connectionRegistry = require('../services/connectionRegistryService');
const connectionAgent = require('../services/connectionAgentService');
const { ValidationError } = require('../utils/errors');
const logger = require('winston');

/**
 * Get all connections
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllConnections = async (req, res) => {
  try {
    const { type, limit = 50, offset = 0, includeInactive = false } = req.query;
    
    let connections;
    if (type) {
      connections = await connectionRegistry.findConnectionsByType(
        type, 
        { limit: parseInt(limit), offset: parseInt(offset), includeInactive: includeInactive === 'true' }
      );
    } else {
      // Use the Connection model directly for more flexibility
      const { Connection } = req.app.get('models');
      const query = {
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['createdAt', 'DESC']]
      };
      
      if (!includeInactive || includeInactive !== 'true') {
        query.where = { status: 'active' };
      }
      
      connections = await Connection.findAll(query);
    }
    
    res.json({ connections });
  } catch (error) {
    logger.error(`Error getting connections: ${error.message}`);
    res.status(500).json({ error: 'Failed to get connections', message: error.message });
  }
};

/**
 * Get a connection by ID
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConnection = async (req, res) => {
  try {
    const { id } = req.params;
    const { includeEndpoints, includeWorkflows, includeCredentials, includeTools } = req.query;
    
    // Get models
    const { 
      Connection, 
      ConnectionEndpoint, 
      UiAutomationWorkflow,
      ConnectionCredential,
      ConnectionTool
    } = req.app.get('models');
    
    // Build include array
    const include = [];
    
    if (includeEndpoints === 'true') {
      include.push({ model: ConnectionEndpoint, as: 'endpoints' });
    }
    
    if (includeWorkflows === 'true') {
      include.push({ model: UiAutomationWorkflow, as: 'workflows' });
    }
    
    if (includeCredentials === 'true' && req.user.isAdmin) {
      // Only admins can see credential info (but not actual secret values)
      include.push({ 
        model: ConnectionCredential, 
        as: 'credentials',
        attributes: { exclude: ['encryptedData'] }
      });
    }
    
    if (includeTools === 'true') {
      include.push({ model: ConnectionTool, as: 'tools' });
    }
    
    const connection = await Connection.findByPk(id, { include });
    
    if (!connection) {
      return res.status(404).json({ error: 'Connection not found' });
    }
    
    res.json({ connection });
  } catch (error) {
    logger.error(`Error getting connection: ${error.message}`);
    res.status(500).json({ error: 'Failed to get connection', message: error.message });
  }
};

/**
 * Create a new connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createConnection = async (req, res) => {
  try {
    const { name, description, type, systemUrl, documentationUrl, apiBaseUrl, apiVersion, authType, config, credentials } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }
    
    if (!type) {
      return res.status(400).json({ error: 'Type is required' });
    }
    
    // Create connection
    const connection = await connectionRegistry.createConnection({
      name,
      description,
      type,
      status: 'active',
      systemUrl,
      documentationUrl,
      apiBaseUrl,
      apiVersion,
      authType,
      config: config || {},
      createdBy: req.user.id
    }, credentials);
    
    res.status(201).json({ connection });
  } catch (error) {
    logger.error(`Error creating connection: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to create connection', message: error.message });
  }
};

/**
 * Update a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateConnection = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, status, systemUrl, documentationUrl, apiBaseUrl, apiVersion, authType, config } = req.body;
    
    // Get the connection
    const { Connection } = req.app.get('models');
    const connection = await Connection.findByPk(id);
    
    if (!connection) {
      return res.status(404).json({ error: 'Connection not found' });
    }
    
    // Update fields
    if (name !== undefined) connection.name = name;
    if (description !== undefined) connection.description = description;
    if (status !== undefined) connection.status = status;
    if (systemUrl !== undefined) connection.systemUrl = systemUrl;
    if (documentationUrl !== undefined) connection.documentationUrl = documentationUrl;
    if (apiBaseUrl !== undefined) connection.apiBaseUrl = apiBaseUrl;
    if (apiVersion !== undefined) connection.apiVersion = apiVersion;
    if (authType !== undefined) connection.authType = authType;
    if (config !== undefined) connection.config = config;
    
    await connection.save();
    
    res.json({ connection });
  } catch (error) {
    logger.error(`Error updating connection: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to update connection', message: error.message });
  }
};

/**
 * Delete a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteConnection = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get the connection
    const { Connection } = req.app.get('models');
    const connection = await Connection.findByPk(id);
    
    if (!connection) {
      return res.status(404).json({ error: 'Connection not found' });
    }
    
    // Check if user is admin or created the connection
    if (!req.user.isAdmin && connection.createdBy !== req.user.id) {
      return res.status(403).json({ error: 'You do not have permission to delete this connection' });
    }
    
    await connection.destroy();
    
    res.json({ message: 'Connection deleted successfully' });
  } catch (error) {
    logger.error(`Error deleting connection: ${error.message}`);
    res.status(500).json({ error: 'Failed to delete connection', message: error.message });
  }
};

/**
 * Get endpoints for a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConnectionEndpoints = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get the connection with endpoints
    const { Connection, ConnectionEndpoint } = req.app.get('models');
    const connection = await Connection.findByPk(id, {
      include: [{ model: ConnectionEndpoint, as: 'endpoints' }]
    });
    
    if (!connection) {
      return res.status(404).json({ error: 'Connection not found' });
    }
    
    res.json({ endpoints: connection.endpoints });
  } catch (error) {
    logger.error(`Error getting connection endpoints: ${error.message}`);
    res.status(500).json({ error: 'Failed to get endpoints', message: error.message });
  }
};

/**
 * Add an endpoint to a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addConnectionEndpoint = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, description, path, method, requestSchema, 
      responseSchema, parameters, headers, requiresAuth 
    } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }
    
    if (!path) {
      return res.status(400).json({ error: 'Path is required' });
    }
    
    if (!method) {
      return res.status(400).json({ error: 'Method is required' });
    }
    
    // Add the endpoint
    const endpoint = await connectionRegistry.addEndpoint(id, {
      name,
      description,
      path,
      method,
      requestSchema,
      responseSchema,
      parameters,
      headers,
      requiresAuth
    });
    
    res.status(201).json({ endpoint });
  } catch (error) {
    logger.error(`Error adding connection endpoint: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to add endpoint', message: error.message });
  }
};

/**
 * Get workflows for a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConnectionWorkflows = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get the connection with workflows
    const { Connection, UiAutomationWorkflow } = req.app.get('models');
    const connection = await Connection.findByPk(id, {
      include: [{ model: UiAutomationWorkflow, as: 'workflows' }]
    });
    
    if (!connection) {
      return res.status(404).json({ error: 'Connection not found' });
    }
    
    res.json({ workflows: connection.workflows });
  } catch (error) {
    logger.error(`Error getting connection workflows: ${error.message}`);
    res.status(500).json({ error: 'Failed to get workflows', message: error.message });
  }
};

/**
 * Add a workflow to a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addConnectionWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, description, entryUrl, steps, 
      parameters, expectedOutput, status, metadata 
    } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }
    
    if (!entryUrl) {
      return res.status(400).json({ error: 'Entry URL is required' });
    }
    
    if (!steps || !Array.isArray(steps)) {
      return res.status(400).json({ error: 'Steps array is required' });
    }
    
    // Add the workflow
    const workflow = await connectionRegistry.addWorkflow(id, {
      name,
      description,
      entryUrl,
      steps,
      parameters,
      expectedOutput,
      status: status || 'active',
      metadata
    });
    
    res.status(201).json({ workflow });
  } catch (error) {
    logger.error(`Error adding connection workflow: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to add workflow', message: error.message });
  }
};

/**
 * Test a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.testConnection = async (req, res) => {
  try {
    const { id } = req.params;
    const { testEndpoints, testWorkflows } = req.body;
    
    // Generate test report
    const report = await connectionAgent.testConnectionAndGenerateReport({
      connectionId: id,
      testEndpoints,
      testWorkflows,
      userId: req.user.id
    });
    
    res.json({ report });
  } catch (error) {
    logger.error(`Error testing connection: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to test connection', message: error.message });
  }
};

/**
 * Get tools for a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConnectionTools = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get the connection with tools
    const { Connection, ConnectionTool, Tool } = req.app.get('models');
    const connection = await Connection.findByPk(id, {
      include: [{ 
        model: ConnectionTool, 
        as: 'tools',
        include: [{ model: Tool, as: 'tool' }]
      }]
    });
    
    if (!connection) {
      return res.status(404).json({ error: 'Connection not found' });
    }
    
    res.json({ tools: connection.tools });
  } catch (error) {
    logger.error(`Error getting connection tools: ${error.message}`);
    res.status(500).json({ error: 'Failed to get tools', message: error.message });
  }
};

/**
 * Generate tools for a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.generateConnectionTools = async (req, res) => {
  try {
    const { id } = req.params;
    const { endpointIds, workflowIds, generateAll } = req.body;
    
    // Generate tools
    const tools = await connectionAgent.generateConnectionTools({
      connectionId: id,
      endpointIds,
      workflowIds,
      generateAll,
      userId: req.user.id
    });
    
    res.json({ tools });
  } catch (error) {
    logger.error(`Error generating connection tools: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to generate tools', message: error.message });
  }
};

/**
 * Analyze API documentation for a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.analyzeApiDocumentation = async (req, res) => {
  try {
    const { name, documentationUrl, apiBaseUrl, apiVersion, authType } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }
    
    if (!documentationUrl) {
      return res.status(400).json({ error: 'Documentation URL is required' });
    }
    
    // Analyze API documentation
    const result = await connectionAgent.analyzeApiDocumentation({
      name,
      documentationUrl,
      apiBaseUrl,
      apiVersion,
      authType,
      userId: req.user.id
    });
    
    res.status(201).json(result);
  } catch (error) {
    logger.error(`Error analyzing API documentation: ${error.message}`);
    res.status(500).json({ error: 'Failed to analyze API documentation', message: error.message });
  }
};

/**
 * Design a UI workflow for a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.designUiWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, entryUrl, task, parametersInfo, outputInfo } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }
    
    if (!entryUrl) {
      return res.status(400).json({ error: 'Entry URL is required' });
    }
    
    if (!task) {
      return res.status(400).json({ error: 'Task description is required' });
    }
    
    // Design workflow
    const result = await connectionAgent.designUiWorkflow({
      connectionId: id,
      name,
      description,
      entryUrl,
      task,
      parametersInfo,
      outputInfo,
      userId: req.user.id
    });
    
    res.status(201).json(result);
  } catch (error) {
    logger.error(`Error designing UI workflow: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to design UI workflow', message: error.message });
  }
};

/**
 * Get credentials for a connection
 * Restricted to admins
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConnectionCredentials = async (req, res) => {
  try {
    // Only allow admins
    if (!req.user.isAdmin) {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    const { id } = req.params;
    
    // Get the connection with credentials
    const { Connection, ConnectionCredential } = req.app.get('models');
    const connection = await Connection.findByPk(id, {
      include: [{ 
        model: ConnectionCredential, 
        as: 'credentials',
        attributes: { exclude: ['encryptedData'] } // Never return encrypted data
      }]
    });
    
    if (!connection) {
      return res.status(404).json({ error: 'Connection not found' });
    }
    
    res.json({ credentials: connection.credentials });
  } catch (error) {
    logger.error(`Error getting connection credentials: ${error.message}`);
    res.status(500).json({ error: 'Failed to get credentials', message: error.message });
  }
};

/**
 * Add credentials to a connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addConnectionCredentials = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, type, rawData, isDefault, expiresAt, metadata } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }
    
    if (!type) {
      return res.status(400).json({ error: 'Type is required' });
    }
    
    if (!rawData) {
      return res.status(400).json({ error: 'Raw credential data is required' });
    }
    
    // Add credentials
    const credential = await connectionRegistry.addCredentials(id, {
      name,
      type,
      rawData,
      isDefault: isDefault !== false,
      expiresAt,
      metadata
    });
    
    // Return credential without sensitive data
    res.status(201).json({
      credential: {
        id: credential.id,
        connectionId: credential.connectionId,
        name: credential.name,
        type: credential.type,
        isDefault: credential.isDefault,
        expiresAt: credential.expiresAt,
        metadata: credential.metadata,
        createdAt: credential.createdAt,
        updatedAt: credential.updatedAt
      }
    });
  } catch (error) {
    logger.error(`Error adding connection credentials: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to add credentials', message: error.message });
  }
};

/**
 * Get connection metrics
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConnectionMetrics = async (req, res) => {
  try {
    const { id } = req.params;
    const { period = 'day' } = req.query;
    
    // Validate period
    if (!['day', 'week', 'month'].includes(period)) {
      return res.status(400).json({ error: 'Invalid period. Must be day, week, or month' });
    }
    
    // Get metrics
    const metrics = await connectionRegistry.getConnectionMetrics(id, period);
    
    res.json({ metrics });
  } catch (error) {
    logger.error(`Error getting connection metrics: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to get metrics', message: error.message });
  }
};

/**
 * Get connection usage statistics
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConnectionUsage = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get usage statistics
    const usage = await connectionRegistry.getConnectionUsage(id);
    
    res.json({ usage });
  } catch (error) {
    logger.error(`Error getting connection usage: ${error.message}`);
    
    if (error instanceof ValidationError) {
      return res.status(400).json({ error: 'Validation error', message: error.message });
    }
    
    res.status(500).json({ error: 'Failed to get usage statistics', message: error.message });
  }
};
