const metricService = require('../services/metricService');
const { StatusCodes } = require('http-status-codes');
const { Op } = require('sequelize');
const moment = require('moment');

/**
 * Controller for metrics API endpoints
 */
class MetricController {
  /**
   * Get aggregated metrics for a given time period and category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async getAggregatedMetrics(req, res, next) {
    try {
      const { category, timePeriod } = req.params;
      const { startDate, endDate, aggregationType = 'avg' } = req.query;
      
      // Validate permissions
      const hasAccess = await metricService.hasPermission(
        req.user.id,
        req.user.roleId,
        category,
        'view'
      );
      
      if (!hasAccess) {
        return res.status(StatusCodes.FORBIDDEN).json({
          success: false,
          message: 'You do not have permission to view these metrics'
        });
      }
      
      // Convert dates
      const start = startDate ? moment(startDate).toDate() : moment().subtract(30, 'days').toDate();
      const end = endDate ? moment(endDate).toDate() : moment().toDate();
      
      const metrics = await metricService.getMetricsByTimePeriod(
        category === 'aggregated' ? 'aggregated' : category,
        timePeriod || 'daily',
        start,
        end
      );
      
      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get real-time metrics for a specific category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async getRealtimeMetrics(req, res, next) {
    try {
      const { category } = req.params;
      const { limit = 100 } = req.query;
      
      // Validate permissions
      const hasAccess = await metricService.hasPermission(
        req.user.id,
        req.user.roleId,
        category,
        'view'
      );
      
      if (!hasAccess) {
        return res.status(StatusCodes.FORBIDDEN).json({
          success: false,
          message: 'You do not have permission to view these metrics'
        });
      }
      
      const metrics = await metricService.getRealtimeMetrics(category, parseInt(limit, 10));
      
      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Record a metric
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async recordMetric(req, res, next) {
    try {
      const { category } = req.params;
      
      // Validate permissions - needs configure permission to record
      const hasAccess = await metricService.hasPermission(
        req.user.id,
        req.user.roleId,
        category,
        'configure'
      );
      
      if (!hasAccess) {
        return res.status(StatusCodes.FORBIDDEN).json({
          success: false,
          message: 'You do not have permission to record metrics in this category'
        });
      }
      
      // Record the metric based on the category
      let result;
      
      switch (category) {
        case 'component':
          result = await metricService.recordComponentMetric(
            req.body.componentName,
            req.body.metricName,
            req.body.metricValue,
            req.body.unit,
            req.body.metadata
          );
          break;
        case 'business':
          result = await metricService.recordBusinessKpi(req.body);
          break;
        case 'ai':
          result = await metricService.recordAiMetric(req.body);
          break;
        case 'telephony':
          result = await metricService.recordTelephonyMetric(req.body);
          break;
        case 'system':
          result = await metricService.recordSystemHealth(req.body);
          break;
        case 'realtime':
          result = await metricService.recordRealtimeMetric(
            req.body.metricCategory,
            req.body.metricName,
            req.body.metricValue,
            req.body.unit,
            req.body.ttlMinutes
          );
          break;
        default:
          return res.status(StatusCodes.BAD_REQUEST).json({
            success: false,
            message: `Invalid metric category: ${category}`
          });
      }
      
      res.status(StatusCodes.CREATED).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Calculate and store aggregated metrics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async aggregateMetrics(req, res, next) {
    try {
      const {
        metricCategory,
        metricName,
        aggregationType,
        timePeriod,
        startTime,
        endTime
      } = req.body;
      
      // Validate permissions - needs configure permission to aggregate
      const hasAccess = await metricService.hasPermission(
        req.user.id,
        req.user.roleId,
        metricCategory,
        'configure'
      );
      
      if (!hasAccess) {
        return res.status(StatusCodes.FORBIDDEN).json({
          success: false,
          message: 'You do not have permission to aggregate metrics in this category'
        });
      }
      
      const result = await metricService.aggregateMetrics(
        metricCategory,
        metricName,
        aggregationType,
        timePeriod,
        new Date(startTime),
        new Date(endTime)
      );
      
      res.status(StatusCodes.CREATED).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check if a user has permission to access a dashboard section
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async checkPermission(req, res, next) {
    try {
      const { dashboardSection, permissionType = 'view' } = req.params;
      
      const hasAccess = await metricService.hasPermission(
        req.user.id,
        req.user.roleId,
        dashboardSection,
        permissionType
      );
      
      res.json({
        success: true,
        hasPermission: hasAccess
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Manually run the cleanup task for expired realtime metrics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  async cleanupExpiredMetrics(req, res, next) {
    try {
      // Only administrators can manually run cleanup
      if (req.user.role !== 'admin') {
        return res.status(StatusCodes.FORBIDDEN).json({
          success: false,
          message: 'Only administrators can run manual cleanup'
        });
      }
      
      const deletedCount = await metricService.cleanupExpiredRealtimeMetrics();
      
      res.json({
        success: true,
        message: `Cleaned up ${deletedCount} expired metrics`
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new MetricController();
