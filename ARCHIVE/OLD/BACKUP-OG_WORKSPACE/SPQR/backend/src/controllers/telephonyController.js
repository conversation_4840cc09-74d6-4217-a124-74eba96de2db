/**
 * Telephony Controller
 * 
 * Handles HTTP requests related to telephony features, including
 * webhook processing from VAPI.ai, call management, and phone number
 * administration.
 */

const telephonyService = require('../services/telephonyService');
const vapiService = require('../services/vapiService');
const { Call, PhoneNumber, CallTranscript, CallInsight } = require('../models');
const logger = require('winston');

/**
 * Process webhooks from VAPI.ai
 */
exports.processWebhook = async (req, res) => {
  try {
    const event = req.body;
    logger.info(`Received VAPI webhook: ${event.type} for call ${event.call_id}`);
    
    // Validate the webhook payload
    if (!event || !event.type || !event.call_id) {
      return res.status(400).json({
        success: false,
        message: 'Invalid webhook payload'
      });
    }
    
    let result;
    
    // Handle different event types
    switch (event.type) {
      case 'call_initiated':
        // New incoming call
        result = await telephonyService.processIncomingCall(event);
        break;
        
      case 'call_answered':
      case 'call_ended':
      case 'call_failed':
        // Call status updates
        result = await telephonyService.processCallUpdate(event.call_id, event);
        break;
        
      case 'transcript_update':
        // Transcript updates
        result = await telephonyService.processTranscriptUpdate(event.call_id, event);
        break;
        
      default:
        logger.warn(`Unhandled webhook event type: ${event.type}`);
        return res.status(200).json({
          success: true,
          message: 'Event acknowledged but not processed'
        });
    }
    
    return res.status(200).json({
      success: true,
      message: `Processed ${event.type} event`,
      result
    });
  } catch (error) {
    logger.error(`Error processing webhook: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error processing webhook',
      error: error.message
    });
  }
};

/**
 * Make an outbound call
 */
exports.makeCall = async (req, res) => {
  try {
    const { fromNumber, toNumber, customerId, options } = req.body;
    
    // Validate required fields
    if (!fromNumber || !toNumber) {
      return res.status(400).json({
        success: false,
        message: 'fromNumber and toNumber are required'
      });
    }
    
    const call = await telephonyService.makeOutboundCall({
      fromNumber,
      toNumber,
      customerId,
      userId: req.user?.id, // Authenticated user if available
      options
    });
    
    return res.status(201).json({
      success: true,
      message: 'Call initiated successfully',
      call
    });
  } catch (error) {
    logger.error(`Error making call: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error making call',
      error: error.message
    });
  }
};

/**
 * Get call details
 */
exports.getCall = async (req, res) => {
  try {
    const { id } = req.params;
    
    const call = await Call.findByPk(id, {
      include: [
        { association: 'phoneNumber' },
        { association: 'customer' },
        { association: 'user' },
        { association: 'transcript' },
        { association: 'insights' }
      ]
    });
    
    if (!call) {
      return res.status(404).json({
        success: false,
        message: `Call with ID ${id} not found`
      });
    }
    
    return res.status(200).json({
      success: true,
      call
    });
  } catch (error) {
    logger.error(`Error getting call: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error getting call',
      error: error.message
    });
  }
};

/**
 * List calls with filtering options
 */
exports.listCalls = async (req, res) => {
  try {
    const {
      limit = 20,
      offset = 0,
      direction,
      status,
      customerId,
      userId,
      phoneNumberId,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortDir = 'DESC'
    } = req.query;
    
    // Build where clause based on filters
    const where = {};
    
    if (direction) where.direction = direction;
    if (status) where.status = status;
    if (customerId) where.customerId = customerId;
    if (userId) where.userId = userId;
    if (phoneNumberId) where.phoneNumberId = phoneNumberId;
    
    // Date filtering
    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) where.startTime.$gte = new Date(startDate);
      if (endDate) where.startTime.$lte = new Date(endDate);
    }
    
    // Get paginated results
    const calls = await Call.findAndCountAll({
      where,
      include: [
        { association: 'phoneNumber' },
        { association: 'customer' },
        { association: 'user' }
      ],
      order: [[sortBy, sortDir]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    return res.status(200).json({
      success: true,
      count: calls.count,
      calls: calls.rows,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: calls.count,
        pages: Math.ceil(calls.count / limit)
      }
    });
  } catch (error) {
    logger.error(`Error listing calls: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error listing calls',
      error: error.message
    });
  }
};

/**
 * End an active call
 */
exports.endCall = async (req, res) => {
  try {
    const { id } = req.params;
    
    const call = await Call.findByPk(id);
    
    if (!call) {
      return res.status(404).json({
        success: false,
        message: `Call with ID ${id} not found`
      });
    }
    
    if (call.status !== 'in-progress' && call.status !== 'initiated') {
      return res.status(400).json({
        success: false,
        message: `Call is already in ${call.status} status and cannot be ended`
      });
    }
    
    // Hang up the call in VAPI
    await vapiService.hangupCall(call.callId);
    
    // Update call status (actual update will happen via webhook)
    call.status = 'completed';
    call.endTime = new Date();
    call.durationSeconds = call.startTime 
      ? Math.round((call.endTime - call.startTime) / 1000) 
      : 0;
    await call.save();
    
    return res.status(200).json({
      success: true,
      message: 'Call ended successfully',
      call
    });
  } catch (error) {
    logger.error(`Error ending call: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error ending call',
      error: error.message
    });
  }
};

/**
 * Get call transcript
 */
exports.getCallTranscript = async (req, res) => {
  try {
    const { id } = req.params;
    
    const call = await Call.findByPk(id);
    
    if (!call) {
      return res.status(404).json({
        success: false,
        message: `Call with ID ${id} not found`
      });
    }
    
    const transcript = await CallTranscript.findOne({
      where: { callId: id }
    });
    
    if (!transcript) {
      return res.status(404).json({
        success: false,
        message: 'Transcript not found for this call'
      });
    }
    
    return res.status(200).json({
      success: true,
      transcript
    });
  } catch (error) {
    logger.error(`Error getting transcript: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error getting transcript',
      error: error.message
    });
  }
};

/**
 * Get call insights
 */
exports.getCallInsights = async (req, res) => {
  try {
    const { id } = req.params;
    
    const call = await Call.findByPk(id);
    
    if (!call) {
      return res.status(404).json({
        success: false,
        message: `Call with ID ${id} not found`
      });
    }
    
    const insights = await CallInsight.findAll({
      where: { callId: id },
      order: [['createdAt', 'ASC']]
    });
    
    return res.status(200).json({
      success: true,
      insights
    });
  } catch (error) {
    logger.error(`Error getting insights: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error getting insights',
      error: error.message
    });
  }
};

/**
 * Initiate a two-stage connection
 */
exports.initiateConnection = async (req, res) => {
  try {
    const { callId } = req.params;
    const { targetUserId, targetNumber, reason } = req.body;
    
    // Validate required fields
    if (!targetUserId || !targetNumber) {
      return res.status(400).json({
        success: false,
        message: 'targetUserId and targetNumber are required'
      });
    }
    
    const call = await Call.findByPk(callId);
    
    if (!call) {
      return res.status(404).json({
        success: false,
        message: `Call with ID ${callId} not found`
      });
    }
    
    if (call.status !== 'in-progress') {
      return res.status(400).json({
        success: false,
        message: 'Call must be in progress to initiate a connection'
      });
    }
    
    const connection = await telephonyService.initiateTwoStageConnection({
      originalCallId: call.id,
      targetUserId,
      targetNumber,
      reason
    });
    
    return res.status(201).json({
      success: true,
      message: 'Connection initiated',
      connection
    });
  } catch (error) {
    logger.error(`Error initiating connection: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error initiating connection',
      error: error.message
    });
  }
};

/**
 * List phone numbers
 */
exports.listPhoneNumbers = async (req, res) => {
  try {
    const { active } = req.query;
    
    const where = {};
    if (active !== undefined) {
      where.isActive = active === 'true';
    }
    
    const phoneNumbers = await PhoneNumber.findAll({
      where,
      order: [['createdAt', 'DESC']]
    });
    
    return res.status(200).json({
      success: true,
      phoneNumbers
    });
  } catch (error) {
    logger.error(`Error listing phone numbers: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error listing phone numbers',
      error: error.message
    });
  }
};

/**
 * Register a new phone number
 */
exports.registerPhoneNumber = async (req, res) => {
  try {
    const { number, description, configuration } = req.body;
    
    if (!number) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required'
      });
    }
    
    const phoneNumber = await telephonyService.registerPhoneNumber({
      number,
      description: description || `Registered on ${new Date().toISOString()}`,
      configuration: configuration || {}
    });
    
    return res.status(201).json({
      success: true,
      message: 'Phone number registered successfully',
      phoneNumber
    });
  } catch (error) {
    logger.error(`Error registering phone number: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error registering phone number',
      error: error.message
    });
  }
};

/**
 * Update a phone number
 */
exports.updatePhoneNumber = async (req, res) => {
  try {
    const { id } = req.params;
    const { description, isActive, configuration } = req.body;
    
    const phoneNumber = await PhoneNumber.findByPk(id);
    
    if (!phoneNumber) {
      return res.status(404).json({
        success: false,
        message: `Phone number with ID ${id} not found`
      });
    }
    
    // Update fields if provided
    if (description !== undefined) phoneNumber.description = description;
    if (isActive !== undefined) phoneNumber.isActive = isActive;
    if (configuration !== undefined) phoneNumber.configuration = configuration;
    
    await phoneNumber.save();
    
    return res.status(200).json({
      success: true,
      message: 'Phone number updated successfully',
      phoneNumber
    });
  } catch (error) {
    logger.error(`Error updating phone number: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error updating phone number',
      error: error.message
    });
  }
};

/**
 * Get customer call history
 */
exports.getCustomerCallHistory = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { limit = 50, offset = 0, includeTranscripts = false } = req.query;
    
    const calls = await telephonyService.getCustomerCallHistory(
      customerId,
      {
        limit: parseInt(limit),
        offset: parseInt(offset),
        includeTranscripts: includeTranscripts === 'true'
      }
    );
    
    return res.status(200).json({
      success: true,
      calls
    });
  } catch (error) {
    logger.error(`Error getting customer call history: ${error.message}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error getting customer call history',
      error: error.message
    });
  }
};
