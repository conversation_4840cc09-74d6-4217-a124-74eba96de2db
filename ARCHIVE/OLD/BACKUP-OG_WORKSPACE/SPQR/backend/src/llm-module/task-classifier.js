/**
 * Task Classifier
 * 
 * Analyzes prompts and messages to determine the task type and complexity.
 * Used by the LLM Orchestrator to select the optimal provider for a given task.
 */

const logger = require('../utils/logger');

// Task types
const TASK_TYPES = {
  CODING: 'coding',
  CREATIVE: 'creative',
  REASONING: 'reasoning',
  MATH: 'math',
  EXTRACTION: 'extraction',
  SUMMARIZATION: 'summarization',
  TRANSLATION: 'translation',
  CLASSIFICATION: 'classification',
  GENERAL: 'general',
  LOW_COMPLEXITY: 'low_complexity'
};

// Complexity levels
const COMPLEXITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
};

class TaskClassifier {
  /**
   * Create a new task classifier
   * 
   * @param {Object} config - Classifier configuration
   */
  constructor(config = {}) {
    this.config = config;
    
    // Keywords for task type detection
    this.taskTypeKeywords = {
      [TASK_TYPES.CODING]: [
        'code', 'function', 'programming', 'algorithm', 'javascript', 'python', 
        'java', 'c++', 'typescript', 'html', 'css', 'sql', 'api', 'debug', 
        'implement', 'class', 'method', 'compile', 'runtime', 'syntax'
      ],
      [TASK_TYPES.CREATIVE]: [
        'creative', 'story', 'poem', 'write', 'imagine', 'fiction', 'narrative',
        'creative writing', 'novel', 'screenplay', 'script', 'dialogue', 'character',
        'plot', 'setting', 'theme', 'metaphor', 'simile', 'analogy', 'artistic'
      ],
      [TASK_TYPES.REASONING]: [
        'reason', 'logic', 'argument', 'philosophy', 'ethics', 'analyze', 'evaluate',
        'critical thinking', 'inference', 'deduction', 'induction', 'fallacy',
        'premise', 'conclusion', 'syllogism', 'validity', 'soundness', 'coherence'
      ],
      [TASK_TYPES.MATH]: [
        'math', 'calculation', 'equation', 'formula', 'algebra', 'calculus', 'geometry',
        'statistics', 'probability', 'arithmetic', 'numerical', 'solve', 'compute',
        'derivative', 'integral', 'function', 'variable', 'constant', 'matrix', 'vector'
      ],
      [TASK_TYPES.EXTRACTION]: [
        'extract', 'find', 'identify', 'locate', 'pull', 'retrieve', 'parse',
        'scrape', 'get', 'fetch', 'search', 'filter', 'isolate', 'separate',
        'entities', 'keywords', 'names', 'dates', 'values', 'json', 'xml', 'csv'
      ],
      [TASK_TYPES.SUMMARIZATION]: [
        'summarize', 'summary', 'condense', 'shorten', 'brief', 'synopsis',
        'outline', 'abstract', 'recap', 'tldr', 'overview', 'digest', 'précis',
        'abridgment', 'compression', 'distillation', 'essence', 'gist'
      ],
      [TASK_TYPES.TRANSLATION]: [
        'translate', 'translation', 'language', 'english', 'spanish', 'french',
        'german', 'chinese', 'japanese', 'russian', 'arabic', 'hindi', 'portuguese',
        'italian', 'dutch', 'korean', 'turkish', 'swedish', 'polish', 'vietnamese'
      ],
      [TASK_TYPES.CLASSIFICATION]: [
        'classify', 'categorize', 'sort', 'group', 'label', 'tag', 'bucket',
        'segment', 'partition', 'divide', 'organize', 'arrange', 'structure',
        'taxonomy', 'hierarchy', 'classification', 'category', 'class', 'type'
      ]
    };
    
    // Complexity indicators
    this.complexityIndicators = {
      [COMPLEXITY_LEVELS.HIGH]: [
        'complex', 'complicated', 'advanced', 'sophisticated', 'intricate',
        'elaborate', 'detailed', 'comprehensive', 'thorough', 'in-depth',
        'exhaustive', 'nuanced', 'multifaceted', 'layered', 'deep'
      ],
      [COMPLEXITY_LEVELS.LOW]: [
        'simple', 'basic', 'easy', 'straightforward', 'elementary', 'fundamental',
        'rudimentary', 'introductory', 'beginner', 'quick', 'brief', 'short',
        'concise', 'minimal', 'lightweight', 'trivial', 'uncomplicated'
      ]
    };
  }

  /**
   * Classify a task based on prompt and messages
   * 
   * @param {Object} params - Classification parameters
   * @param {string} [params.prompt] - The prompt to classify
   * @param {Array<Object>} [params.messages] - Chat messages to classify
   * @param {string} [params.systemPrompt] - System instructions
   * @returns {Object} - Classification result with task type and complexity
   */
  classify(params) {
    // Extract text from prompt and messages
    let text = '';
    
    if (params.prompt) {
      text += params.prompt + ' ';
    }
    
    if (params.systemPrompt) {
      text += params.systemPrompt + ' ';
    }
    
    if (params.messages && params.messages.length > 0) {
      text += params.messages.map(msg => {
        if (typeof msg.content === 'string') {
          return msg.content;
        } else if (Array.isArray(msg.content)) {
          return msg.content.map(part => {
            if (part.type === 'text') {
              return part.text;
            }
            return '';
          }).join(' ');
        }
        return '';
      }).join(' ');
    }
    
    // Normalize text
    text = text.toLowerCase();
    
    // Detect task type
    const taskType = this._detectTaskType(text);
    
    // Detect complexity
    const complexity = this._detectComplexity(text);
    
    // Adjust task type based on complexity
    let finalTaskType = taskType;
    if (complexity === COMPLEXITY_LEVELS.LOW && taskType === TASK_TYPES.GENERAL) {
      finalTaskType = TASK_TYPES.LOW_COMPLEXITY;
    }
    
    logger.debug(`Task classified as type: ${finalTaskType}, complexity: ${complexity}`);
    
    return {
      taskType: finalTaskType,
      complexity: complexity,
      confidence: this._calculateConfidence(text, finalTaskType, complexity)
    };
  }

  /**
   * Detect the task type from text
   * 
   * @param {string} text - Text to analyze
   * @returns {string} - Detected task type
   * @private
   */
  _detectTaskType(text) {
    const scores = {};
    
    // Calculate score for each task type
    for (const [type, keywords] of Object.entries(this.taskTypeKeywords)) {
      scores[type] = 0;
      
      for (const keyword of keywords) {
        // Count occurrences of keyword
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const matches = text.match(regex);
        
        if (matches) {
          scores[type] += matches.length;
        }
      }
    }
    
    // Find task type with highest score
    let maxScore = 0;
    let maxType = TASK_TYPES.GENERAL;
    
    for (const [type, score] of Object.entries(scores)) {
      if (score > maxScore) {
        maxScore = score;
        maxType = type;
      }
    }
    
    // If score is too low, default to general
    if (maxScore < 2) {
      return TASK_TYPES.GENERAL;
    }
    
    return maxType;
  }

  /**
   * Detect the complexity level from text
   * 
   * @param {string} text - Text to analyze
   * @returns {string} - Detected complexity level
   * @private
   */
  _detectComplexity(text) {
    const scores = {};
    
    // Calculate score for each complexity level
    for (const [level, indicators] of Object.entries(this.complexityIndicators)) {
      scores[level] = 0;
      
      for (const indicator of indicators) {
        // Count occurrences of indicator
        const regex = new RegExp(`\\b${indicator}\\b`, 'gi');
        const matches = text.match(regex);
        
        if (matches) {
          scores[level] += matches.length;
        }
      }
    }
    
    // Text length is also an indicator of complexity
    const wordCount = text.split(/\s+/).length;
    
    if (wordCount > 500) {
      scores[COMPLEXITY_LEVELS.HIGH] += 2;
    } else if (wordCount > 200) {
      scores[COMPLEXITY_LEVELS.MEDIUM] += 2;
    } else {
      scores[COMPLEXITY_LEVELS.LOW] += 2;
    }
    
    // Find complexity level with highest score
    if (scores[COMPLEXITY_LEVELS.HIGH] > scores[COMPLEXITY_LEVELS.MEDIUM] && 
        scores[COMPLEXITY_LEVELS.HIGH] > scores[COMPLEXITY_LEVELS.LOW]) {
      return COMPLEXITY_LEVELS.HIGH;
    } else if (scores[COMPLEXITY_LEVELS.LOW] > scores[COMPLEXITY_LEVELS.MEDIUM]) {
      return COMPLEXITY_LEVELS.LOW;
    } else {
      return COMPLEXITY_LEVELS.MEDIUM;
    }
  }

  /**
   * Calculate confidence score for classification
   * 
   * @param {string} text - Text that was analyzed
   * @param {string} taskType - Detected task type
   * @param {string} complexity - Detected complexity level
   * @returns {number} - Confidence score (0-1)
   * @private
   */
  _calculateConfidence(text, taskType, complexity) {
    // Simple confidence calculation based on keyword matches
    let confidence = 0.5; // Base confidence
    
    if (taskType !== TASK_TYPES.GENERAL) {
      // Count task type keywords
      const keywords = this.taskTypeKeywords[taskType] || [];
      let matches = 0;
      
      for (const keyword of keywords) {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const keywordMatches = text.match(regex);
        
        if (keywordMatches) {
          matches += keywordMatches.length;
        }
      }
      
      // Adjust confidence based on matches
      confidence += Math.min(0.4, matches * 0.05);
    }
    
    // Adjust confidence based on text length
    const wordCount = text.split(/\s+/).length;
    
    if (wordCount > 100) {
      confidence += 0.1;
    } else if (wordCount < 20) {
      confidence -= 0.1;
    }
    
    // Ensure confidence is between 0 and 1
    return Math.max(0, Math.min(1, confidence));
  }
}

module.exports = {
  TaskClassifier,
  TASK_TYPES,
  COMPLEXITY_LEVELS
};
