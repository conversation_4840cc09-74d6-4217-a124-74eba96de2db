/**
 * OpenAI LLM Provider
 * 
 * Implementation of the BaseLLMProvider interface for OpenAI models.
 */

const { OpenAI } = require('openai');
const retry = require('async-retry');
const BaseLLMProvider = require('./base');
const logger = require('../../utils/logger');

// Helper function to count tokens using OpenAI's tokenizer
const countOpenAITokens = (text) => {
  // Simple approximation: ~4 characters per token
  return Math.ceil(text.length / 4);
};

class OpenAIProvider extends BaseLLMProvider {
  /**
   * Create a new OpenAI provider instance
   * 
   * @param {Object} config - Provider configuration
   * @param {string} config.apiKey - OpenAI API key
   * @param {string} config.model - OpenAI model to use
   * @param {number} config.maxRetries - Maximum retry attempts
   * @param {number} config.retryDelay - Delay between retries in ms
   * @param {Object} config.defaultParams - Default parameters for OpenAI API
   */
  constructor(config = {}) {
    super(config);
    this.name = 'openai';
    this.client = null;
    this.model = config.model || process.env.OPENAI_MODEL || 'gpt-4';
    this.maxRetries = config.maxRetries || parseInt(process.env.OPENAI_MAX_RETRY_ATTEMPTS || '3', 10);
    this.retryDelay = config.retryDelay || 1000;
    this.defaultParams = {
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0,
      ...config.defaultParams
    };
  }

  /**
   * Initialize the OpenAI provider
   * 
   * @returns {Promise<boolean>} - True if initialization was successful
   */
  async initialize() {
    try {
      const apiKey = this.config.apiKey || process.env.OPENAI_API_KEY;
      
      if (!apiKey) {
        logger.error('OpenAI API key not provided');
        return false;
      }
      
      this.client = new OpenAI({
        apiKey: apiKey,
        timeout: parseInt(process.env.OPENAI_API_TIMEOUT || '60000', 10)
      });
      
      this.isInitialized = true;
      logger.info(`OpenAI provider initialized with model: ${this.model}`);
      return true;
    } catch (error) {
      logger.error(`Failed to initialize OpenAI provider: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate a response from OpenAI
   * 
   * @param {Object} params - Generation parameters
   * @param {string} [params.prompt] - The prompt to send to OpenAI (for completion mode)
   * @param {string} [params.systemPrompt] - System instructions for OpenAI
   * @param {Array<Object>} [params.messages] - Chat messages (for chat mode)
   * @param {Object} [params.options] - Additional options
   * @returns {Promise<Object>} - OpenAI's response
   * @throws {Error} - If generation fails
   */
  async generateResponse(params) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.client) {
      throw new Error('OpenAI provider not initialized');
    }

    // Prepare messages for chat mode or convert prompt to message
    let messages = params.messages || [];
    
    // Add system message if provided
    if (params.systemPrompt) {
      messages.unshift({ role: 'system', content: params.systemPrompt });
    } else if (messages.length === 0 || messages[0].role !== 'system') {
      // Add default system message if none exists
      messages.unshift({ 
        role: 'system', 
        content: 'You are a helpful assistant.' 
      });
    }
    
    // Add user prompt if provided
    if (params.prompt && !messages.some(m => m.role === 'user')) {
      messages.push({ role: 'user', content: params.prompt });
    }

    if (messages.length < 2) {
      throw new Error('No user prompt or messages provided');
    }

    // Prepare request parameters
    const requestParams = {
      model: params.options?.model || this.model,
      messages: messages,
      temperature: params.options?.temperature ?? this.defaultParams.temperature,
      max_tokens: params.options?.max_tokens ?? this.defaultParams.max_tokens,
      top_p: params.options?.top_p ?? this.defaultParams.top_p,
      frequency_penalty: params.options?.frequency_penalty ?? this.defaultParams.frequency_penalty,
      presence_penalty: params.options?.presence_penalty ?? this.defaultParams.presence_penalty,
    };

    // Use retry for robust error handling
    return retry(async (bail, attempt) => {
      try {
        logger.debug(`OpenAI attempt ${attempt}: ${JSON.stringify(messages[messages.length - 1]).substring(0, 100)}...`);
        
        const startTime = Date.now();
        const response = await this.client.chat.completions.create(requestParams);
        const endTime = Date.now();
        
        // Update stats
        this.lastUsed = new Date();
        this.requestCount = (this.requestCount || 0) + 1;
        this.successCount = (this.successCount || 0) + 1;
        this.averageLatency = this.averageLatency 
          ? (this.averageLatency * (this.successCount - 1) + (endTime - startTime)) / this.successCount
          : (endTime - startTime);
        
        return {
          provider: this.name,
          model: response.model,
          content: response.choices,
          text: response.choices[0].message.content,
          usage: response.usage,
          id: response.id,
          stopReason: response.choices[0].finish_reason,
          latency: endTime - startTime
        };
      } catch (error) {
        // Update error stats
        this.lastError = error;
        this.errorCount = (this.errorCount || 0) + 1;
        
        // Log the error
        logger.error(`OpenAI API error (attempt ${attempt}/${this.maxRetries}): ${error.message}`);
        
        // Handle different error types
        if (error.status === 429) {
          // Rate limit error, we should retry
          logger.warn('OpenAI rate limit hit, retrying after delay');
          throw error; // This will trigger a retry
        } else if (error.status >= 500) {
          // Server error, we should retry
          logger.warn('OpenAI server error, retrying');
          throw error; // This will trigger a retry
        } else if (error.status === 400) {
          // Bad request - analyze the error message
          if (error.message.includes('maximum context length')) {
            logger.error('OpenAI context window exceeded');
            // We shouldn't retry token limit errors
            bail(new Error(`OpenAI context window exceeded: ${error.message}`));
            return;
          }
          // Other 400 errors might be retryable
          throw error;
        } else if (error.status === 401) {
          // Authentication error, no point in retrying
          logger.error('OpenAI authentication failed');
          bail(new Error(`OpenAI authentication failed: ${error.message}`));
          return;
        } else {
          // For unknown errors, retry a few times
          throw error;
        }
      }
    }, {
      retries: this.maxRetries,
      minTimeout: this.retryDelay,
      maxTimeout: this.retryDelay * 3,
      factor: 2,
      onRetry: (error, attempt) => {
        logger.warn(`Retrying OpenAI API call (${attempt}/${this.maxRetries}) after error: ${error.message}`);
      }
    });
  }

  /**
   * Check if the OpenAI provider is available
   * 
   * @returns {Promise<boolean>} - True if the provider is available
   */
  async isAvailable() {
    if (!this.isInitialized) {
      return false;
    }
    
    try {
      // Simple health check - just verify we have a valid API key
      return !!this.config.apiKey || !!process.env.OPENAI_API_KEY;
    } catch (error) {
      logger.error(`OpenAI availability check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Get the OpenAI provider's capabilities
   * 
   * @returns {Object} - Provider capabilities
   */
  getCapabilities() {
    const capabilities = {
      name: this.name,
      models: [
        'gpt-4o',
        'gpt-4-turbo',
        'gpt-4',
        'gpt-4-32k',
        'gpt-3.5-turbo',
        'gpt-3.5-turbo-16k'
      ],
      maxTokens: 4096,
      contextWindow: this.model.includes('gpt-4-32k') ? 32768 : 
                     this.model.includes('gpt-4o') || this.model.includes('gpt-4-turbo') ? 128000 :
                     this.model.includes('gpt-4') ? 8192 :
                     this.model.includes('16k') ? 16384 : 4096,
      streaming: true,
      cost: this.model.includes('gpt-4') ? 'high' : 'medium',
      strengths: ['coding', 'math', 'factuality', 'instruction-following'],
      weaknesses: [],
      supportedModes: ['completion', 'chat'],
    };
    
    return capabilities;
  }

  /**
   * Get the OpenAI provider's cost estimate for a request
   * 
   * @param {Object} params - Request parameters
   * @returns {Object} - Cost estimate
   */
  getCostEstimate(params) {
    // Prepare messages for token counting
    let messages = params.messages || [];
    
    // Add system message if provided
    if (params.systemPrompt && !messages.some(m => m.role === 'system')) {
      messages.unshift({ role: 'system', content: params.systemPrompt });
    } else if (messages.length === 0 || messages[0].role !== 'system') {
      // Add default system message if none exists
      messages.unshift({ 
        role: 'system', 
        content: 'You are a helpful assistant.' 
      });
    }
    
    // Add user prompt if provided
    if (params.prompt && !messages.some(m => m.role === 'user')) {
      messages.push({ role: 'user', content: params.prompt });
    }
    
    // Count tokens in messages
    let inputTokens = 0;
    messages.forEach(msg => {
      const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
      inputTokens += countOpenAITokens(content);
    });
    
    // Add overhead for message formatting
    const overhead = messages.length * 4;
    inputTokens += overhead;
    
    // Estimate output tokens (default to 1/2 of max_tokens)
    const outputTokens = params.options?.max_tokens 
      ? Math.min(params.options.max_tokens, this.defaultParams.max_tokens) 
      : this.defaultParams.max_tokens / 2;
    
    // Calculate cost based on model
    let inputCostPer1k = 0;
    let outputCostPer1k = 0;
    
    if (this.model.includes('gpt-4o')) {
      inputCostPer1k = 5.0;
      outputCostPer1k = 15.0;
    } else if (this.model.includes('gpt-4-turbo')) {
      inputCostPer1k = 10.0;
      outputCostPer1k = 30.0;
    } else if (this.model.includes('gpt-4-32k')) {
      inputCostPer1k = 60.0;
      outputCostPer1k = 120.0;
    } else if (this.model.includes('gpt-4')) {
      inputCostPer1k = 30.0;
      outputCostPer1k = 60.0;
    } else {
      // GPT-3.5 pricing
      inputCostPer1k = 0.5;
      outputCostPer1k = 1.5;
    }
    
    const inputCost = (inputTokens / 1000) * inputCostPer1k;
    const outputCost = (outputTokens / 1000) * outputCostPer1k;
    
    return {
      provider: this.name,
      model: this.model,
      inputTokens,
      outputTokens,
      totalTokens: inputTokens + outputTokens,
      estimatedCost: inputCost + outputCost,
      currency: 'USD',
      breakdown: {
        inputCost,
        outputCost,
        inputCostPer1k,
        outputCostPer1k
      }
    };
  }
}

module.exports = OpenAIProvider;
