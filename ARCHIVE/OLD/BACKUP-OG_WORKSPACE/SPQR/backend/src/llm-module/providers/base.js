/**
 * Base LLM Provider
 * 
 * Defines the interface that all LLM providers must implement.
 * This ensures a consistent API across different LLM implementations.
 */

class BaseLLMProvider {
  /**
   * Create a new LLM provider instance
   * 
   * @param {Object} config - Provider configuration
   */
  constructor(config = {}) {
    this.config = config;
    this.name = 'base'; // Should be overridden by subclasses
    this.isInitialized = false;
  }

  /**
   * Initialize the provider
   * This method should be called before using the provider
   * 
   * @returns {Promise<boolean>} - True if initialization was successful
   */
  async initialize() {
    this.isInitialized = true;
    return true;
  }

  /**
   * Generate a response from the LLM
   * 
   * @param {Object} params - Generation parameters
   * @param {string} params.prompt - The prompt to send to the LLM
   * @param {string} [params.systemPrompt] - Optional system instructions
   * @param {Array<Object>} [params.messages] - Optional chat messages (for chat models)
   * @param {Object} [params.options] - Additional provider-specific options
   * @returns {Promise<Object>} - The LLM response
   * @throws {Error} - If generation fails
   */
  async generateResponse(params) {
    throw new Error('Method generateResponse() must be implemented by subclass');
  }

  /**
   * Check if the provider is available
   * 
   * @returns {Promise<boolean>} - True if the provider is available
   */
  async isAvailable() {
    return this.isInitialized;
  }

  /**
   * Get the provider's capabilities
   * 
   * @returns {Object} - Provider capabilities
   */
  getCapabilities() {
    return {
      name: this.name,
      maxTokens: 2048,
      contextWindow: 4096,
      streaming: false,
      cost: 'medium',
      strengths: ['general'],
      weaknesses: [],
      supportedModes: ['completion'],
    };
  }

  /**
   * Get the provider's cost estimate for a request
   * 
   * @param {Object} params - Request parameters
   * @returns {Object} - Cost estimate (tokens and approximate cost)
   */
  getCostEstimate(params) {
    return {
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: 0,
      estimatedCost: 0,
      currency: 'USD'
    };
  }

  /**
   * Get the provider's status
   * 
   * @returns {Object} - Provider status
   */
  getStatus() {
    return {
      name: this.name,
      isInitialized: this.isInitialized,
      isAvailable: this.isInitialized,
      lastError: null,
      lastUsed: null,
      requestCount: 0,
      successCount: 0,
      errorCount: 0,
      averageLatency: 0
    };
  }
}

module.exports = BaseLLMProvider;
