/**
 * Claude LLM Provider
 * 
 * Implementation of the BaseLLMProvider interface for Anthropic's Claude models.
 */

const { Client } = require('@anthropic-ai/sdk');
const retry = require('async-retry');
const BaseLLMProvider = require('./base');
const logger = require('../../utils/logger');
const { countClaudeTokens } = require('../../utils/tokenCounter');

class ClaudeProvider extends BaseLLMProvider {
  /**
   * Create a new Claude provider instance
   * 
   * @param {Object} config - Provider configuration
   * @param {string} config.apiKey - Anthropic API key
   * @param {string} config.model - Claude model to use
   * @param {number} config.maxRetries - Maximum retry attempts
   * @param {number} config.retryDelay - Delay between retries in ms
   * @param {Object} config.defaultParams - Default parameters for Claude API
   */
  constructor(config = {}) {
    super(config);
    this.name = 'claude';
    this.client = null;
    this.model = config.model || process.env.CLAUDE_MODEL || 'claude-3-opus-20240229';
    this.maxRetries = config.maxRetries || parseInt(process.env.CLAUDE_MAX_RETRY_ATTEMPTS || '3', 10);
    this.retryDelay = config.retryDelay || 1000;
    this.defaultParams = {
      temperature: 0.7,
      max_tokens: 4000,
      top_p: 0.95,
      top_k: 40,
      ...config.defaultParams
    };
  }

  /**
   * Initialize the Claude provider
   * 
   * @returns {Promise<boolean>} - True if initialization was successful
   */
  async initialize() {
    try {
      const apiKey = this.config.apiKey || process.env.CLAUDE_API_KEY;
      
      if (!apiKey) {
        logger.error('Claude API key not provided');
        return false;
      }
      
      this.client = new Client(apiKey);
      this.isInitialized = true;
      logger.info(`Claude provider initialized with model: ${this.model}`);
      return true;
    } catch (error) {
      logger.error(`Failed to initialize Claude provider: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate a response from Claude
   * 
   * @param {Object} params - Generation parameters
   * @param {string} [params.prompt] - The prompt to send to Claude (for completion mode)
   * @param {string} [params.systemPrompt] - System instructions for Claude
   * @param {Array<Object>} [params.messages] - Chat messages (for chat mode)
   * @param {Object} [params.options] - Additional options
   * @returns {Promise<Object>} - Claude's response
   * @throws {Error} - If generation fails
   */
  async generateResponse(params) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.client) {
      throw new Error('Claude provider not initialized');
    }

    // Prepare messages for chat mode or convert prompt to message
    let messages = params.messages || [];
    if (params.prompt && messages.length === 0) {
      messages = [{ role: 'user', content: params.prompt }];
    }

    if (messages.length === 0) {
      throw new Error('No prompt or messages provided');
    }

    // Prepare system prompt
    const systemPrompt = params.systemPrompt || 
      'You are Claude, an AI assistant created by Anthropic to be helpful, harmless, and honest.';

    // Prepare request parameters
    const requestParams = {
      model: params.options?.model || this.model,
      system: systemPrompt,
      messages: messages,
      temperature: params.options?.temperature ?? this.defaultParams.temperature,
      max_tokens: params.options?.max_tokens ?? this.defaultParams.max_tokens,
      top_p: params.options?.top_p ?? this.defaultParams.top_p,
      top_k: params.options?.top_k ?? this.defaultParams.top_k,
    };

    // Use retry for robust error handling
    return retry(async (bail, attempt) => {
      try {
        logger.debug(`Claude attempt ${attempt}: ${JSON.stringify(messages[0]).substring(0, 100)}...`);
        
        const startTime = Date.now();
        const response = await this.client.messages.create(requestParams);
        const endTime = Date.now();
        
        // Update stats
        this.lastUsed = new Date();
        this.requestCount = (this.requestCount || 0) + 1;
        this.successCount = (this.successCount || 0) + 1;
        this.averageLatency = this.averageLatency 
          ? (this.averageLatency * (this.successCount - 1) + (endTime - startTime)) / this.successCount
          : (endTime - startTime);
        
        return {
          provider: this.name,
          model: response.model,
          content: response.content,
          text: response.content.map(part => part.type === 'text' ? part.text : '').join(''),
          usage: response.usage,
          id: response.id,
          stopReason: response.stop_reason,
          latency: endTime - startTime
        };
      } catch (error) {
        // Update error stats
        this.lastError = error;
        this.errorCount = (this.errorCount || 0) + 1;
        
        // Log the error
        logger.error(`Claude API error (attempt ${attempt}/${this.maxRetries}): ${error.message}`);
        
        // Handle different error types
        if (error.status === 429) {
          // Rate limit error, we should retry
          logger.warn('Claude rate limit hit, retrying after delay');
          throw error; // This will trigger a retry
        } else if (error.status >= 500) {
          // Server error, we should retry
          logger.warn('Claude server error, retrying');
          throw error; // This will trigger a retry
        } else if (error.status === 400) {
          // Bad request - analyze the error message
          if (error.message.includes('token limit')) {
            logger.error('Claude context window exceeded');
            // We shouldn't retry token limit errors
            bail(new Error(`Claude context window exceeded: ${error.message}`));
            return;
          }
          // Other 400 errors might be retryable
          throw error;
        } else if (error.status === 401) {
          // Authentication error, no point in retrying
          logger.error('Claude authentication failed');
          bail(new Error(`Claude authentication failed: ${error.message}`));
          return;
        } else {
          // For unknown errors, retry a few times
          throw error;
        }
      }
    }, {
      retries: this.maxRetries,
      minTimeout: this.retryDelay,
      maxTimeout: this.retryDelay * 3,
      factor: 2,
      onRetry: (error, attempt) => {
        logger.warn(`Retrying Claude API call (${attempt}/${this.maxRetries}) after error: ${error.message}`);
      }
    });
  }

  /**
   * Check if the Claude provider is available
   * 
   * @returns {Promise<boolean>} - True if the provider is available
   */
  async isAvailable() {
    if (!this.isInitialized) {
      return false;
    }
    
    try {
      // Simple health check - just verify we have a valid API key
      return !!this.config.apiKey || !!process.env.CLAUDE_API_KEY;
    } catch (error) {
      logger.error(`Claude availability check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Get the Claude provider's capabilities
   * 
   * @returns {Object} - Provider capabilities
   */
  getCapabilities() {
    const capabilities = {
      name: this.name,
      models: [
        'claude-3-opus-20240229',
        'claude-3-sonnet-20240229',
        'claude-3-haiku-20240307',
        'claude-2.1',
        'claude-2.0',
        'claude-instant-1.2'
      ],
      maxTokens: 4096,
      contextWindow: this.model.includes('claude-3-opus') ? 200000 : 
                     this.model.includes('claude-3-sonnet') ? 180000 :
                     this.model.includes('claude-3-haiku') ? 150000 : 100000,
      streaming: true,
      cost: this.model.includes('opus') ? 'high' : 
            this.model.includes('sonnet') ? 'medium' : 'low',
      strengths: ['reasoning', 'instruction-following', 'safety', 'long-context'],
      weaknesses: ['math', 'coding', 'factuality'],
      supportedModes: ['completion', 'chat'],
    };
    
    return capabilities;
  }

  /**
   * Get the Claude provider's cost estimate for a request
   * 
   * @param {Object} params - Request parameters
   * @returns {Object} - Cost estimate
   */
  getCostEstimate(params) {
    // Prepare messages for token counting
    let messages = params.messages || [];
    if (params.prompt && messages.length === 0) {
      messages = [{ role: 'user', content: params.prompt }];
    }

    // Count tokens in system prompt
    const systemPrompt = params.systemPrompt || 
      'You are Claude, an AI assistant created by Anthropic to be helpful, harmless, and honest.';
    const systemTokens = countClaudeTokens(systemPrompt);
    
    // Count tokens in messages
    let messageTokens = 0;
    messages.forEach(msg => {
      const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
      messageTokens += countClaudeTokens(content);
    });
    
    // Add overhead for message formatting
    const overhead = messages.length * 4;
    
    // Calculate total input tokens
    const inputTokens = systemTokens + messageTokens + overhead;
    
    // Estimate output tokens (default to 1/2 of max_tokens)
    const outputTokens = params.options?.max_tokens 
      ? Math.min(params.options.max_tokens, this.defaultParams.max_tokens) 
      : this.defaultParams.max_tokens / 2;
    
    // Calculate cost based on model
    let inputCostPer1k = 0;
    let outputCostPer1k = 0;
    
    if (this.model.includes('claude-3-opus')) {
      inputCostPer1k = 15.0;
      outputCostPer1k = 75.0;
    } else if (this.model.includes('claude-3-sonnet')) {
      inputCostPer1k = 3.0;
      outputCostPer1k = 15.0;
    } else if (this.model.includes('claude-3-haiku')) {
      inputCostPer1k = 0.25;
      outputCostPer1k = 1.25;
    } else {
      // Claude 2 pricing
      inputCostPer1k = 8.0;
      outputCostPer1k = 24.0;
    }
    
    const inputCost = (inputTokens / 1000) * inputCostPer1k;
    const outputCost = (outputTokens / 1000) * outputCostPer1k;
    
    return {
      provider: this.name,
      model: this.model,
      inputTokens,
      outputTokens,
      totalTokens: inputTokens + outputTokens,
      estimatedCost: inputCost + outputCost,
      currency: 'USD',
      breakdown: {
        inputCost,
        outputCost,
        inputCostPer1k,
        outputCostPer1k
      }
    };
  }
}

module.exports = ClaudeProvider;
