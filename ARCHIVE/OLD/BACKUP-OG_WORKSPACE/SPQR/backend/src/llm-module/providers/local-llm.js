/**
 * Local LLM Provider
 * 
 * Implementation of the BaseLLMProvider interface for locally hosted LLM models.
 * Supports Ollama and other local LLM servers with compatible APIs.
 */

const axios = require('axios');
const retry = require('async-retry');
const BaseLLMProvider = require('./base');
const logger = require('../../utils/logger');

class LocalLLMProvider extends BaseLLMProvider {
  /**
   * Create a new Local LLM provider instance
   * 
   * @param {Object} config - Provider configuration
   * @param {string} config.baseUrl - Base URL for the local LLM server
   * @param {string} config.model - Model name to use
   * @param {number} config.maxRetries - Maximum retry attempts
   * @param {number} config.retryDelay - Delay between retries in ms
   * @param {Object} config.defaultParams - Default parameters for API
   * @param {string} config.type - Type of local LLM server ('ollama' or 'localai')
   */
  constructor(config = {}) {
    super(config);
    this.name = 'local-llm';
    this.baseUrl = config.baseUrl || process.env.LOCAL_AI_BASE_URL || 'http://localhost:11434/api';
    this.model = config.model || process.env.LOCAL_AI_MODEL || 'llama3';
    this.maxRetries = config.maxRetries || 3;
    this.retryDelay = config.retryDelay || 1000;
    this.type = config.type || process.env.LOCAL_LLM_TYPE || 'ollama'; // 'ollama' or 'localai'
    this.defaultParams = {
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.95,
      top_k: 40,
      ...config.defaultParams
    };
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: parseInt(process.env.LOCAL_LLM_TIMEOUT || '60000', 10)
    });
  }

  /**
   * Initialize the Local LLM provider
   * 
   * @returns {Promise<boolean>} - True if initialization was successful
   */
  async initialize() {
    try {
      // Check if the server is available
      if (this.type === 'ollama') {
        const response = await this.client.get('/');
        logger.info(`Ollama server available: ${JSON.stringify(response.data)}`);
      } else if (this.type === 'localai') {
        const response = await this.client.get('/models');
        logger.info(`LocalAI server available with ${response.data.length} models`);
      } else {
        throw new Error(`Unsupported local LLM type: ${this.type}`);
      }
      
      this.isInitialized = true;
      logger.info(`Local LLM provider initialized with model: ${this.model}`);
      return true;
    } catch (error) {
      logger.error(`Failed to initialize Local LLM provider: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate a response from the local LLM
   * 
   * @param {Object} params - Generation parameters
   * @param {string} [params.prompt] - The prompt to send to the LLM
   * @param {string} [params.systemPrompt] - System instructions
   * @param {Array<Object>} [params.messages] - Chat messages
   * @param {Object} [params.options] - Additional options
   * @returns {Promise<Object>} - LLM response
   * @throws {Error} - If generation fails
   */
  async generateResponse(params) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    // Prepare prompt or messages
    let prompt = params.prompt || '';
    let messages = params.messages || [];
    let systemPrompt = params.systemPrompt || '';
    
    // Convert messages to prompt if needed
    if (prompt === '' && messages.length > 0) {
      if (this.type === 'ollama') {
        // For Ollama, we'll use the chat endpoint if messages are provided
        // No conversion needed
      } else {
        // For other providers, convert messages to a prompt
        if (systemPrompt) {
          prompt += `System: ${systemPrompt}\n\n`;
        }
        
        messages.forEach(msg => {
          const role = msg.role.charAt(0).toUpperCase() + msg.role.slice(1);
          prompt += `${role}: ${msg.content}\n\n`;
        });
        
        prompt += 'Assistant:';
      }
    }

    // Prepare request parameters based on provider type
    let requestParams;
    let endpoint;
    
    if (this.type === 'ollama') {
      if (messages.length > 0) {
        // Use chat endpoint
        endpoint = '/chat';
        requestParams = {
          model: params.options?.model || this.model,
          messages: messages,
          stream: false
        };
        
        // Add system message if provided
        if (systemPrompt && !messages.some(m => m.role === 'system')) {
          requestParams.messages.unshift({ role: 'system', content: systemPrompt });
        }
      } else {
        // Use completion endpoint
        endpoint = '/generate';
        requestParams = {
          model: params.options?.model || this.model,
          prompt: prompt,
          system: systemPrompt,
          stream: false
        };
      }
      
      // Add other parameters
      Object.assign(requestParams, {
        temperature: params.options?.temperature ?? this.defaultParams.temperature,
        max_tokens: params.options?.max_tokens ?? this.defaultParams.max_tokens,
        top_p: params.options?.top_p ?? this.defaultParams.top_p,
        top_k: params.options?.top_k ?? this.defaultParams.top_k,
      });
    } else if (this.type === 'localai') {
      // LocalAI uses OpenAI-compatible API
      endpoint = '/chat/completions';
      
      // Prepare messages
      let localAIMessages = messages.length > 0 ? [...messages] : [];
      
      // Add system message if provided
      if (systemPrompt && !localAIMessages.some(m => m.role === 'system')) {
        localAIMessages.unshift({ role: 'system', content: systemPrompt });
      }
      
      // Add user prompt if provided and no messages exist
      if (prompt && localAIMessages.length === 0) {
        localAIMessages.push({ role: 'user', content: prompt });
      }
      
      requestParams = {
        model: params.options?.model || this.model,
        messages: localAIMessages,
        temperature: params.options?.temperature ?? this.defaultParams.temperature,
        max_tokens: params.options?.max_tokens ?? this.defaultParams.max_tokens,
        top_p: params.options?.top_p ?? this.defaultParams.top_p,
        stream: false
      };
    } else {
      throw new Error(`Unsupported local LLM type: ${this.type}`);
    }

    // Use retry for robust error handling
    return retry(async (bail, attempt) => {
      try {
        logger.debug(`Local LLM attempt ${attempt}: ${this.type} / ${this.model}`);
        
        const startTime = Date.now();
        const response = await this.client.post(endpoint, requestParams);
        const endTime = Date.now();
        
        // Update stats
        this.lastUsed = new Date();
        this.requestCount = (this.requestCount || 0) + 1;
        this.successCount = (this.successCount || 0) + 1;
        this.averageLatency = this.averageLatency 
          ? (this.averageLatency * (this.successCount - 1) + (endTime - startTime)) / this.successCount
          : (endTime - startTime);
        
        // Format response based on provider type
        if (this.type === 'ollama') {
          if (endpoint === '/chat') {
            return {
              provider: this.name,
              model: this.model,
              content: response.data.message,
              text: response.data.message.content,
              usage: {
                prompt_tokens: response.data.prompt_eval_count || 0,
                completion_tokens: response.data.eval_count || 0,
                total_tokens: (response.data.prompt_eval_count || 0) + (response.data.eval_count || 0)
              },
              id: response.data.id || `local-${Date.now()}`,
              stopReason: response.data.done ? 'stop' : null,
              latency: endTime - startTime
            };
          } else {
            return {
              provider: this.name,
              model: this.model,
              content: { text: response.data.response },
              text: response.data.response,
              usage: {
                prompt_tokens: response.data.prompt_eval_count || 0,
                completion_tokens: response.data.eval_count || 0,
                total_tokens: (response.data.prompt_eval_count || 0) + (response.data.eval_count || 0)
              },
              id: response.data.id || `local-${Date.now()}`,
              stopReason: response.data.done ? 'stop' : null,
              latency: endTime - startTime
            };
          }
        } else if (this.type === 'localai') {
          return {
            provider: this.name,
            model: response.data.model || this.model,
            content: response.data.choices,
            text: response.data.choices[0].message.content,
            usage: response.data.usage || {
              prompt_tokens: 0,
              completion_tokens: 0,
              total_tokens: 0
            },
            id: response.data.id || `local-${Date.now()}`,
            stopReason: response.data.choices[0].finish_reason,
            latency: endTime - startTime
          };
        }
      } catch (error) {
        // Update error stats
        this.lastError = error;
        this.errorCount = (this.errorCount || 0) + 1;
        
        // Log the error
        logger.error(`Local LLM API error (attempt ${attempt}/${this.maxRetries}): ${error.message}`);
        
        // Handle different error types
        if (error.code === 'ECONNREFUSED' || error.code === 'ECONNRESET') {
          logger.warn('Local LLM server connection error, retrying');
          throw error; // This will trigger a retry
        } else if (error.response && error.response.status >= 500) {
          // Server error, we should retry
          logger.warn('Local LLM server error, retrying');
          throw error; // This will trigger a retry
        } else if (error.response && error.response.status === 400) {
          // Bad request - analyze the error message
          if (error.response.data && error.response.data.error && 
              error.response.data.error.includes('context length')) {
            logger.error('Local LLM context window exceeded');
            // We shouldn't retry token limit errors
            bail(new Error(`Local LLM context window exceeded: ${error.message}`));
            return;
          }
          // Other 400 errors might be retryable
          throw error;
        } else {
          // For unknown errors, retry a few times
          throw error;
        }
      }
    }, {
      retries: this.maxRetries,
      minTimeout: this.retryDelay,
      maxTimeout: this.retryDelay * 3,
      factor: 2,
      onRetry: (error, attempt) => {
        logger.warn(`Retrying Local LLM API call (${attempt}/${this.maxRetries}) after error: ${error.message}`);
      }
    });
  }

  /**
   * Check if the Local LLM provider is available
   * 
   * @returns {Promise<boolean>} - True if the provider is available
   */
  async isAvailable() {
    if (!this.isInitialized) {
      return false;
    }
    
    try {
      if (this.type === 'ollama') {
        const response = await this.client.get('/');
        return !!response.data;
      } else if (this.type === 'localai') {
        const response = await this.client.get('/models');
        return Array.isArray(response.data);
      }
      return false;
    } catch (error) {
      logger.error(`Local LLM availability check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Get the Local LLM provider's capabilities
   * 
   * @returns {Object} - Provider capabilities
   */
  getCapabilities() {
    const capabilities = {
      name: this.name,
      models: ['llama3', 'mistral', 'mixtral', 'phi3', 'gemma'],
      maxTokens: 2048,
      contextWindow: 8192,
      streaming: true,
      cost: 'free',
      strengths: ['privacy', 'customization', 'no-api-key'],
      weaknesses: ['setup-complexity', 'resource-intensive'],
      supportedModes: ['completion', 'chat'],
      type: this.type
    };
    
    return capabilities;
  }

  /**
   * Get the Local LLM provider's cost estimate for a request
   * 
   * @param {Object} params - Request parameters
   * @returns {Object} - Cost estimate
   */
  getCostEstimate(params) {
    // Local LLMs don't have API costs, but they do have computational costs
    // We'll estimate token counts for informational purposes
    
    // Simple token estimation
    const estimateTokens = (text) => {
      return Math.ceil(text.length / 4);
    };
    
    // Prepare messages for token counting
    let messages = params.messages || [];
    let prompt = params.prompt || '';
    let systemPrompt = params.systemPrompt || '';
    
    // Count tokens
    let inputTokens = 0;
    
    if (systemPrompt) {
      inputTokens += estimateTokens(systemPrompt);
    }
    
    if (prompt) {
      inputTokens += estimateTokens(prompt);
    }
    
    messages.forEach(msg => {
      const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
      inputTokens += estimateTokens(content);
    });
    
    // Estimate output tokens (default to 1/2 of max_tokens)
    const outputTokens = params.options?.max_tokens 
      ? Math.min(params.options.max_tokens, this.defaultParams.max_tokens) 
      : this.defaultParams.max_tokens / 2;
    
    return {
      provider: this.name,
      model: this.model,
      inputTokens,
      outputTokens,
      totalTokens: inputTokens + outputTokens,
      estimatedCost: 0, // Local LLMs don't have API costs
      currency: 'USD',
      breakdown: {
        computeResources: 'local',
        apiCost: 0
      }
    };
  }
}

module.exports = LocalLLMProvider;
