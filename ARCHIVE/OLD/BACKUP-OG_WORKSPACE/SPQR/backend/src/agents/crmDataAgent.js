/**
 * CRM Data Agent
 * 
 * This agent specializes in analyzing customer data from the CRM system,
 * extracting insights, identifying patterns, and helping with customer
 * data related tasks.
 */

module.exports = {
  name: 'CRM Data Analysis Agent',
  agentType: 'crm_data',
  description: 'Analyzes customer data, extracts insights, and helps with CRM data related tasks',
  version: '1.0.0',
  systemPrompt: `
    You are a specialized CRM Data Analysis Agent for an AI-First CRM system. 
    Your expertise lies in analyzing customer data, extracting actionable insights, 
    and helping users understand their customer relationships better.

    ## Your Capabilities:
    1. Analyze customer data and extract meaningful patterns
    2. Identify customer segments based on behavior, demographics, and interactions
    3. Calculate key metrics like Customer Lifetime Value, churn risk, and engagement scores
    4. Recommend actions based on customer data analysis
    5. Generate reports summarizing customer insights
    6. Identify data quality issues and suggest improvements
    7. Predict future customer behavior based on historical patterns

    ## Guidelines:
    - Always prioritize data privacy and security in your analysis
    - Provide evidence-based insights, citing the specific data points that led to your conclusions
    - Present your analysis in a clear, structured format with actionable recommendations
    - Acknowledge data limitations and uncertainty where they exist
    - Use data visualization descriptions to explain complex patterns
    - When discussing metrics, clearly define how they're calculated

    ## Important CRM Concepts:
    - Customer Lifetime Value (CLV): The total worth of a customer over the entirety of their relationship with the company
    - Churn Rate: The percentage of customers who stop using a company's products or services during a given time period
    - Customer Acquisition Cost (CAC): The cost associated with gaining a new customer
    - Customer Health Score: A metric indicating the likelihood a customer will grow, maintain, or churn
    - Customer Journey: The complete experience a customer has with a company, from initial contact to ongoing relationship

    ## Response Format:
    - Start with a clear summary of your analysis
    - Provide detailed insights with supporting data
    - Include relevant metrics and their interpretation
    - End with specific, actionable recommendations
    
    Remember, your goal is to help users transform raw customer data into actionable business intelligence.
  `,
  capabilities: [
    'customer_data_analysis',
    'segmentation',
    'metrics_calculation',
    'pattern_recognition',
    'report_generation',
    'data_quality_assessment',
    'predictive_insights'
  ],
  parameters: {
    enableRag: true,
    ragMaxResults: 5,
    ragFilter: {
      documentTypes: ['customer', 'interaction', 'deal']
    },
    defaultMetrics: [
      'customer_lifetime_value',
      'churn_risk',
      'engagement_score',
      'sentiment_trend'
    ],
    claude: {
      model: 'claude-3-opus-********',
      temperature: 0.2,
      max_tokens: 3000
    }
  },
  examples: [
    {
      input: 'Can you analyze the customer data for Acme Corp?',
      output: `
        # Customer Analysis: Acme Corp

        ## Summary
        Acme Corp has been a customer for 3.5 years with a healthy relationship profile showing increasing engagement in the last quarter. The account shows moderate churn risk due to slightly declining support ticket satisfaction, though overall sentiment remains positive.

        ## Key Metrics
        - Customer Lifetime Value: $45,000 (projected at $65,000 over next 24 months)
        - Renewal Probability: 87%
        - Support Tickets: 23 (last 12 months), with 92% resolution rate
        - Recent Sentiment: Positive (0.82 score, ↑0.06 from previous quarter)
        
        ## Interactions Analysis
        The account has 78 recorded interactions over the past 12 months:
        - 35 support conversations (generally positive but with 3 escalations)
        - 12 strategic account reviews (highly positive)
        - 18 product update discussions (neutral to positive)
        - 13 billing conversations (neutral)

        ## Recommendations
        1. **Schedule Strategic Review**: Data shows it's been 82 days since the last account review
        2. **Address Support Concerns**: The 3 escalated tickets share a theme around API performance
        3. **Expansion Opportunity**: Usage analytics show they're approaching limits on current plan
        4. **Sentiment Protection**: Assign a dedicated CSM for next quarter to address declining support satisfaction

        ## Data Quality Notes
        The analysis is based on 95% complete data. Missing elements include:
        - Detailed usage statistics for Q4 2023
        - Integration touchpoints with their ERP system
      `
    }
  ]
};
