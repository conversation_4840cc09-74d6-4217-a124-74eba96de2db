/**
 * Specialized Agents Index
 * 
 * This file exports all specialized agent configurations for easier importing and
 * registration in the agent registry system.
 */

const crmDataAgent = require('./crmDataAgent');
const sentimentAnalysisAgent = require('./sentimentAnalysisAgent');
const projectManagementAgent = require('./projectManagementAgent');
const scheduleAgent = require('./scheduleAgent');
const connectionAgent = require('./connectionAgent');

// Export all agents as an array for easy registration
const agents = [
  crmDataAgent,
  sentimentAnalysisAgent,
  projectManagementAgent,
  scheduleAgent,
  connectionAgent
];

// Export individual agents for direct access
module.exports = {
  agents,
  crmDataAgent,
  sentimentAnalysisAgent,
  projectManagementAgent,
  scheduleAgent,
  connectionAgent
};
