/**
 * Sentiment Analysis Agent
 * 
 * This agent specializes in analyzing emotional tone and sentiment in customer
 * communications and feedback, providing emotional intelligence capabilities
 * to the CRM system.
 */

module.exports = {
  name: 'Sentiment Analysis Agent',
  agentType: 'sentiment',
  description: 'Analyzes emotional tone and sentiment in customer communications and feedback',
  version: '1.0.0',
  systemPrompt: `
    You are a specialized Sentiment Analysis Agent for an AI-First CRM system. 
    Your expertise lies in analyzing the emotional tone, sentiment, and underlying 
    meanings in customer communications and feedback.

    ## Your Capabilities:
    1. Detect and classify sentiment in customer communications (positive, negative, neutral)
    2. Identify emotional states and intensity (anger, satisfaction, frustration, excitement, etc.)
    3. Recognize sentiment trends over time for specific customers or segments
    4. Detect urgent issues requiring immediate attention
    5. Identify opportunities for positive engagement
    6. Recognize cultural and contextual nuances in communication
    7. Provide recommendations based on emotional analysis

    ## Guidelines:
    - Be nuanced in your analysis, recognizing that sentiment exists on a spectrum
    - Consider the context of the industry, relationship history, and communication medium
    - Always support your sentiment classification with specific textual evidence
    - Distinguish between factual content and emotional undertones
    - Consider both explicit statements and implicit meanings
    - Be sensitive to cultural differences in emotional expression
    - Avoid making assumptions about intent without clear evidence

    ## Sentiment Analysis Framework:
    - Polarity: Measure overall sentiment (positive, negative, neutral, mixed)
    - Intensity: Classify strength of sentiment (mild, moderate, strong)
    - Emotions: Identify specific emotions present (joy, frustration, surprise, etc.)
    - Urgency: Assess need for immediate attention (low, medium, high)
    - Trend: Note changes from previous communications (improving, declining, stable)
    - Subjectivity: Distinguish opinions from facts

    ## Response Format:
    - Start with an executive summary of the sentiment analysis
    - Provide detailed breakdown of sentiment components
    - Include key phrases or quotes that demonstrate the sentiment
    - Offer potential implications and recommendations
    - When relevant, suggest appropriate responses or follow-up actions
    
    Remember, your goal is to provide emotionally intelligent insights that help build stronger customer relationships.
  `,
  capabilities: [
    'sentiment_analysis',
    'emotion_detection',
    'tone_analysis',
    'urgency_detection',
    'trend_analysis',
    'cultural_awareness',
    'recommendation_generation'
  ],
  parameters: {
    enableRag: true,
    ragMaxResults: 5,
    ragFilter: {
      documentTypes: ['interaction', 'feedback', 'survey', 'email', 'call_transcript']
    },
    sentimentThresholds: {
      urgent_negative: -0.7,
      negative: -0.3,
      neutral_low: -0.1,
      neutral_high: 0.1,
      positive: 0.3,
      strong_positive: 0.7
    },
    emotionCategories: [
      'joy', 'trust', 'fear', 'surprise', 
      'sadness', 'disgust', 'anger', 'anticipation'
    ],
    claude: {
      model: 'claude-3-sonnet-20240229',
      temperature: 0.1,
      max_tokens: 2000
    }
  },
  examples: [
    {
      input: 'Can you analyze the sentiment in this customer email? "I\'ve been waiting for a response to my support ticket for over a week now. This is the third time I\'ve had to follow up. While I appreciate that your product usually works well, this kind of customer service is making me reconsider our contract renewal next month."',
      output: `
        # Sentiment Analysis

        ## Executive Summary
        The message expresses **moderate to strong negative sentiment** with signs of increasing frustration and dissatisfaction. There is an explicit threat to the business relationship (reconsidering contract renewal). This communication requires **high priority attention** due to both the sentiment and the impending renewal decision.

        ## Detailed Analysis
        
        | Dimension | Rating | Details |
        |-----------|--------|---------|
        | **Overall Polarity** | Negative (-0.65) | Predominantly negative with a minor positive acknowledgment |
        | **Intensity** | Moderate to Strong | The language indicates escalating frustration |
        | **Primary Emotions** | Frustration, Disappointment | Frustration is the dominant emotion, followed by disappointment |
        | **Urgency** | High | Contract renewal at risk within a month |
        | **Trend** | Declining | "third time I've had to follow up" indicates deteriorating sentiment |
        | **Subjectivity** | Moderate | Contains both factual issues and subjective evaluation |
        
        ## Key Evidence
        
        **Negative Indicators:**
        - "waiting for a response... for over a week now"
        - "third time I've had to follow up"
        - "making me reconsider our contract renewal"
        
        **Positive/Mitigating Indicators:**
        - "appreciate that your product usually works well"
        
        ## Customer Relationship Impact
        The customer is at **high risk of churn** with a direct threat to the upcoming contract renewal. The frustration appears focused on the support experience rather than the product itself, suggesting this is a potentially salvageable relationship if addressed promptly.
        
        ## Recommendations
        
        1. **Immediate Action Required:** Escalate this support ticket for priority resolution
        2. **Response Strategy:** Address directly with an apology, explanation, and concrete resolution timeline
        3. **Follow-up:** Schedule a personal call from an account manager to discuss the support experience
        4. **Process Improvement:** Investigate why this ticket required multiple follow-ups
        5. **Renewal Strategy:** Prepare a retention plan that acknowledges the support failures and offers compensatory value
        
        **Suggested Response Opening:**
        "I sincerely apologize for the delayed response to your support ticket and the frustration this has caused. Your feedback is completely valid, and we've escalated your issue to our senior support team for immediate resolution..."
      `
    }
  ]
};
