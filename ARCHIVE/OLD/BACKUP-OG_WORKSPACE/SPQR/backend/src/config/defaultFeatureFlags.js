/**
 * Default Feature Flags Configuration
 * 
 * Provides initial feature flags to be loaded when the system is first initialized.
 * These defaults are used to bootstrap the feature flag system and can be modified
 * through the Mission Control Panel afterwards.
 */

const defaultFeatureFlags = [
  {
    key: 'new_dashboard',
    name: 'New Dashboard UI',
    description: 'Enables the new and improved dashboard user interface with enhanced visualizations and metrics.',
    category: 'ui',
    enabled: false,
    rules: {},
    rolloutPercentage: 0,
    startDate: null,
    endDate: null,
    dependencies: {},
    metricKeys: ['dashboard_load_time', 'dashboard_engagement'],
    tenantConfigurable: false
  },
  {
    key: 'advanced_analytics',
    name: 'Advanced Analytics',
    description: 'Enables advanced analytics features including predictive metrics and customizable reports.',
    category: 'analytics',
    enabled: false,
    rules: {
      tenantPlans: ['premium', 'enterprise']
    },
    rolloutPercentage: 100,
    startDate: null,
    endDate: null,
    dependencies: {},
    metricKeys: ['analytics_usage_time', 'report_exports'],
    tenantConfigurable: false
  },
  {
    key: 'ai_recommendations',
    name: 'AI-Powered Recommendations',
    description: 'Enables AI-powered recommendations for next-best-actions, content, and customer insights.',
    category: 'ai',
    enabled: true,
    rules: {},
    rolloutPercentage: 100,
    startDate: null,
    endDate: null,
    dependencies: {},
    metricKeys: ['recommendation_impressions', 'recommendation_clicks', 'recommendation_conversions'],
    tenantConfigurable: true
  },
  {
    key: 'multi_channel_campaigns',
    name: 'Multi-Channel Campaign Management',
    description: 'Enables creation and management of marketing campaigns across multiple channels from a single interface.',
    category: 'integrations',
    enabled: true,
    rules: {},
    rolloutPercentage: 100,
    startDate: null,
    endDate: null,
    dependencies: {},
    metricKeys: ['campaign_creations', 'channel_usage'],
    tenantConfigurable: true
  },
  {
    key: 'voice_transcription',
    name: 'Voice Call Transcription',
    description: 'Automatically transcribes phone calls and generates searchable records with sentiment analysis.',
    category: 'telephony',
    enabled: false,
    rules: {
      tenantPlans: ['enterprise']
    },
    rolloutPercentage: 0,
    startDate: null,
    endDate: null,
    dependencies: {},
    metricKeys: ['transcription_accuracy', 'transcription_usage'],
    tenantConfigurable: false
  },
  {
    key: 'automated_workflows',
    name: 'Automated Workflows',
    description: 'Enables creation of custom automated workflows using a visual editor with triggers and actions.',
    category: 'integrations',
    enabled: true,
    rules: {},
    rolloutPercentage: 100,
    startDate: null,
    endDate: null,
    dependencies: {},
    metricKeys: ['workflow_creations', 'workflow_executions', 'workflow_errors'],
    tenantConfigurable: true
  },
  {
    key: 'customer_segmentation',
    name: 'Advanced Customer Segmentation',
    description: 'Enables advanced customer segmentation with ML-based clustering and behavior analysis.',
    category: 'ai',
    enabled: false,
    rules: {},
    rolloutPercentage: 20, // Limited rollout
    startDate: null,
    endDate: null,
    dependencies: {
      'ai_recommendations': true
    },
    metricKeys: ['segment_creations', 'segment_applications'],
    tenantConfigurable: false
  },
  {
    key: 'dark_mode',
    name: 'Dark Mode',
    description: 'Enables dark mode theme across the application.',
    category: 'ui',
    enabled: true,
    rules: {},
    rolloutPercentage: 100,
    startDate: null,
    endDate: null,
    dependencies: {},
    metricKeys: ['theme_switches'],
    tenantConfigurable: true
  },
  {
    key: 'data_export_api',
    name: 'Data Export API',
    description: 'Enables API endpoints for exporting customer and sales data in various formats.',
    category: 'infrastructure',
    enabled: true,
    rules: {
      tenantPlans: ['standard', 'premium', 'enterprise']
    },
    rolloutPercentage: 100,
    startDate: null,
    endDate: null,
    dependencies: {},
    metricKeys: ['api_usage', 'export_volume'],
    tenantConfigurable: false
  },
  {
    key: 'admin_audit_logs',
    name: 'Admin Audit Logs',
    description: 'Enhanced audit logs for administrator actions with detailed filtering and export capabilities.',
    category: 'admin',
    enabled: true,
    rules: {
      userRoles: ['admin', 'system_admin']
    },
    rolloutPercentage: 100,
    startDate: null,
    endDate: null,
    dependencies: {},
    metricKeys: ['audit_log_views', 'audit_log_exports'],
    tenantConfigurable: false
  }
];

module.exports = defaultFeatureFlags;
