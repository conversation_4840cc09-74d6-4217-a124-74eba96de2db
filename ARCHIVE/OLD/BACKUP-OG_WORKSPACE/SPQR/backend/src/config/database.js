const { Pool } = require('pg');

// Konfiguration für die direkte PostgreSQL-Verbindung
const config = {
  url: process.env.DATABASE_URL || '********************************************/spqr',
  pool: null
};

// Erstelle einen Connection Pool
config.pool = new Pool({
  connectionString: config.url,
  max: 5,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000
});

// Füge pgvector-Erweiterung hinzu, wenn sie noch nicht existiert
const initPgVector = async () => {
  const client = await config.pool.connect();
  try {
    await client.query('CREATE EXTENSION IF NOT EXISTS vector');
    console.log('pgvector extension initialized');
  } catch (error) {
    console.error('Error initializing pgvector extension:', error);
  } finally {
    client.release();
  }
};

// Initialisiere pgvector, wenn die Datenbank gestartet wird
initPgVector().catch(console.error);

module.exports = config;
