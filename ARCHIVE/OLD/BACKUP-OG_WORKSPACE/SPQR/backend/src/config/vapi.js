/**
 * VAPI.ai Configuration
 * 
 * This file provides configuration for the VAPI.ai telephony integration.
 * It includes API keys, webhook endpoints, and other configuration settings.
 */

require('dotenv').config();

module.exports = {
  // API credentials
  apiKey: process.env.VAPI_API_KEY,
  
  // Base API URL
  baseUrl: process.env.VAPI_BASE_URL || 'https://api.vapi.ai',
  
  // Webhook settings
  webhookUrl: process.env.VAPI_WEBHOOK_URL || '/api/direct-webhooks/vapi',
  webhookSecret: process.env.VAPI_WEBHOOK_SECRET,
  
  // Call settings
  defaultVoiceId: process.env.VAPI_DEFAULT_VOICE_ID || 'echo', // Default VAPI voice ID
  transcriptionEnabled: process.env.VAPI_TRANSCRIPTION_ENABLED === 'true' || true,
  recordingEnabled: process.env.VAPI_RECORDING_ENABLED === 'true' || true,
  
  // AI settings
  maxTurnCount: parseInt(process.env.VAPI_MAX_TURN_COUNT || '30'),
  firstMessageDelay: parseInt(process.env.VAPI_FIRST_MESSAGE_DELAY || '500'),
  endCallAfterSilence: parseInt(process.env.VAPI_END_CALL_AFTER_SILENCE || '5000'),
  
  // Connection settings
  maxRetries: parseInt(process.env.VAPI_MAX_RETRIES || '3'),
  retryInterval: parseInt(process.env.VAPI_RETRY_INTERVAL || '1000'),
  
  // Two-stage call settings
  twoStageTimeout: parseInt(process.env.VAPI_TWO_STAGE_TIMEOUT || '30000'), // 30 seconds
  
  // Analysis settings
  insightExtractionEnabled: process.env.VAPI_INSIGHT_EXTRACTION_ENABLED === 'true' || true,
  sentimentAnalysisEnabled: process.env.VAPI_SENTIMENT_ANALYSIS_ENABLED === 'true' || true,
  
  // Debug mode
  debug: process.env.VAPI_DEBUG === 'true' || false
};
