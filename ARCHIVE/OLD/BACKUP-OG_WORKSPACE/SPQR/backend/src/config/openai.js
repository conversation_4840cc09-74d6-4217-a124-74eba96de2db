const { OpenAI } = require('openai/index.mjs');
require('dotenv').config();

// OpenAI-Konfiguration
const apiKey = process.env.OPENAI_API_KEY;
const embeddingModel = process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-ada-002';
const completionModel = process.env.OPENAI_COMPLETION_MODEL || 'gpt-4-turbo-preview';

if (!apiKey) {
  console.error('OpenAI API-Schlüssel fehlt. Bitte überprüfen Sie Ihre .env-Datei.');
  process.exit(1);
}

// OpenAI-Client erstellen
const openai = new OpenAI({
  apiKey: apiKey
});

module.exports = {
  openai,
  embeddingModel,
  completionModel
};
