/**
 * Claude AI Configuration
 * 
 * This file contains configuration settings for the Claude AI service.
 */

module.exports = {
  // API key from environment variables
  apiKey: process.env.CLAUDE_API_KEY,
  
  // Default model to use
  model: process.env.CLAUDE_MODEL || 'claude-3-opus-20240229',
  
  // API timeout in milliseconds
  apiTimeout: parseInt(process.env.CLAUDE_API_TIMEOUT || '60000', 10),
  
  // Maximum retry attempts for API calls
  maxRetryAttempts: parseInt(process.env.CLAUDE_MAX_RETRY_ATTEMPTS || '3', 10),
  
  // Delay between retries in milliseconds
  retryDelay: 1000,
  
  // Default system message if none provided
  defaultSystemMessage: 'You are <PERSON>, an AI assistant created by <PERSON>throp<PERSON> to be helpful, harmless, and honest.',
  
  // Default parameters for Claude API calls
  defaultParams: {
    temperature: 0.7,
    max_tokens: 4000,
    top_p: 0.95,
    top_k: 40
  }
};
