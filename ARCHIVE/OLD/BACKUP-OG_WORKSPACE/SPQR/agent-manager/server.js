const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { Pool } = require('pg');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const { OpenAI } = require('openai');

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();

// Middleware
app.use(helmet()); // Set security headers
app.use(cors()); // Enable CORS
app.use(express.json()); // JSON body parser
app.use(express.urlencoded({ extended: true })); // Process URL-encoded data

// Logging configuration
const logFormat = process.env.NODE_ENV === 'production' 
  ? 'combined' 
  : 'dev';

app.use(morgan(logFormat));

// Connect to PostgreSQL database
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Initialize LLM module
const llmModule = {
  async generate(params) {
    // Determine which provider to use based on model
    if (params.model.startsWith('claude-')) {
      return executeClaudeRequest(params);
    } else if (params.model.startsWith('gpt-')) {
      return executeOpenAIRequest(params);
    } else {
      throw new Error(`Unsupported model: ${params.model}`);
    }
  }
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'online', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    service: 'agent-manager',
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Agent registry endpoints

/**
 * Retrieve all available agents
 */
app.get('/api/agents', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT id, name, description, type, capabilities, status, created_at, updated_at FROM agents ORDER BY name'
    );
    
    res.json({
      success: true,
      data: result.rows
    });
  } catch (error) {
    console.error('Error retrieving agents:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Retrieve a specific agent by ID
 */
app.get('/api/agents/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await pool.query(
      'SELECT * FROM agents WHERE id = $1',
      [id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: `Agent not found: ${id}`
      });
    }
    
    res.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error(`Error retrieving agent ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Create a new agent
 */
app.post('/api/agents', async (req, res) => {
  try {
    const {
      name,
      description,
      type,
      model,
      capabilities,
      config
    } = req.body;
    
    // Validations
    if (!name || !type || !model) {
      return res.status(400).json({
        success: false,
        error: 'Name, type, and model are required'
      });
    }
    
    const id = uuidv4();
    
    await pool.query(
      'INSERT INTO agents (id, name, description, type, model, capabilities, config, status) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)',
      [id, name, description || '', type, model, capabilities || [], config || {}, 'active']
    );
    
    res.status(201).json({
      success: true,
      data: {
        id,
        name,
        description: description || '',
        type,
        model,
        capabilities: capabilities || [],
        config: config || {},
        status: 'active',
        created_at: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error creating agent:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Execute an agent
 */
app.post('/api/agents/:id/execute', async (req, res) => {
  try {
    const { id } = req.params;
    const { input, context } = req.body;
    
    // Retrieve agent from database
    const agentResult = await pool.query(
      'SELECT * FROM agents WHERE id = $1',
      [id]
    );
    
    if (agentResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: `Agent not found: ${id}`
      });
    }
    
    const agent = agentResult.rows[0];
    
    // Generate execution ID
    const executionId = uuidv4();
    
    // Log execution in database
    await pool.query(
      'INSERT INTO agent_executions (id, agent_id, input, context, status, started_at) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)',
      [executionId, id, input, context || {}, 'running']
    );
    
    // Execute agent based on model
    const model = agent.model;
    let result;
    
    if (model.startsWith('claude-')) {
      result = await executeClaudeAgent(agent, input, context);
    } else if (model.startsWith('gpt-')) {
      result = await executeOpenAIAgent(agent, input, context);
    } else {
      throw new Error(`Unsupported model: ${model}`);
    }
    
    // Store result in database
    await pool.query(
      'UPDATE agent_executions SET output = $1, status = $2, completed_at = CURRENT_TIMESTAMP WHERE id = $3',
      [result, 'completed', executionId]
    );
    
    res.json({
      success: true,
      data: {
        execution_id: executionId,
        agent_id: id,
        result
      }
    });
  } catch (error) {
    console.error(`Error executing agent ${req.params.id}:`, error);
    
    // Log error in database
    try {
      await pool.query(
        'UPDATE agent_executions SET status = $1, error = $2, completed_at = CURRENT_TIMESTAMP WHERE id = $3',
        ['failed', error.message, req.body.execution_id]
      );
    } catch (dbError) {
      console.error('Error logging failure:', dbError);
    }
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Execute a Claude agent
 */
async function executeClaudeAgent(agent, input, context) {
  try {
    // Prepare system prompt and user input
    const systemPrompt = agent.config.system_prompt || 
      "You are a helpful AI assistant. Answer the user's questions clearly and precisely.";
    
    // Use the LLM module to generate a response
    const response = await executeClaudeRequest({
      model: agent.model,
      systemPrompt: systemPrompt,
      messages: [
        {
          role: 'user',
          content: input
        }
      ],
      options: {
        max_tokens: 4000
      }
    });
    
    return response.text;
  } catch (error) {
    console.error('Error with Claude API request:', error);
    throw new Error(`Claude API error: ${error.message}`);
  }
}

/**
 * Execute a Claude request using Anthropic API
 */
async function executeClaudeRequest(params) {
  try {
    // Make a direct API call to Anthropic
    const response = await axios.post(
      'https://api.anthropic.com/v1/messages',
      {
        model: params.model,
        system: params.systemPrompt,
        messages: params.messages,
        max_tokens: params.options?.max_tokens || 4000
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.CLAUDE_API_KEY,
          'anthropic-version': '2023-06-01'
        }
      }
    );
    
    return {
      text: response.data.content[0].text,
      content: response.data.content,
      model: response.data.model,
      id: response.data.id
    };
  } catch (error) {
    console.error('Error with Claude API request:', error);
    throw new Error(`Claude API error: ${error.response?.data?.error?.message || error.message}`);
  }
}

/**
 * Execute an OpenAI agent
 */
async function executeOpenAIAgent(agent, input, context) {
  try {
    // Prepare system prompt and user input
    const systemPrompt = agent.config.system_prompt || 
      "You are a helpful AI assistant. Answer the user's questions clearly and precisely.";
    
    const response = await openai.chat.completions.create({
      model: agent.model,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: input
        }
      ],
      max_tokens: 4000
    });
    
    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error with OpenAI API request:', error);
    throw new Error(`OpenAI API error: ${error.message}`);
  }
}

/**
 * Execute an OpenAI request
 */
async function executeOpenAIRequest(params) {
  try {
    // Prepare messages
    const messages = [];
    
    // Add system message if provided
    if (params.systemPrompt) {
      messages.push({
        role: 'system',
        content: params.systemPrompt
      });
    }
    
    // Add user messages
    messages.push(...params.messages);
    
    const response = await openai.chat.completions.create({
      model: params.model,
      messages: messages,
      max_tokens: params.options?.max_tokens || 4000
    });
    
    return {
      text: response.choices[0].message.content,
      content: response.choices,
      model: response.model,
      id: response.id
    };
  } catch (error) {
    console.error('Error with OpenAI API request:', error);
    throw new Error(`OpenAI API error: ${error.message}`);
  }
}

// 404 handler for routes not found
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `Route not found: ${req.originalUrl}`
  });
});

// Global error handler
app.use((err, req, res, next) => {
  const statusCode = err.statusCode || 500;
  console.error(`[ERROR] ${err.message}`);
  console.error(err.stack);
  
  res.status(statusCode).json({
    success: false,
    message: err.message,
    stack: process.env.NODE_ENV === 'production' ? '🥞' : err.stack
  });
});

// Set server port
const PORT = process.env.PORT || 3201;

// Start server
app.listen(PORT, () => {
  console.log(`Agent Manager running on port ${PORT} (${process.env.NODE_ENV})`);
});

// Process error handling
process.on('uncaughtException', (error) => {
  console.error('Unhandled error:', error);
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Promise rejection:', reason);
});

process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down server...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down server...');
  process.exit(0);
});
