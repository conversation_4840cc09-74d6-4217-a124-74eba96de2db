# Builder Stage
FROM node:20-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .

# Runner Stage
FROM node:20-alpine AS runner
WORKDIR /app
RUN apk add --no-cache postgresql-client curl
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/server.js ./
# No src directory in agent-manager
EXPOSE 3201
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3201/health || exit 1
CMD ["npm", "start"]
