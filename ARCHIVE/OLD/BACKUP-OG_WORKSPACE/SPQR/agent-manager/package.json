{"name": "spqr-agent-manager", "version": "1.0.0", "description": "Agent Manager service for SPQR system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "lint": "eslint .", "test": "jest"}, "dependencies": {"@anthropic-ai/sdk": "^0.8.0", "axios": "^1.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "lodash": "^4.17.21", "morgan": "^1.10.0", "npm": "^11.2.0", "openai": "^4.24.1", "pg": "^8.11.3", "pgvector": "^0.2.0", "tiktoken": "^1.0.10", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}