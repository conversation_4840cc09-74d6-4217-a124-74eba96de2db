/**
 * E-Mail-Agent für SPQR
 *
 * Dieser Agent verwaltet E-Mail-Konten, liest E-Mails und sendet E-Mails.
 */

const nodemailer = require('nodemailer');
const Imap = require('imap');
const { simpleParser } = require('mailparser');
const Agent = require('../core/Agent');

class EmailAgent extends Agent {
  constructor(config) {
    super({
      name: 'email',
      capabilities: [
        'email_reading',
        'email_sending',
        'email_management',
        'email_filtering',
        'email_scheduling'
      ],
      config
    });

    this.providers = {};
    this.activeProvider = config.defaultProvider || 'google';
    this.checkInterval = config.checkInterval || 300000; // 5 Minuten
    this.autoReply = config.autoReply || false;
    this.signature = config.signature || '';
    this.intervalId = null;
  }

  /**
   * Initialisiert den E-Mail-Agent
   */
  async _performInitialization() {
    // Google Mail-Provider konfigurieren
    if (process.env.EMAIL_GOOGLE_USER && process.env.EMAIL_GOOGLE_PASSWORD) {
      this.providers.google = {
        smtp: nodemailer.createTransport({
          host: process.env.EMAIL_GOOGLE_HOST || 'smtp.gmail.com',
          port: parseInt(process.env.EMAIL_GOOGLE_PORT || '465'),
          secure: process.env.EMAIL_GOOGLE_SECURE === 'true',
          auth: {
            user: process.env.EMAIL_GOOGLE_USER,
            pass: process.env.EMAIL_GOOGLE_PASSWORD
          }
        }),
        imap: new Imap({
          user: process.env.EMAIL_GOOGLE_USER,
          password: process.env.EMAIL_GOOGLE_PASSWORD,
          host: process.env.EMAIL_GOOGLE_IMAP_HOST || 'imap.gmail.com',
          port: parseInt(process.env.EMAIL_GOOGLE_IMAP_PORT || '993'),
          tls: true,
          tlsOptions: { rejectUnauthorized: false }
        })
      };
      this.logger.info('Google Mail-Provider konfiguriert');
    }

    // Strato Mail-Provider konfigurieren
    if (process.env.EMAIL_STRATO_USER && process.env.EMAIL_STRATO_PASSWORD) {
      this.providers.strato = {
        smtp: nodemailer.createTransport({
          host: process.env.EMAIL_STRATO_HOST || 'smtp.strato.de',
          port: parseInt(process.env.EMAIL_STRATO_PORT || '465'),
          secure: process.env.EMAIL_STRATO_SECURE === 'true',
          auth: {
            user: process.env.EMAIL_STRATO_USER,
            pass: process.env.EMAIL_STRATO_PASSWORD
          }
        }),
        imap: new Imap({
          user: process.env.EMAIL_STRATO_USER,
          password: process.env.EMAIL_STRATO_PASSWORD,
          host: process.env.EMAIL_STRATO_IMAP_HOST || 'imap.strato.de',
          port: parseInt(process.env.EMAIL_STRATO_IMAP_PORT || '993'),
          tls: true,
          tlsOptions: { rejectUnauthorized: false }
        })
      };
      this.logger.info('Strato Mail-Provider konfiguriert');
    }

    // Generischen Mail-Provider konfigurieren
    if (process.env.EMAIL_GENERIC_USER && process.env.EMAIL_GENERIC_PASSWORD) {
      this.providers.generic = {
        smtp: nodemailer.createTransport({
          host: process.env.EMAIL_GENERIC_SMTP_HOST,
          port: parseInt(process.env.EMAIL_GENERIC_SMTP_PORT || '587'),
          secure: process.env.EMAIL_GENERIC_SMTP_SECURE === 'true',
          auth: {
            user: process.env.EMAIL_GENERIC_USER,
            pass: process.env.EMAIL_GENERIC_PASSWORD
          }
        }),
        imap: new Imap({
          user: process.env.EMAIL_GENERIC_USER,
          password: process.env.EMAIL_GENERIC_PASSWORD,
          host: process.env.EMAIL_GENERIC_IMAP_HOST,
          port: parseInt(process.env.EMAIL_GENERIC_IMAP_PORT || '993'),
          tls: process.env.EMAIL_GENERIC_IMAP_SECURE === 'true',
          tlsOptions: { rejectUnauthorized: false }
        })
      };
      this.logger.info('Generischer E-Mail-Provider konfiguriert');
    }

    // Aktiven Provider setzen
    this.activeProvider = process.env.EMAIL_DEFAULT_PROVIDER || 'google';
    if (!this.providers[this.activeProvider]) {
      const availableProviders = Object.keys(this.providers);
      if (availableProviders.length > 0) {
        this.activeProvider = availableProviders[0];
      } else {
        throw new Error('Kein E-Mail-Provider konfiguriert');
      }
    }

    // E-Mail-Überprüfung starten, wenn aktiviert
    if (this.checkInterval > 0) {
      this.startEmailChecking();
    }
  }

  /**
   * Startet die regelmäßige Überprüfung auf neue E-Mails
   */
  startEmailChecking() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(() => {
      this.checkEmails().catch(err => {
        this.logger.error('Fehler beim Überprüfen der E-Mails:', err);
      });
    }, this.checkInterval);

    this.logger.info(`E-Mail-Überprüfung gestartet (Intervall: ${this.checkInterval}ms)`);
  }

  /**
   * Stoppt die regelmäßige Überprüfung auf neue E-Mails
   */
  stopEmailChecking() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      this.logger.info('E-Mail-Überprüfung gestoppt');
    }
  }

  /**
   * Überprüft auf neue E-Mails
   */
  async checkEmails(provider = this.activeProvider) {
    if (!this.providers[provider]) {
      throw new Error(`Provider ${provider} nicht konfiguriert`);
    }

    return new Promise((resolve, reject) => {
      const imap = this.providers[provider].imap;
      const emails = [];

      imap.once('ready', () => {
        imap.openBox('INBOX', false, (err, box) => {
          if (err) return reject(err);

          // Suche nach ungelesenen E-Mails
          const searchCriteria = ['UNSEEN'];
          const fetchOptions = { bodies: ['HEADER.FIELDS (FROM TO SUBJECT DATE)', 'TEXT'], struct: true };

          imap.search(searchCriteria, (err, results) => {
            if (err) return reject(err);
            if (results.length === 0) return resolve([]);

            const f = imap.fetch(results, fetchOptions);
            f.on('message', (msg, seqno) => {
              const email = { seqno, body: '' };

              msg.on('body', (stream, info) => {
                let buffer = '';
                stream.on('data', (chunk) => {
                  buffer += chunk.toString('utf8');
                });
                stream.once('end', () => {
                  if (info.which.includes('HEADER')) {
                    const parsed = Imap.parseHeader(buffer);
                    email.from = parsed.from?.[0] || '';
                    email.to = parsed.to?.[0] || '';
                    email.subject = parsed.subject?.[0] || '';
                    email.date = parsed.date?.[0] || '';
                  } else {
                    email.body = buffer;
                  }
                });
              });

              msg.once('attributes', (attrs) => {
                email.uid = attrs.uid;
                email.flags = attrs.flags;
              });

              msg.once('end', () => {
                emails.push(email);
              });
            });

            f.once('error', (err) => {
              reject(err);
            });

            f.once('end', () => {
              imap.end();
            });
          });
        });
      });

      imap.once('error', (err) => {
        reject(err);
      });

      imap.once('end', () => {
        resolve(emails);
      });

      imap.connect();
    });
  }

  /**
   * Sendet eine E-Mail
   */
  async sendEmail(options, provider = this.activeProvider) {
    if (!this.providers[provider]) {
      throw new Error(`Provider ${provider} nicht konfiguriert`);
    }

    // Signatur hinzufügen, wenn vorhanden
    if (this.signature && options.text) {
      options.text += `\n\n${this.signature}`;
    }
    if (this.signature && options.html) {
      options.html += `<br><br>${this.signature.replace(/\n/g, '<br>')}`;
    }

    try {
      const result = await this.providers[provider].smtp.sendMail(options);
      this.logger.info(`E-Mail gesendet: ${result.messageId}`);
      return result;
    } catch (error) {
      this.logger.error('Fehler beim Senden der E-Mail:', error);
      throw error;
    }
  }

  /**
   * Erkennt automatisch die IMAP/SMTP-Einstellungen basierend auf der E-Mail-Adresse
   *
   * @param {string} email - E-Mail-Adresse
   * @returns {Object} - Erkannte Einstellungen
   */
  async detectEmailSettings(email) {
    try {
      if (!email || !email.includes('@')) {
        throw new Error('Ungültige E-Mail-Adresse');
      }

      const domain = email.split('@')[1].toLowerCase();

      // Bekannte Provider
      const knownProviders = {
        'gmail.com': {
          imap: { host: 'imap.gmail.com', port: 993, secure: true },
          smtp: { host: 'smtp.gmail.com', port: 465, secure: true },
          oauth: true,
          notes: 'Für Gmail wird die Verwendung von OAuth 2.0 oder App-Passwörtern empfohlen.'
        },
        'googlemail.com': {
          imap: { host: 'imap.gmail.com', port: 993, secure: true },
          smtp: { host: 'smtp.gmail.com', port: 465, secure: true },
          oauth: true,
          notes: 'Für Gmail wird die Verwendung von OAuth 2.0 oder App-Passwörtern empfohlen.'
        },
        'outlook.com': {
          imap: { host: 'outlook.office365.com', port: 993, secure: true },
          smtp: { host: 'smtp.office365.com', port: 587, secure: false },
          oauth: true,
          notes: 'Für Outlook wird die Verwendung von OAuth 2.0 empfohlen.'
        },
        'hotmail.com': {
          imap: { host: 'outlook.office365.com', port: 993, secure: true },
          smtp: { host: 'smtp.office365.com', port: 587, secure: false },
          oauth: true,
          notes: 'Für Hotmail wird die Verwendung von OAuth 2.0 empfohlen.'
        },
        'live.com': {
          imap: { host: 'outlook.office365.com', port: 993, secure: true },
          smtp: { host: 'smtp.office365.com', port: 587, secure: false },
          oauth: true,
          notes: 'Für Live.com wird die Verwendung von OAuth 2.0 empfohlen.'
        },
        'yahoo.com': {
          imap: { host: 'imap.mail.yahoo.com', port: 993, secure: true },
          smtp: { host: 'smtp.mail.yahoo.com', port: 465, secure: true },
          oauth: false,
          notes: 'Für Yahoo Mail müssen Sie App-Passwörter verwenden, wenn Sie die Zwei-Faktor-Authentifizierung aktiviert haben.'
        },
        'strato.de': {
          imap: { host: 'imap.strato.de', port: 993, secure: true },
          smtp: { host: 'smtp.strato.de', port: 465, secure: true },
          oauth: false,
          notes: 'Verwenden Sie Ihre normalen Strato-Zugangsdaten.'
        },
        'gmx.de': {
          imap: { host: 'imap.gmx.net', port: 993, secure: true },
          smtp: { host: 'mail.gmx.net', port: 587, secure: false },
          oauth: false,
          notes: 'Verwenden Sie Ihre normalen GMX-Zugangsdaten.'
        },
        'gmx.net': {
          imap: { host: 'imap.gmx.net', port: 993, secure: true },
          smtp: { host: 'mail.gmx.net', port: 587, secure: false },
          oauth: false,
          notes: 'Verwenden Sie Ihre normalen GMX-Zugangsdaten.'
        },
        'web.de': {
          imap: { host: 'imap.web.de', port: 993, secure: true },
          smtp: { host: 'smtp.web.de', port: 587, secure: false },
          oauth: false,
          notes: 'Verwenden Sie Ihre normalen WEB.DE-Zugangsdaten.'
        },
        'icloud.com': {
          imap: { host: 'imap.mail.me.com', port: 993, secure: true },
          smtp: { host: 'smtp.mail.me.com', port: 587, secure: false },
          oauth: false,
          notes: 'Für iCloud müssen Sie ein App-spezifisches Passwort generieren.'
        },
        'me.com': {
          imap: { host: 'imap.mail.me.com', port: 993, secure: true },
          smtp: { host: 'smtp.mail.me.com', port: 587, secure: false },
          oauth: false,
          notes: 'Für iCloud müssen Sie ein App-spezifisches Passwort generieren.'
        },
        'mac.com': {
          imap: { host: 'imap.mail.me.com', port: 993, secure: true },
          smtp: { host: 'smtp.mail.me.com', port: 587, secure: false },
          oauth: false,
          notes: 'Für iCloud müssen Sie ein App-spezifisches Passwort generieren.'
        },
        'zoho.com': {
          imap: { host: 'imap.zoho.com', port: 993, secure: true },
          smtp: { host: 'smtp.zoho.com', port: 465, secure: true },
          oauth: false,
          notes: 'Verwenden Sie Ihre normalen Zoho-Zugangsdaten.'
        },
        'protonmail.com': {
          imap: { host: 'imap.protonmail.ch', port: 993, secure: true },
          smtp: { host: 'smtp.protonmail.ch', port: 465, secure: true },
          oauth: false,
          notes: 'Für ProtonMail benötigen Sie die ProtonMail Bridge-Anwendung.'
        },
        'pm.me': {
          imap: { host: 'imap.protonmail.ch', port: 993, secure: true },
          smtp: { host: 'smtp.protonmail.ch', port: 465, secure: true },
          oauth: false,
          notes: 'Für ProtonMail benötigen Sie die ProtonMail Bridge-Anwendung.'
        }
      };

      if (knownProviders[domain]) {
        return {
          success: true,
          settings: knownProviders[domain],
          email,
          domain
        };
      }

      // Fallback zu Standard-Einstellungen
      return {
        success: true,
        settings: {
          imap: { host: `imap.${domain}`, port: 993, secure: true },
          smtp: { host: `smtp.${domain}`, port: 587, secure: false },
          oauth: false,
          notes: 'Automatisch erkannte Einstellungen. Bitte überprüfen Sie diese mit Ihrem E-Mail-Anbieter.'
        },
        email,
        domain,
        isGuess: true
      };
    } catch (error) {
      this.logger.error('Fehler bei der E-Mail-Einstellungserkennung:', error);
      return {
        success: false,
        message: error.message,
        email
      };
    }
  }

  /**
   * Testet die Verbindung zu einem E-Mail-Server
   *
   * @param {Object} config - Konfiguration für den Verbindungstest
   * @returns {Object} - Testergebnis
   */
  async testEmailConnection(config) {
    try {
      const results = {
        imap: { success: false, message: '' },
        smtp: { success: false, message: '' },
        overall: false
      };

      // IMAP-Verbindung testen
      try {
        const imap = new Imap({
          user: config.user,
          password: config.password,
          host: config.imapHost,
          port: parseInt(config.imapPort),
          tls: config.imapSecure,
          tlsOptions: { rejectUnauthorized: false },
          connTimeout: 10000, // 10 Sekunden Timeout
          authTimeout: 10000  // 10 Sekunden Timeout
        });

        await new Promise((resolve, reject) => {
          let timeout = setTimeout(() => {
            reject(new Error('Zeitüberschreitung bei der IMAP-Verbindung'));
          }, 15000);

          imap.once('ready', () => {
            clearTimeout(timeout);
            imap.end();
            resolve();
          });

          imap.once('error', (err) => {
            clearTimeout(timeout);
            reject(err);
          });

          imap.connect();
        });

        results.imap = { success: true, message: 'IMAP-Verbindung erfolgreich' };
      } catch (error) {
        results.imap = { success: false, message: `IMAP-Fehler: ${error.message}` };
      }

      // SMTP-Verbindung testen
      try {
        const transporter = nodemailer.createTransport({
          host: config.smtpHost,
          port: parseInt(config.smtpPort),
          secure: config.smtpSecure,
          auth: {
            user: config.user,
            pass: config.password
          },
          connectionTimeout: 10000, // 10 Sekunden Timeout
          greetingTimeout: 10000,   // 10 Sekunden Timeout
          socketTimeout: 10000      // 10 Sekunden Timeout
        });

        await transporter.verify();

        results.smtp = { success: true, message: 'SMTP-Verbindung erfolgreich' };
      } catch (error) {
        results.smtp = { success: false, message: `SMTP-Fehler: ${error.message}` };
      }

      // Gesamtergebnis
      results.overall = results.imap.success && results.smtp.success;

      return results;
    } catch (error) {
      this.logger.error('Fehler beim E-Mail-Verbindungstest:', error);
      return {
        imap: { success: false, message: 'Fehler beim Test' },
        smtp: { success: false, message: 'Fehler beim Test' },
        overall: false,
        error: error.message
      };
    }
  }

  /**
   * Verarbeitet eine Anfrage an den E-Mail-Agent
   */
  async processRequest(request, context = {}) {
    const { action, provider = this.activeProvider, ...params } = request;

    switch (action) {
      case 'send_email':
        return await this.sendEmail(params, provider);

      case 'check_emails':
        return await this.checkEmails(provider);

      case 'set_active_provider':
        if (!this.providers[params.provider]) {
          throw new Error(`Provider ${params.provider} nicht konfiguriert`);
        }
        this.activeProvider = params.provider;
        return { success: true, message: `Aktiver Provider auf ${params.provider} gesetzt` };

      case 'get_providers':
        return {
          providers: Object.keys(this.providers),
          activeProvider: this.activeProvider
        };

      case 'detect_email_settings':
        return await this.detectEmailSettings(params.email);

      case 'test_email_connection':
        return await this.testEmailConnection(params);

      default:
        throw new Error(`Unbekannte Aktion: ${action}`);
    }
  }

  /**
   * Bereinigt Ressourcen beim Herunterfahren
   */
  async shutdown() {
    this.stopEmailChecking();

    // IMAP-Verbindungen schließen
    for (const provider of Object.values(this.providers)) {
      if (provider.imap && provider.imap._state === 'connected') {
        provider.imap.end();
      }
    }

    this.logger.info('E-Mail-Agent heruntergefahren');
  }
}

module.exports = EmailAgent;
