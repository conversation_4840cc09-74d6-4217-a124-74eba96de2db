/**
 * Agent.js
 * 
 * Basis-Agent-<PERSON><PERSON><PERSON>, die auf Prompts und Events reagiert.
 */

const LLMClient = require('../core/LLMClient');

class Agent {
  constructor(name, config, brain) {
    this.name = name;
    this.config = config;
    this.brain = brain;
    this.capabilities = config.capabilities || [];
    this.promptTemplates = config.promptTemplates || {};
    this.llmClient = new LLMClient(config.llm);
  }
  
  async initialize() {
    console.log(`Agent ${this.name} wird initialisiert...`);
    
    // Event-Listener registrieren
    this.setupEventListeners();
    
    return this;
  }
  
  // Verarbeitet eine Anfrage
  async process(request, understanding, context) {
    console.log(`Agent ${this.name} verarbeitet Anfrage: "${request}"`);
    
    // Prüfen, ob Genehmigung erforderlich ist
    if (understanding.needsApproval && understanding.needsApproval.required) {
      const approvalResult = await this.requestApproval(request, understanding, context);
      
      if (!approvalResult.approved) {
        return {
          success: false,
          message: `Ich benötige eine Genehmigung für diese Aktion: ${understanding.needsApproval.reason}`,
          approvalStatus: approvalResult
        };
      }
    }
    
    // Prompt-Template auswählen
    const templateName = this.selectTemplate(understanding);
    const template = this.promptTemplates[templateName] || this.promptTemplates.default;
    
    if (!template) {
      return {
        success: false,
        message: "Ich konnte kein passendes Template für diese Anfrage finden."
      };
    }
    
    // Prompt generieren
    const prompt = this.fillTemplate(template, {
      request,
      understanding,
      context,
      capabilities: this.capabilities,
      name: this.name
    });
    
    // LLM-Antwort erhalten
    const response = await this.llmClient.complete({
      prompt,
      max_tokens: template.max_tokens || 1000,
      temperature: template.temperature || 0.2
    });
    
    // Antwort verarbeiten und Aktionen ausführen
    return this.processResponse(response, template, context);
  }
  
  // Wählt ein passendes Template aus
  selectTemplate(understanding) {
    // Basierend auf Intent und Entities das passende Template auswählen
    const intent = understanding.intent;
    
    // Spezifisches Template für den Intent suchen
    if (intent && this.promptTemplates[intent]) {
      return intent;
    }
    
    // Fallback auf Standard-Template
    return 'default';
  }
  
  // Füllt ein Template mit Daten
  fillTemplate(template, data) {
    let prompt = template.text;
    
    // Platzhalter ersetzen
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{{${key}}}`;
      if (typeof value === 'object') {
        prompt = prompt.replace(placeholder, JSON.stringify(value, null, 2));
      } else {
        prompt = prompt.replace(placeholder, value);
      }
    }
    
    return prompt;
  }
  
  // Verarbeitet die LLM-Antwort
  async processResponse(response, template, context) {
    // Antwort parsen und Aktionen extrahieren
    try {
      const parsedResponse = this.parseResponse(response, template.responseFormat);
      
      // Aktionen ausführen
      if (parsedResponse.actions && parsedResponse.actions.length > 0) {
        const actionResults = await this.executeActions(parsedResponse.actions, context);
        
        return {
          success: true,
          message: parsedResponse.message || "Aktionen erfolgreich ausgeführt",
          actions: actionResults
        };
      }
      
      // Einfache Textantwort
      return {
        success: true,
        message: parsedResponse.message || response
      };
    } catch (error) {
      console.error(`Fehler bei der Verarbeitung der Antwort:`, error);
      
      return {
        success: false,
        message: "Es ist ein Fehler bei der Verarbeitung aufgetreten.",
        error: error.message
      };
    }
  }
  
  // Parst die LLM-Antwort
  parseResponse(response, format) {
    if (format === 'json') {
      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        return jsonMatch ? JSON.parse(jsonMatch[0]) : { message: response };
      } catch (error) {
        throw new Error(`JSON-Parsing fehlgeschlagen: ${error.message}`);
      }
    }
    
    // Einfaches Text-Format
    return { message: response };
  }
  
  // Führt Aktionen aus
  async executeActions(actions, context) {
    const results = [];
    
    for (const action of actions) {
      try {
        console.log(`Führe Aktion aus: ${action.type}`);
        
        let result;
        
        switch (action.type) {
          case 'api_call':
            result = await this.executeApiCall(action.details, context);
            break;
          case 'database_query':
            result = await this.executeDatabaseQuery(action.details, context);
            break;
          case 'notification':
            result = await this.sendNotification(action.details, context);
            break;
          case 'create_task':
            result = await this.createTask(action.details, context);
            break;
          default:
            throw new Error(`Unbekannter Aktionstyp: ${action.type}`);
        }
        
        results.push({
          action,
          success: true,
          result
        });
      } catch (error) {
        console.error(`Fehler bei Aktion ${action.type}:`, error);
        
        results.push({
          action,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }
  
  // Fordert eine Genehmigung an
  async requestApproval(request, understanding, context) {
    console.log(`Agent ${this.name} fordert Genehmigung an für: "${request}"`);
    
    // Event für Genehmigungsanfrage auslösen
    return new Promise(resolve => {
      this.brain.eventBus.emit('approval_required', {
        agent: this.name,
        request,
        understanding,
        context,
        callback: (approvalResult) => {
          resolve(approvalResult);
        }
      });
    });
  }
  
  // Richtet Event-Listener ein
  setupEventListeners() {
    // Auf relevante Events reagieren
    // ...
  }
  
  // API-Aufruf ausführen
  async executeApiCall(details, context) {
    // In einer echten Implementierung würde hier ein API-Aufruf durchgeführt werden
    return {
      success: true,
      message: `API-Aufruf simuliert: ${details.service}/${details.endpoint}`
    };
  }
  
  // Datenbankabfrage ausführen
  async executeDatabaseQuery(details, context) {
    // In einer echten Implementierung würde hier eine Datenbankabfrage durchgeführt werden
    return {
      success: true,
      message: `Datenbankabfrage simuliert: ${details.collection}/${details.operation}`
    };
  }
  
  // Benachrichtigung senden
  async sendNotification(details, context) {
    // In einer echten Implementierung würde hier eine Benachrichtigung gesendet werden
    return {
      success: true,
      message: `Benachrichtigung simuliert an: ${details.recipient}`
    };
  }
  
  // Aufgabe erstellen
  async createTask(details, context) {
    // In einer echten Implementierung würde hier eine Aufgabe erstellt werden
    return {
      success: true,
      message: `Aufgabe simuliert: ${details.title}`
    };
  }
}

// Spezialisierte Agent-Klassen
class CommunicationAgent extends Agent {
  // Spezialisierte Methoden für Kommunikation
  async sendEmail(details, context) {
    // E-Mail senden
    // ...
  }
  
  async makeCall(details, context) {
    // Anruf tätigen
    // ...
  }
}

class TaskAgent extends Agent {
  // Spezialisierte Methoden für Aufgabenverwaltung
  async createTask(details, context) {
    // Aufgabe erstellen
    // ...
  }
  
  async updateTask(details, context) {
    // Aufgabe aktualisieren
    // ...
  }
}

class AutomationAgent extends Agent {
  // Spezialisierte Methoden für Automatisierung
  async createAutomation(details, context) {
    // Automatisierung erstellen
    // ...
  }
  
  async executeProcess(details, context) {
    // Prozess ausführen
    // ...
  }
}

class ResearchAgent extends Agent {
  // Spezialisierte Methoden für Recherche
  async searchInformation(query, context) {
    // Informationen suchen
    // ...
  }
  
  async analyzeData(data, context) {
    // Daten analysieren
    // ...
  }
}

module.exports = {
  Agent,
  CommunicationAgent,
  TaskAgent,
  AutomationAgent,
  ResearchAgent
};
